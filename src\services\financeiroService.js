import axios from '@/services/axios';

export const financeiroService = {
  /**
   * Obter todas as faturas com filtros
   */
  async getFaturas(filters = {}) {
    const params = new URLSearchParams();
    
    if (filters.paciente_id) params.append('paciente_id', filters.paciente_id);
    if (filters.status) params.append('status', filters.status);
    if (filters.descricao) params.append('descricao', filters.descricao);
    if (filters.data_inicio) params.append('data_inicio', filters.data_inicio);
    if (filters.data_fim) params.append('data_fim', filters.data_fim);
    if (filters.vencidas) params.append('vencidas', filters.vencidas);
    if (filters.page) params.append('page', filters.page);

    return axios.get(`/faturas?${params.toString()}`);
  },

  /**
   * Obter fatura específica
   */
  async getFatura(id) {
    return axios.get(`/faturas/${id}`);
  },

  /**
   * Criar nova fatura
   */
  async createFatura(data) {
    return axios.post('/faturas', data);
  },

  /**
   * Atualizar fatura
   */
  async updateFatura(id, data) {
    return axios.put(`/faturas/${id}`, data);
  },

  /**
   * Cancelar fatura
   */
  async deleteFatura(id) {
    return axios.delete(`/faturas/${id}`);
  },

  /**
   * Marcar fatura como paga
   */
  async markAsPaid(id, paymentData = {}) {
    return axios.post(`/faturas/${id}/marcar-como-pago`, paymentData);
  },

  /**
   * Obter faturas de um paciente específico
   */
  async getFaturasPaciente(pacienteId) {
    return axios.get(`/faturas/paciente/${pacienteId}`);
  },

  /**
   * Obter estatísticas financeiras
   */
  async getEstatisticas(filters = {}) {
    const params = new URLSearchParams();

    if (filters.paciente_id) params.append('paciente_id', filters.paciente_id);
    if (filters.data_inicio) params.append('data_inicio', filters.data_inicio);
    if (filters.data_fim) params.append('data_fim', filters.data_fim);

    return axios.get(`/faturas/estatisticas?${params.toString()}`);
  },

  /**
   * Obter dados para dashboard
   */
  async getDashboardData(period = 'month') {
    return axios.get(`/faturas/dashboard?period=${period}`);
  },

  /**
   * Gerar relatório
   */
  async generateReport(type, filters = {}) {
    const params = new URLSearchParams();
    params.append('type', type);

    if (filters.data_inicio) params.append('data_inicio', filters.data_inicio);
    if (filters.data_fim) params.append('data_fim', filters.data_fim);
    if (filters.paciente_id) params.append('paciente_id', filters.paciente_id);

    return axios.get(`/faturas/relatorio?${params.toString()}`);
  },

  /**
   * Exportar dados
   */
  async exportData(format = 'excel', filters = {}) {
    const params = new URLSearchParams();
    params.append('format', format);

    if (filters.data_inicio) params.append('data_inicio', filters.data_inicio);
    if (filters.data_fim) params.append('data_fim', filters.data_fim);
    if (filters.status) params.append('status', filters.status);

    return axios.get(`/faturas/export?${params.toString()}`, {
      responseType: 'blob'
    });
  },

  /**
   * Validar dados de fatura
   */
  validateFaturaData(data) {
    const errors = {};

    if (!data.paciente_id) {
      errors.paciente_id = 'Paciente é obrigatório';
    }

    if (!data.descricao || data.descricao.trim() === '') {
      errors.descricao = 'Descrição é obrigatória';
    }

    if (!data.valor_nominal || data.valor_nominal <= 0) {
      errors.valor_nominal = 'Valor deve ser maior que zero';
    }

    if (!data.data_vencimento) {
      errors.data_vencimento = 'Data de vencimento é obrigatória';
    }

    if (data.parcelas_total && (data.parcelas_total < 1 || data.parcelas_total > 60)) {
      errors.parcelas_total = 'Número de parcelas deve estar entre 1 e 60';
    }

    if (data.percentual_desconto && (data.percentual_desconto < 0 || data.percentual_desconto > 100)) {
      errors.percentual_desconto = 'Percentual de desconto deve estar entre 0 e 100';
    }

    if (data.percentual_acrescimo && (data.percentual_acrescimo < 0 || data.percentual_acrescimo > 100)) {
      errors.percentual_acrescimo = 'Percentual de acréscimo deve estar entre 0 e 100';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },

  /**
   * Calcular valor final da fatura
   */
  calculateFinalValue(valorNominal, percentualDesconto = 0, valorDesconto = 0, percentualAcrescimo = 0, valorAcrescimo = 0) {
    let valor = parseFloat(valorNominal) || 0;
    
    // Aplicar desconto
    if (valorDesconto > 0) {
      valor -= parseFloat(valorDesconto);
    } else if (percentualDesconto > 0) {
      valor -= (valor * parseFloat(percentualDesconto)) / 100;
    }
    
    // Aplicar acréscimo
    if (valorAcrescimo > 0) {
      valor += parseFloat(valorAcrescimo);
    } else if (percentualAcrescimo > 0) {
      valor += (valor * parseFloat(percentualAcrescimo)) / 100;
    }
    
    return Math.max(0, valor);
  },

  /**
   * Formatar valor monetário
   */
  formatCurrency(value) {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0);
  },

  /**
   * Formatar data
   */
  formatDate(date) {
    if (!date) return '';
    return new Date(date).toLocaleDateString('pt-BR');
  },

  /**
   * Obter status badge class
   */
  getStatusBadgeClass(status) {
    const statusClasses = {
      'pendente': 'badge-warning',
      'pago': 'badge-success',
      'vencido': 'badge-danger',
      'cancelado': 'badge-secondary'
    };
    
    return statusClasses[status] || 'badge-secondary';
  },

  /**
   * Obter texto do status
   */
  getStatusText(status) {
    const statusTexts = {
      'pendente': 'Pendente',
      'pago': 'Pago',
      'vencido': 'Vencido',
      'cancelado': 'Cancelado'
    };
    
    return statusTexts[status] || 'Desconhecido';
  }
};

export default financeiroService;
