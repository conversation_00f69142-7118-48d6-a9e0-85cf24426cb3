<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('title'); // Título da notificação
            $table->text('message'); // Mensagem da notificação
            $table->string('type')->default('info'); // Tipo: info, success, warning, error
            $table->unsignedBigInteger('user_id'); // Usuário que receberá a notificação
            $table->boolean('read')->default(false); // Se foi lida ou não
            $table->timestamp('read_at')->nullable(); // Quando foi lida
            $table->json('data')->nullable(); // Dados adicionais (opcional)
            $table->string('action_url')->nullable(); // URL para ação (opcional)
            $table->timestamps();

            // Índices para performance
            $table->index(['user_id', 'read']);
            $table->index(['user_id', 'created_at']);

            // Chave estrangeira
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
