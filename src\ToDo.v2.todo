☐ V2/V3?: Fazer dashboards analíticos de marketing
Formulário:
    ☐ Adicionar informação (única/múltipla escolha)
    ou
    ☐ Permitir? Adicionar informação (única/múltipla escolha)
    ☐ Permitir alterar respostas do questionário após respondido
Status do progresso:
    ☐ Colocar status do progresso:
        ☐ Vermelho - após atingir a data
Agenda de consultas:
    ☐ Fazer funcionar impressão
    Fazer funcionar botão de atestado:
        ☐ Criar modal de Novo Atestado
        ☐ No modal, exibir as últimas X consultas (em formato de cartão or something like that), para facilitar selecionar o paciente, ou então escolhe pesquisando mesmo...
        ☐ Gerar atestado em PDF
Paciente > Perfil:
    ☐ Adicionar campo "o paciente é o responsável"
Tratamento recomendado:
    □ Adicionar botões para preencher automaticamente a aparatologia de acordo com o tratamento recomendado
Dentistas:
    ☐ Criar modal:
        ☐ Criar listagem de clínicas
        ☐ Criar opção para adicionar
        ☐ Criar opção para excluir
        ☐ Quando clicar na clínica, exibir info dela:
            ☐ Nome
            ☐ Data de cadastro
            ☐ Total de dentistas
            ☐ Total de pacientes
            ☐ Listagem de dentistas
Fazer modais globais:
    ☐ Fazer modal global de buscar clínica
    ☐ Fazer modal global de buscar paciente
    ☐ Fazer modal global de buscar ortodontista

Paciente > Perfil:
    ☐ Permitir editar meios de contato
Layout:
    Melhorias na responsividade:
        Paciente > Perfil:
            ☐ Pra telas pequenas (s), fazer tabs das seções (informações pessoais, endereço, ficha)
        ☐ Passar botões das abas para baixo do nome do paciente, quando a tela for pequena
        ☐ Verificar outras melhorias
    ☐ Implementar botões de CRUD em todas as entidades (revisar)
    ☐ Corrigir: padding está comendo em baixo, algum bug com a altura geral da página
    ☐ Corrigir: botão de fechar modal está oculto/transparente
    ☐ Corrigir: A sidenav não está chegando até o final da tela
    ☐ Corrigir: ajustar largura da tela do Tratamento
    ☐ Melhorar layout mobile
☐ Ao pesquisar o paciente, parece que precisará dar um delay entre o pressionar das teclas para esperar concluir a palavra
Tela de Plano de tratamento:
    ☐ Gerar as metas terapêuticas do diagnóstico automático realizado (mas ainda possibilitando adicionar novas, manualmente)
    Seção "Necessidade de encaminhamentos":
        ☐ Gerar automaticamente a partir da análise
        ☐ Exibir de qual item foi gerada a necessidade de encaminhamento
