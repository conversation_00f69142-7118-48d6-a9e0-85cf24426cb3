<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Mentoria extends Model
{
    use HasFactory, Notifiable, LogsActivity;

    protected $table = 'mentorias';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'solicitante_id',
        'mentor_id',
        'paciente_id',
        'observacao',
        'status',
        'iniciada_em',
        'finalizada_em',
        'cancelada_em',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'iniciada_em' => 'datetime',
            'finalizada_em' => 'datetime',
            'cancelada_em' => 'datetime',
        ];
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*']);
    }

    // Add a relationship to the FormularioBoasVindas model
    public function paciente()
    {
        return $this->belongsTo(Paciente::class, 'paciente_id');
    }
    public function solicitante()
    {
        return $this->belongsTo(Dentista::class, 'solicitante_id');
    }
    public function mentor()
    {
        return $this->belongsTo(Dentista::class, 'mentor_id');
    }

    public function mensagens()
    {
        return $this->hasMany(MentoriaMensagem::class, 'mentoria_id')->orderBy('created_at', 'asc');
    }

    public function mensagensNaoLidas()
    {
        return $this->hasMany(MentoriaMensagem::class, 'mentoria_id')->where('lida', false);
    }
}