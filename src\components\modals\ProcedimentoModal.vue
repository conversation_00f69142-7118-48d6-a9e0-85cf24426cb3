<template>
  <div class="modal fade" id="procedimentoModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content elegant-modal">
        <div class="modal-header elegant-header">
          <h5 class="modal-title">
            <i class="fas fa-list-alt me-2"></i>
            {{ isEditing ? 'Editar Item' : 'Novo Item' }}
          </h5>
          <button type="button" class="btn-close elegant-close" data-bs-dismiss="modal"></button>
        </div>

        <form @submit.prevent="salvar">
          <div class="modal-body elegant-body">
            <div class="row g-3">
              <!-- Linha 1: Tipo (primeiro) -->
              <div class="col-12">
                <label class="elegant-label">Tipo *</label>
                <div class="tipo-selector">
                  <div
                    class="tipo-option"
                    :class="{ active: form.tipo === 'procedimento' }"
                    @click="form.tipo = 'procedimento'"
                  >
                    <i class="fas fa-user-md"></i>
                    <span>Procedimento</span>
                  </div>
                  <div
                    class="tipo-option"
                    :class="{ active: form.tipo === 'produto' }"
                    @click="form.tipo = 'produto'"
                  >
                    <i class="fas fa-box"></i>
                    <span>Produto</span>
                  </div>
                  <div
                    class="tipo-option"
                    :class="{ active: form.tipo === 'servico' }"
                    @click="form.tipo = 'servico'"
                  >
                    <i class="fas fa-handshake"></i>
                    <span>Serviço</span>
                  </div>
                </div>
                <div v-if="errors.tipo" class="invalid-feedback d-block">{{ errors.tipo }}</div>
              </div>

              <!-- Linha 2: Nome e Código -->
              <div class="col-8">
                <label class="elegant-label">Nome *</label>
                <input
                  type="text"
                  class="elegant-input"
                  v-model="form.nome"
                  :class="{ 'is-invalid': errors.nome }"
                  :placeholder="getNomePlaceholder()"
                  maxlength="255"
                  required
                >
                <div v-if="errors.nome" class="invalid-feedback">{{ errors.nome }}</div>
              </div>

              <div class="col-4">
                <label class="elegant-label">Código</label>
                <input
                  type="text"
                  class="elegant-input"
                  v-model="form.codigo"
                  :class="{ 'is-invalid': errors.codigo }"
                  placeholder="Ex: PROC001"
                  maxlength="50"
                >
                <div v-if="errors.codigo" class="invalid-feedback">{{ errors.codigo }}</div>
              </div>

              <!-- Linha 3: Valores (elegantes) -->
              <div class="col-4">
                <label class="elegant-label">Valor Base *</label>
                <div class="elegant-input-group" :class="{ 'is-invalid': errors.valor_base }">
                  <span class="elegant-addon elegant-addon-left">R$</span>
                  <input
                    type="text"
                    class="elegant-input money-input text-center"
                    :value="formatCurrencyInput(form.valor_base)"
                    @input="updateValorBase($event.target.value, $event)"
                    placeholder="0,00"
                    maxlength="15"
                    required
                  >
                </div>
                <div v-if="errors.valor_base" class="invalid-feedback">{{ errors.valor_base }}</div>
              </div>

              <div class="col-4">
                <label class="elegant-label">Valor Mín.</label>
                <div class="elegant-input-group" :class="{ 'is-invalid': errors.valor_minimo }">
                  <span class="elegant-addon elegant-addon-left">R$</span>
                  <input
                    type="text"
                    class="elegant-input money-input text-center"
                    :value="formatCurrencyInput(form.valor_minimo)"
                    @input="updateValorMinimo($event.target.value, $event)"
                    placeholder="0,00"
                    maxlength="15"
                  >
                </div>
                <div v-if="errors.valor_minimo" class="invalid-feedback">{{ errors.valor_minimo }}</div>
              </div>

              <div class="col-4">
                <label class="elegant-label">Valor Máx.</label>
                <div class="elegant-input-group" :class="{ 'is-invalid': errors.valor_maximo }">
                  <span class="elegant-addon elegant-addon-left">R$</span>
                  <input
                    type="text"
                    class="elegant-input money-input text-center"
                    :value="formatCurrencyInput(form.valor_maximo)"
                    @input="updateValorMaximo($event.target.value, $event)"
                    placeholder="0,00"
                    maxlength="15"
                  >
                </div>
                <div v-if="errors.valor_maximo" class="invalid-feedback">{{ errors.valor_maximo }}</div>
              </div>



              <!-- Linha 5: Ortodontista e Tempo Estimado -->
              <div class="col-6">
                <label class="elegant-label">Ortodontista</label>
                <div class="elegant-select-wrapper">
                  <select
                    class="elegant-select elegant-select-custom"
                    v-model="form.dentista_id"
                    :class="{ 'is-invalid': errors.dentista_id }"
                  >
                    <option value="">Todos</option>
                    <option v-for="dentista in dentistas" :key="dentista.id" :value="dentista.id">
                      {{ dentista.nome }}
                    </option>
                  </select>
                  <i class="fas fa-chevron-down elegant-select-icon"></i>
                </div>
                <div v-if="errors.dentista_id" class="invalid-feedback">{{ errors.dentista_id }}</div>
              </div>

              <div class="col-6">
                <label class="elegant-label">Tempo estimado ({{ getUnidadeLabel(form.unidade_tempo) }})</label>
                <div class="row g-2">
                  <div class="col-6">
                    <input
                      type="number"
                      class="elegant-input"
                      v-model.number="form.tempo_estimado"
                      :class="{ 'is-invalid': errors.tempo_estimado }"
                      placeholder="Ex: 60"
                      min="1"
                    >
                  </div>
                  <div class="col-6">
                    <div class="elegant-select-wrapper">
                      <select
                        class="elegant-select elegant-select-custom"
                        v-model="form.unidade_tempo"
                        :class="{ 'is-invalid': errors.unidade_tempo }"
                      >
                        <option value="min">Minutos</option>
                        <option value="h">Horas</option>
                        <option value="sessao">Sessões</option>
                      </select>
                      <i class="fas fa-chevron-down elegant-select-icon"></i>
                    </div>
                  </div>
                </div>
                <div v-if="errors.tempo_estimado" class="invalid-feedback">{{ errors.tempo_estimado }}</div>
              </div>

              <!-- Linha 6: Descrição -->
              <div class="col-12" style="margin-bottom: 0;">
                <label class="elegant-label">Descrição</label>
                <textarea
                  class="elegant-textarea"
                  v-model="form.descricao"
                  :class="{ 'is-invalid': errors.descricao }"
                  :placeholder="getDescricaoPlaceholder()"
                  rows="2"
                ></textarea>
                <div v-if="errors.descricao" class="invalid-feedback">{{ errors.descricao }}</div>
              </div>
            </div>
          </div>

          <div class="modal-footer elegant-footer">
            <button type="button" class="btn btn-light elegant-btn-cancel" data-bs-dismiss="modal">
              <i class="fas fa-times me-1"></i>
              Cancelar
            </button>
            <button type="submit" class="btn btn-primary elegant-btn-save" :disabled="loading">
              <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
              <i v-else class="fas fa-save me-1"></i>
              {{ loading ? 'Salvando...' : 'Salvar' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { servicoProdutoService } from '@/services/servicoProdutoService';
import { openModal, closeModal } from '@/utils/modalHelper';
import { getDentistas } from '@/services/dentistasService';
import cSwal from '@/utils/cSwal';

export default {
  name: 'ProcedimentoModal',
  data() {
    return {
      isEditing: false,
      loading: false,
      procedimentoId: null,
      dentistas: [],
      form: {
        codigo: '',
        nome: '',
        descricao: '',
        tipo: 'procedimento',
        valor_base: 0,
        valor_minimo: null,
        valor_maximo: null,
        dentista_id: '',
        unidade_tempo: 'min',
        tempo_estimado: null
      },
      errors: {}
    };
  },
  methods: {
    async open(procedimento = null) {
      this.resetForm();
      this.errors = {};

      // Carregar dentistas
      await this.carregarDentistas();

      if (procedimento) {
        this.isEditing = true;
        this.procedimentoId = procedimento.id;
        this.form = {
          codigo: procedimento.codigo || '',
          nome: procedimento.nome || '',
          descricao: procedimento.descricao || '',
          tipo: procedimento.tipo || 'procedimento',
          valor_base: procedimento.valor_base || 0,
          valor_minimo: procedimento.valor_minimo || null,
          valor_maximo: procedimento.valor_maximo || null,
          dentista_id: procedimento.dentista_id || '',
          unidade_tempo: procedimento.unidade_tempo || 'min',
          tempo_estimado: procedimento.tempo_estimado || null
        };
      } else {
        this.isEditing = false;
        this.procedimentoId = null;
      }

      openModal('procedimentoModal');
    },

    async carregarDentistas() {
      try {
        const response = await getDentistas();
        if (response) {
          this.dentistas = response;
        }
      } catch (error) {
        console.error('Erro ao carregar dentistas:', error);
      }
    },

    resetForm() {
      this.form = {
        codigo: '',
        nome: '',
        descricao: '',
        tipo: 'procedimento',
        valor_base: 0,
        valor_minimo: null,
        valor_maximo: null,
        dentista_id: '',
        unidade_tempo: 'min',
        tempo_estimado: null
      };
    },

    validateForm() {
      this.errors = {};
      let isValid = true;

      if (!this.form.nome || this.form.nome.trim() === '') {
        this.errors.nome = 'O nome é obrigatório';
        isValid = false;
      }

      if (!this.form.tipo) {
        this.errors.tipo = 'O tipo é obrigatório';
        isValid = false;
      }

      if (!this.form.valor_base || this.form.valor_base <= 0) {
        this.errors.valor_base = 'O valor base deve ser maior que zero';
        isValid = false;
      }

      if (this.form.valor_minimo && this.form.valor_maximo) {
        if (this.form.valor_minimo > this.form.valor_maximo) {
          this.errors.valor_minimo = 'O valor mínimo não pode ser maior que o máximo';
          isValid = false;
        }
      }

      if (this.form.valor_minimo && this.form.valor_minimo > this.form.valor_base) {
        this.errors.valor_minimo = 'O valor mínimo não pode ser maior que o valor base';
        isValid = false;
      }

      if (this.form.valor_maximo && this.form.valor_maximo < this.form.valor_base) {
        this.errors.valor_maximo = 'O valor máximo não pode ser menor que o valor base';
        isValid = false;
      }

      return isValid;
    },

    // Métodos de formatação de moeda
    formatCurrency(value) {
      if (!value || value === 0) return 'R$ 0,00';
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(value);
    },

    formatCurrencyInput(value) {
      if (!value || value === 0) return '';
      return this.formatCurrency(value).replace('R$ ', '');
    },

    formatMoneyMask(value) {
      if (!value) return '';

      // Remove tudo que não é dígito
      const digits = value.replace(/\D/g, '');
      if (!digits) return '';

      // Converte para centavos (últimos 2 dígitos são centavos)
      const cents = parseInt(digits);
      const reais = cents / 100;

      // Formata como moeda brasileira
      return reais.toLocaleString('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    parseCurrencyInput(value) {
      if (!value) return 0;
      // Remove formatação e converte para número
      const cleanValue = value.replace(/[^\d,]/g, '').replace(',', '.');
      return parseFloat(cleanValue) || 0;
    },

    updateValorBase(value, event) {
      const maskedValue = this.formatMoneyMask(value);
      if (event && event.target) {
        this.$nextTick(() => {
          event.target.value = maskedValue;
        });
      }
      const numValue = this.parseCurrencyInput(maskedValue);
      this.form.valor_base = numValue;
    },

    updateValorMinimo(value, event) {
      const maskedValue = this.formatMoneyMask(value);
      if (event && event.target) {
        this.$nextTick(() => {
          event.target.value = maskedValue;
        });
      }
      const numValue = this.parseCurrencyInput(maskedValue);
      this.form.valor_minimo = numValue || null;
    },

    updateValorMaximo(value, event) {
      const maskedValue = this.formatMoneyMask(value);
      if (event && event.target) {
        this.$nextTick(() => {
          event.target.value = maskedValue;
        });
      }
      const numValue = this.parseCurrencyInput(maskedValue);
      this.form.valor_maximo = numValue || null;
    },

    getUnidadeLabel(unidade) {
      const labels = {
        'min': 'minutos',
        'h': 'horas',
        'sessao': 'sessões'
      };
      return labels[unidade] || unidade;
    },

    getNomePlaceholder() {
      const placeholders = {
        'procedimento': 'Nome do procedimento',
        'produto': 'Nome do produto',
        'servico': 'Nome do serviço'
      };
      return placeholders[this.form.tipo] || 'Nome do item';
    },

    getDescricaoPlaceholder() {
      const placeholders = {
        'procedimento': 'Descrição do procedimento',
        'produto': 'Descrição do produto',
        'servico': 'Descrição do serviço'
      };
      return placeholders[this.form.tipo] || 'Descrição do item';
    },

    async salvar() {
      if (!this.validateForm()) {
        return;
      }

      this.loading = true;
      this.errors = {};

      try {
        const data = { ...this.form };
        
        // Limpar valores nulos/vazios
        if (!data.codigo) data.codigo = null;
        if (!data.dentista_id) data.dentista_id = null;
        if (!data.valor_minimo) data.valor_minimo = null;
        if (!data.valor_maximo) data.valor_maximo = null;
        if (!data.tempo_estimado) data.tempo_estimado = null;

        let response;
        if (this.isEditing) {
          response = await servicoProdutoService.updateServicoProduto(this.procedimentoId, data);
        } else {
          response = await servicoProdutoService.createServicoProduto(data);
        }

        cSwal.cSuccess(
          this.isEditing ? 'Procedimento atualizado com sucesso!' : 'Procedimento criado com sucesso!'
        );

        closeModal('procedimentoModal');
        this.$emit('saved', response.data.data);

      } catch (error) {
        console.error('Erro ao salvar procedimento:', error);
        
        if (error.response && error.response.data && error.response.data.data) {
          this.errors = error.response.data.data;
        } else {
          cSwal.cError('Erro ao salvar procedimento');
        }
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

<style scoped>
/* Modal elegante */
.elegant-modal {
  border: none;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.elegant-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  position: relative;
}

.elegant-header .modal-title {
  font-weight: 600;
  font-size: 1.1rem;
  margin: 0;
  color: #fff !important;
}

.elegant-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: #fff !important;
}

.elegant-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.elegant-close::before {
  content: "×";
  font-size: 1.5rem;
  font-weight: bold;
  line-height: 1;
}

.elegant-body {
  padding: 2rem 2rem 1.5rem 2rem;
  background: #fafbfc;
}

.elegant-body .row > [style*="margin-bottom: 0"] {
  margin-bottom: 0 !important;
}

.elegant-footer {
  background: white;
  border: none;
  padding: 1rem 1.5rem;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* Labels elegantes */
.elegant-label {
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  display: block;
}

/* Inputs elegantes */
.elegant-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: white;
  color: #2d3748;
}

.elegant-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  transform: translateY(-1px);
}

.elegant-input.is-invalid {
  border-color: #e53e3e;
}

/* Textarea elegante */
.elegant-textarea {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: white;
  color: #2d3748;
  resize: vertical;
  min-height: 60px;
}

.elegant-textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Select elegante */
.elegant-select {
  padding: 0.5rem 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: white;
  color: #2d3748;
}

.elegant-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Select customizado com ícone */
.elegant-select-wrapper {
  position: relative;
  display: inline-block;
  width: 100%;
}

.elegant-select-custom {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: white;
  padding-right: 2.5rem !important;
  cursor: pointer;
  width: 100%;
}

.elegant-select-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #718096;
  font-size: 0.75rem;
  pointer-events: none;
  transition: all 0.2s ease;
  z-index: 1;
}

.elegant-select-wrapper:hover .elegant-select-icon {
  color: #007bff;
}

.elegant-select-custom:focus + .elegant-select-icon {
  color: #007bff;
}

/* Input groups elegantes */
.elegant-input-group {
  display: flex;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e2e8f0;
  transition: all 0.2s ease;
}

.elegant-input-group:focus-within {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  transform: translateY(-1px);
}

.elegant-input-group.is-invalid {
  border-color: #e53e3e;
}

.elegant-addon {
  background: #f7fafc;
  color: #718096;
  padding: 0.5rem 0.75rem;
  font-weight: 600;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  white-space: nowrap;
  border: none;
}

.elegant-addon-left {
  border-right: 1px solid #e2e8f0;
}

.elegant-input-group .elegant-input {
  border: none;
  border-radius: 0;
  flex: 1;
}

.elegant-input-group .elegant-input:focus {
  box-shadow: none;
  transform: none;
}

/* Seletor de tipo compacto */
.tipo-selector {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.25rem;
}

.tipo-option {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.6rem 0.875rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
  font-size: 0.875rem;
  font-weight: 500;
}

.tipo-option:hover {
  border-color: #007bff;
  background: #f7fafc;
  transform: translateY(-1px);
}

.tipo-option.active {
  border-color: #007bff;
  background: #007bff;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.tipo-option i {
  font-size: 1rem;
}



/* Botões elegantes */
.elegant-btn-cancel {
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  border: 2px solid #e2e8f0;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.elegant-btn-cancel:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.elegant-btn-save {
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.elegant-btn-save:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

.elegant-btn-save:disabled {
  opacity: 0.7;
  transform: none;
  box-shadow: none;
}

/* Responsividade */
@media (max-width: 768px) {
  .elegant-body {
    padding: 1.5rem;
  }

  .elegant-header,
  .elegant-footer {
    padding: 0.75rem 1rem;
  }

  .tipo-selector {
    flex-direction: column;
  }

  .tempo-container {
    flex-direction: column;
    align-items: stretch;
  }

  .tempo-unidade {
    flex: none;
  }
}

/* Animações */
.elegant-modal {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
