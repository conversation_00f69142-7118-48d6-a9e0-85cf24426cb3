# Preview do Modal de Recuperação de Rascunho

## Como Ficará o Modal

O modal de recuperação de rascunho terá uma aparência elegante e informativa, mostrando exatamente quais campos foram alterados e seus valores.

### Estrutura Visual (Nova - Formato Tabela)

```
┌─────────────────────────────────────────────────────────────┐
│                    📄 Rascunho recuperado                    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  📄  Encontramos um rascunho com 5 alterações não salvas   │
│      da última sessão.                                     │
│                                                             │
│  Alterações encontradas:                                   │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ Campo                │ Valor Anterior │ Novo Valor   │   │
│  ├─────────────────────────────────────────────────────┤   │
│  │ Dados Pessoais (3 alterações)                      │   │
│  ├─────────────────────────────────────────────────────┤   │
│  │ Nome completo        │ João <PERSON>     │ <PERSON>  │   │
│  │ Data de nascimento   │ vazio          │ 15/03/1990   │   │
│  │ CPF                  │ vazio          │ 123.456.789  │   │
│  ├─────────────────────────────────────────────────────┤   │
│  │ Endereço (2 alterações)                            │   │
│  ├─────────────────────────────────────────────────────┤   │
│  │ CEP                  │ vazio          │ 01234-567    │   │
│  │ Cidade               │ vazio          │ São Paulo    │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  🕐 Última modificação: 18/07/2025 14:30:25               │
│                                                             │
│  Deseja recuperar este rascunho?                           │
│                                                             │
│  [↶ Sim, recuperar rascunho]  [🗑️ Não, descartar]         │
└─────────────────────────────────────────────────────────────┘
```

## Características do Design

### 🎨 **Cores e Estilo**
- **Header**: Gradiente azul-verde elegante
- **Categorias**: Gradiente cinza profissional
- **Valores antigos**: Fundo amarelo suave (indicando "antes")
- **Valores novos**: Fundo azul claro (indicando "novo")
- **Botões**: Gradientes com hover animado

### 📱 **Responsividade**
- **Desktop**: Modal largo (600px) com layout em duas colunas para comparação
- **Mobile**: Layout adaptado com valores empilhados
- **Scroll**: Barra customizada para lista de alterações

### ✨ **Animações**
- **Entrada**: Modal desliza suavemente
- **Hover**: Itens destacam com transição suave
- **Botões**: Elevação sutil no hover
- **Sucesso**: Ícone de check com pulse animado

### 🔍 **Detalhes Técnicos**

#### **Formatação Inteligente**
- **CPF**: `12345678900` → `123.456.789-00`
- **Data**: `1990-03-15` → `15/03/1990`
- **CEP**: `01234567` → `01234-567`
- **Dentista**: ID → Nome do dentista
- **Textos longos**: Truncados com "..." após 50 caracteres

#### **Categorização**
- **Dados Pessoais**: Nome, CPF, RG, Data nascimento, etc.
- **Responsável**: Dados do responsável legal
- **Endereço**: CEP, logradouro, cidade, estado, etc.

#### **Ícones Contextuais**
- 👤 Dados pessoais
- 🆔 Documentos
- 🎂 Data de nascimento
- 🏠 Endereço
- 📍 Localização
- 👨‍⚕️ Dentista

## Fluxo de Interação

### 1. **Detecção Automática**
```javascript
// Ao carregar a página
checkAndRestoreFromCache() → Modal aparece se há rascunho
```

### 2. **Escolha do Usuário no Modal**
- **"Sim, recuperar"**:
  - Restaura dados + estados de edição
  - Campos destacados em verde
  - Scroll automático para primeiro campo
  - Toast de confirmação

- **"Não, descartar"**:
  - Remove rascunho do localStorage
  - Continua com dados originais
  - Sem alterações visuais

### 3. **FAB de Navegação (Novo!)**
Quando há alterações pendentes e usuário tenta navegar:

```
┌─────────────────────────────────────────────┐
│  ⚠️  João Silva Santos                      │
│     tem alterações não salvas               │
│                                             │
│  [💾 Salvar e continuar]                   │
│  [← Voltar e salvar]  [🗑️ Descartar]       │
└─────────────────────────────────────────────┘
```

#### **Opções do FAB:**
- **💾 Salvar e continuar**: Salva no banco + navega para destino
- **← Voltar e salvar**: Volta ao perfil + mantém rascunho ativo
- **🗑️ Descartar**: Remove rascunho + navega para destino

### 4. **Pós-Recuperação**
```javascript
// Após recuperar
highlightRestoredFields() → Campos ficam verdes por 8 segundos
scrollToFirstField() → Scroll suave para primeiro campo alterado
showSuccessToast() → "Rascunho restaurado! Continue editando..."
```

## Benefícios da Implementação

### 👨‍⚕️ **Para Médicos/Clínicos**
- ✅ **Transparência total**: Veem exatamente o que foi alterado
- ✅ **Confiança**: Sabem que nada será perdido
- ✅ **Eficiência**: Continuam de onde pararam
- ✅ **Controle**: Decidem se querem recuperar ou não

### 💻 **Para Desenvolvedores**
- ✅ **Manutenibilidade**: Código bem estruturado e documentado
- ✅ **Extensibilidade**: Fácil adicionar novos tipos de campo
- ✅ **Performance**: Formatação lazy e cache otimizado
- ✅ **UX Consistente**: Design system unificado

### 🏥 **Para a Clínica**
- ✅ **Produtividade**: Menos retrabalho
- ✅ **Qualidade**: Dados mais completos e precisos
- ✅ **Satisfação**: Usuários mais confiantes no sistema
- ✅ **Diferencial**: Funcionalidade única e profissional

## Exemplo de Código

### Formatação de Campo
```javascript
formatFieldValue(fieldName, value) {
  switch (fieldName) {
    case 'cpf':
      return value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
    case 'data_nascimento':
      const [year, month, day] = value.split('-');
      return `${day}/${month}/${year}`;
    // ... outros casos
  }
}
```

### Destaque de Campos
```javascript
highlightRestoredFields() {
  this.changedFields.forEach(fieldName => {
    const field = document.querySelector(`[name="${fieldName}"]`);
    field.classList.add('field-restored');
    // Animação de pulse verde por 5 segundos
  });
}
```

Esta implementação representa o estado da arte em UX para sistemas de recuperação de dados, combinando funcionalidade robusta com design elegante e profissional! 🚀
