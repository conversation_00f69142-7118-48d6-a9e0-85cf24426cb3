<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('servicos_produtos', function (Blueprint $table) {
            $table->unsignedBigInteger('dentista_id')->nullable()->after('clinica_id');
            $table->foreign('dentista_id')->references('id')->on('dentistas')->onDelete('set null');
            $table->index('dentista_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('servicos_produtos', function (Blueprint $table) {
            $table->dropForeign(['dentista_id']);
            $table->dropIndex(['dentista_id']);
            $table->dropColumn('dentista_id');
        });
    }
};
