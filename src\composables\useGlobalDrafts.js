import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { getPacienteUrl } from '@/helpers/patientUrlHelper';

// Estado global dos rascunhos
const globalDrafts = ref(new Map());

export function useGlobalDrafts() {
  const router = useRouter();

  // Computed para converter Map em Array para o template
  const draftsArray = computed(() => {
    return Array.from(globalDrafts.value.values())
      .sort((a, b) => b.lastModified - a.lastModified); // Mais recente primeiro
  });

  // Adicionar ou atualizar rascunho
  const addOrUpdateDraft = (patientData) => {
    const key = `patient_${patientData.patientId}`;

    // Se não há alterações, remover o draft
    if (!patientData.changesCount || patientData.changesCount === 0) {
      removeDraft(patientData.patientId);
      return;
    }

    const draftData = {
      patientId: patientData.patientId,
      patientName: patientData.patientName,
      changesCount: patientData.changesCount,
      lastModified: Date.now(),
      showTooltip: false,
      cacheKey: key,
      routePath: `/paciente/${patientData.patientId}`,
      patientClinicId: patientData.patientClinicId, // Incluir ID da clínica do paciente
      ...patientData
    };

    globalDrafts.value.set(key, draftData);

    // Salvar no localStorage para persistência
    saveToLocalStorage();

    console.log('📝 Draft added/updated:', draftData);
  };

  // Remover rascunho
  const removeDraft = (patientId, options = {}) => {
    const key = `patient_${patientId}`;
    globalDrafts.value.delete(key);

    // Remover também do localStorage
    localStorage.removeItem(key);
    saveToLocalStorage();

    console.log('🗑️ Draft removed:', patientId);

    // Se foi salvo via modal, notificar página do paciente
    if (options.savedViaModal && options.savedPatientData) {
      // console.log('📢 Notificando página sobre salvamento via modal');

      // Usar evento global mais específico
      const event = new CustomEvent('draftSavedAndRemoved', {
        detail: {
          patientId: patientId,
          savedPatientData: options.savedPatientData,
          timestamp: Date.now()
        }
      });

      window.dispatchEvent(event);

      // Também salvar no localStorage para garantir
      localStorage.setItem('lastPatientSaved', JSON.stringify({
        patientId: patientId,
        savedPatientData: options.savedPatientData,
        timestamp: Date.now()
      }));
    }
  };

  // Alternar tooltip
  const toggleTooltip = (patientId) => {
    const key = `patient_${patientId}`;
    const draft = globalDrafts.value.get(key);
    
    if (draft) {
      // Fechar todos os outros tooltips
      globalDrafts.value.forEach((d, k) => {
        if (k !== key) {
          d.showTooltip = false;
        }
      });
      
      // Alternar o tooltip atual
      draft.showTooltip = !draft.showTooltip;
      globalDrafts.value.set(key, draft);
    }
  };

  // Fechar tooltip
  const closeTooltip = (patientId) => {
    const key = `patient_${patientId}`;
    const draft = globalDrafts.value.get(key);
    
    if (draft) {
      draft.showTooltip = false;
      globalDrafts.value.set(key, draft);
    }
  };

  // Fechar todos os tooltips
  const closeAllTooltips = () => {
    globalDrafts.value.forEach((draft, key) => {
      draft.showTooltip = false;
      globalDrafts.value.set(key, draft);
    });
  };

  // Verificar se paciente tem rascunho
  const hasDraft = (patientId) => {
    return globalDrafts.value.has(`patient_${patientId}`);
  };

  // Obter rascunho específico
  const getDraft = (patientId) => {
    return globalDrafts.value.get(`patient_${patientId}`);
  };

  // Navegar para paciente
  const openPatient = (draft) => {
    closeAllTooltips();

    // Gerar URL correta usando helper
    const targetUrl = getPacienteUrl(draft);

    router.push(targetUrl);
  };

  // Salvar no localStorage
  const saveToLocalStorage = () => {
    const draftsData = {};
    globalDrafts.value.forEach((draft, key) => {
      draftsData[key] = {
        ...draft,
        showTooltip: false // Não salvar estado do tooltip
      };
    });

    console.log('💾 Salvando drafts no localStorage:', draftsData);
    localStorage.setItem('globalDrafts', JSON.stringify(draftsData));
    console.log('✅ Drafts salvos no localStorage');
  };

  // Carregar do localStorage
  const loadFromLocalStorage = () => {
    try {
      console.log('🔍 Tentando carregar drafts do localStorage...');

      const saved = localStorage.getItem('globalDrafts');
      console.log('📦 globalDrafts no localStorage:', saved);

      if (saved) {
        const draftsData = JSON.parse(saved);
        console.log('📋 Dados dos drafts parseados:', draftsData);

        // Verificar se os rascunhos ainda existem no localStorage individual
        Object.entries(draftsData).forEach(([key, draft]) => {
          console.log(`🔍 Verificando draft ${key}:`, draft);

          // Usar o cacheKey do draft, não a chave do Map global
          const cacheKey = draft.cacheKey || key;
          console.log(`🔑 Procurando cache individual com chave: ${cacheKey}`);

          const individualCache = localStorage.getItem(cacheKey);
          console.log(`📄 Cache individual para ${cacheKey}:`, individualCache ? 'EXISTE' : 'NÃO EXISTE');

          if (individualCache) {
            // Atualizar timestamp se necessário
            draft.lastModified = draft.lastModified || Date.now();
            globalDrafts.value.set(key, draft);
            console.log(`✅ Draft ${key} adicionado ao Map global`);
          } else {
            console.log(`❌ Cache individual ${cacheKey} não encontrado - draft ignorado`);
          }
        });

        console.log('📂 Global drafts loaded:', globalDrafts.value.size);
        console.log('📊 Drafts finais no Map:', Array.from(globalDrafts.value.keys()));
      } else {
        console.log('📭 Nenhum draft global encontrado no localStorage');
      }
    } catch (error) {
      console.error('❌ Error loading global drafts:', error);
    }
  };

  // Limpar todos os rascunhos
  const clearAllDrafts = () => {
    globalDrafts.value.forEach((_, key) => {
      localStorage.removeItem(key);
    });
    
    globalDrafts.value.clear();
    localStorage.removeItem('globalDrafts');
    
    console.log('🧹 All drafts cleared');
  };

  // Obter contagem total de alterações
  const getTotalChangesCount = computed(() => {
    return Array.from(globalDrafts.value.values())
      .reduce((total, draft) => total + draft.changesCount, 0);
  });

  // Inicializar carregando dados salvos
  const initialize = () => {
    loadFromLocalStorage();
  };

  return {
    // Estado
    draftsArray,
    globalDrafts: globalDrafts.value,
    
    // Computed
    getTotalChangesCount,
    
    // Métodos
    addOrUpdateDraft,
    removeDraft,
    toggleTooltip,
    closeTooltip,
    closeAllTooltips,
    hasDraft,
    getDraft,
    openPatient,
    clearAllDrafts,
    initialize,
    
    // Utilitários
    saveToLocalStorage,
    loadFromLocalStorage
  };
}

// Instância singleton para uso global
let globalDraftsInstance = null;

export function getGlobalDraftsInstance() {
  if (!globalDraftsInstance) {
    globalDraftsInstance = useGlobalDrafts();
    globalDraftsInstance.initialize();
  }
  return globalDraftsInstance;
}
