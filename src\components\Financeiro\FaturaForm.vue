<template>
  <div class="fatura-form">
    <div class="row g-3">
      <!-- Paciente -->
      <div class="col-md-6">
        <label class="form-label">Paciente *</label>
        <paciente-busca-button
          :paciente-selecionado="pacienteSelecionado"
          :has-error="!!errors.paciente_id"
          :error-message="errors.paciente_id"
          :disabled="preselectedPaciente"
          placeholder="Clique na lupa para buscar um paciente..."
          @abrir-busca="$emit('abrir-busca-paciente')"
          @limpar-selecao="$emit('limpar-paciente')"
        />
      </div>

      <!-- Dentista -->
      <div class="col-md-6">
        <label class="form-label">Ortodontista</label>
        <select class="form-select" 
                :value="form.dentista_id" 
                @change="$emit('update-form', 'dentista_id', $event.target.value)">
          <option value="">Selecione um ortodontista</option>
          <option v-for="dentista in dentistas" :key="dentista?.id || Math.random()" :value="dentista?.id">
            {{ dentista?.nome || 'Ortodontista sem nome' }}
          </option>
        </select>
      </div>

      <!-- Descrição -->
      <div class="col-12">
        <label class="form-label">Descrição *</label>
        <input type="text" 
               class="form-control" 
               :value="form.descricao" 
               @input="$emit('update-form', 'descricao', $event.target.value)"
               :class="{ 'is-invalid': errors.descricao }"
               placeholder="Descrição da fatura">
        <div class="invalid-feedback" v-if="errors.descricao">
          {{ errors.descricao }}
        </div>
      </div>

      <!-- Valor Nominal -->
      <div class="col-md-4">
        <label class="form-label">Valor Nominal *</label>
        <div class="input-group">
          <span class="input-group-text">R$</span>
          <input type="number" 
                 step="0.01" 
                 class="form-control" 
                 :value="form.valor_nominal"
                 @input="updateValorNominal($event.target.value)"
                 :class="{ 'is-invalid': errors.valor_nominal }">
        </div>
        <div class="invalid-feedback" v-if="errors.valor_nominal">
          {{ errors.valor_nominal }}
        </div>
      </div>

      <!-- Data de Vencimento -->
      <div class="col-md-4">
        <label class="form-label">Data de Vencimento *</label>
        <input type="date" 
               class="form-control" 
               :value="form.data_vencimento"
               @change="$emit('update-form', 'data_vencimento', $event.target.value)"
               :class="{ 'is-invalid': errors.data_vencimento }">
        <div class="invalid-feedback" v-if="errors.data_vencimento">
          {{ errors.data_vencimento }}
        </div>
      </div>

      <!-- Parcelas -->
      <div class="col-md-4">
        <label class="form-label">Número de Parcelas</label>
        <input type="number" 
               min="1" 
               max="60" 
               class="form-control" 
               :value="form.parcelas_total"
               @input="$emit('update-form', 'parcelas_total', parseInt($event.target.value) || 1)"
               :class="{ 'is-invalid': errors.parcelas_total }">
        <div class="invalid-feedback" v-if="errors.parcelas_total">
          {{ errors.parcelas_total }}
        </div>
      </div>

      <!-- Descontos -->
      <div class="col-md-6">
        <label class="form-label">Desconto</label>
        <div class="row g-2">
          <div class="col-6">
            <div class="input-group">
              <input type="number" 
                     step="0.01" 
                     class="form-control" 
                     :value="form.percentual_desconto"
                     @input="updatePercentualDesconto($event.target.value)"
                     placeholder="%">
              <span class="input-group-text">%</span>
            </div>
          </div>
          <div class="col-6">
            <div class="input-group">
              <span class="input-group-text">R$</span>
              <input type="number" 
                     step="0.01" 
                     class="form-control" 
                     :value="form.valor_desconto"
                     @input="updateValorDesconto($event.target.value)">
            </div>
          </div>
        </div>
      </div>

      <!-- Acréscimos -->
      <div class="col-md-6">
        <label class="form-label">Acréscimo</label>
        <div class="row g-2">
          <div class="col-6">
            <div class="input-group">
              <input type="number" 
                     step="0.01" 
                     class="form-control" 
                     :value="form.percentual_acrescimo"
                     @input="updatePercentualAcrescimo($event.target.value)"
                     placeholder="%">
              <span class="input-group-text">%</span>
            </div>
          </div>
          <div class="col-6">
            <div class="input-group">
              <span class="input-group-text">R$</span>
              <input type="number" 
                     step="0.01" 
                     class="form-control" 
                     :value="form.valor_acrescimo"
                     @input="updateValorAcrescimo($event.target.value)">
            </div>
          </div>
        </div>
      </div>

      <!-- Valor Final -->
      <div class="col-md-6">
        <label class="form-label">Valor Final</label>
        <div class="input-group">
          <span class="input-group-text">R$</span>
          <input type="text" 
                 class="form-control bg-light" 
                 :value="formatCurrency(calculatedFinalValue)" 
                 readonly>
        </div>
      </div>

      <!-- Observações -->
      <div class="col-12">
        <label class="form-label">Observações</label>
        <textarea class="form-control" 
                  rows="3" 
                  :value="form.observacoes"
                  @input="$emit('update-form', 'observacoes', $event.target.value)"
                  placeholder="Observações adicionais"></textarea>
      </div>
    </div>
  </div>
</template>

<script>
import { financeiroService } from '@/services/financeiroService';
import PacienteBuscaButton from '@/components/Global/PacienteBuscaButton.vue';

export default {
  name: 'FaturaForm',
  components: {
    PacienteBuscaButton,
  },
  props: {
    form: {
      type: Object,
      required: true
    },
    errors: {
      type: Object,
      default: () => ({})
    },
    pacientes: {
      type: Array,
      default: () => []
    },
    dentistas: {
      type: Array,
      default: () => []
    },
    preselectedPaciente: {
      type: [String, Number],
      default: null
    },
    pacienteSelecionado: {
      type: Object,
      default: null
    }
  },
  computed: {
    calculatedFinalValue() {
      return financeiroService.calculateFinalValue(
        this.form.valor_nominal,
        this.form.percentual_desconto,
        this.form.valor_desconto,
        this.form.percentual_acrescimo,
        this.form.valor_acrescimo
      );
    }
  },
  methods: {
    formatCurrency: financeiroService.formatCurrency,

    updateValorNominal(value) {
      this.$emit('update-form', 'valor_nominal', parseFloat(value) || 0);
      this.clearOppositeDiscount();
    },

    updatePercentualDesconto(value) {
      const numValue = parseFloat(value) || 0;
      this.$emit('update-form', 'percentual_desconto', numValue);
      if (numValue > 0) {
        this.$emit('update-form', 'valor_desconto', 0);
      }
    },

    updateValorDesconto(value) {
      const numValue = parseFloat(value) || 0;
      this.$emit('update-form', 'valor_desconto', numValue);
      if (numValue > 0) {
        this.$emit('update-form', 'percentual_desconto', 0);
      }
    },

    updatePercentualAcrescimo(value) {
      const numValue = parseFloat(value) || 0;
      this.$emit('update-form', 'percentual_acrescimo', numValue);
      if (numValue > 0) {
        this.$emit('update-form', 'valor_acrescimo', 0);
      }
    },

    updateValorAcrescimo(value) {
      const numValue = parseFloat(value) || 0;
      this.$emit('update-form', 'valor_acrescimo', numValue);
      if (numValue > 0) {
        this.$emit('update-form', 'percentual_acrescimo', 0);
      }
    },

    clearOppositeDiscount() {
      // Limpar campos de desconto/acréscimo quando valor nominal muda
    }
  }
};
</script>

<style scoped>
.bg-light {
  background-color: #f8f9fa !important;
}

.input-group-text {
  background-color: #f8f9fa;
  border-color: #dee2e6;
}

.is-invalid {
  border-color: #dc3545;
}

.invalid-feedback {
  display: block;
}
</style>
