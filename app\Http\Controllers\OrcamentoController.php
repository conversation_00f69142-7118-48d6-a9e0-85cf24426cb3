<?php

namespace App\Http\Controllers;

use App\Models\Orcamento;
use App\Models\OrcamentoItem;
use App\Models\ServicoProduto;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class OrcamentoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $query = Orcamento::with(['paciente', 'dentista', 'criadoPor'])
            ->daClinica($clinicaId);

        // Filtros
        if ($request->has('paciente_id')) {
            $query->doPaciente($request->paciente_id);
        }

        if ($request->has('status')) {
            $query->porStatus($request->status);
        }

        if ($request->has('data_inicio') && $request->has('data_fim')) {
            $query->whereBetween('data_orcamento', [
                $request->data_inicio,
                $request->data_fim
            ]);
        }

        if ($request->has('dentista_id')) {
            $query->where('dentista_id', $request->dentista_id);
        }

        $orcamentos = $query->orderBy('created_at', 'desc')
                           ->paginate(15);

        return responseSuccess(['data' => $orcamentos]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $validator = Validator::make($request->all(), [
            'paciente_id' => 'nullable|exists:pacientes,id',
            'dentista_id' => 'nullable|exists:dentistas,id',
            'titulo' => 'required|string|max:255',
            'descricao' => 'nullable|string',
            'data_validade' => 'nullable|date|after:today',
            'observacoes' => 'nullable|string',
            'itens' => 'required|array|min:1',
            'itens.*.servico_produto_id' => 'nullable|exists:servicos_produtos,id',
            'itens.*.nome' => 'required|string|max:255',
            'itens.*.quantidade' => 'required|numeric|min:0.01',
            'itens.*.valor_unitario' => 'required|numeric|min:0.01',
            'itens.*.desconto_percentual' => 'nullable|numeric|min:0|max:100',
            'itens.*.desconto_valor' => 'nullable|numeric|min:0',
            'itens.*.dentes' => 'nullable|array',
            'itens.*.dentes.*' => 'integer|min:11|max:48',
        ]);

        if ($validator->fails()) {
            return responseError([
                'message' => 'Dados inválidos',
                'data' => $validator->errors(),
                'statusCode' => 422
            ]);
        }

        try {
            DB::beginTransaction();

            $orcamento = Orcamento::create([
                'clinica_id' => $clinicaId,
                'paciente_id' => $request->paciente_id,
                'dentista_id' => $request->dentista_id,
                'titulo' => $request->titulo,
                'descricao' => $request->descricao,
                'data_orcamento' => now()->toDateString(),
                'data_validade' => $request->data_validade,
                'observacoes' => $request->observacoes,
                'criado_por' => $user['id'],
                'status' => Orcamento::STATUS_RASCUNHO,
            ]);

            // Criar itens
            foreach ($request->itens as $index => $itemData) {
                OrcamentoItem::create([
                    'orcamento_id' => $orcamento->id,
                    'servico_produto_id' => $itemData['servico_produto_id'] ?? null,
                    'nome' => $itemData['nome'],
                    'descricao' => $itemData['descricao'] ?? null,
                    'quantidade' => $itemData['quantidade'],
                    'valor_unitario' => $itemData['valor_unitario'],
                    'desconto_percentual' => $itemData['desconto_percentual'] ?? 0,
                    'desconto_valor' => $itemData['desconto_valor'] ?? 0,
                    'observacoes' => $itemData['observacoes'] ?? null,
                    'ordem' => $index + 1,
                    'dentes' => $itemData['dentes'] ?? null,
                ]);
            }

            // Calcular valores totais
            $orcamento->calcularValores();

            DB::commit();

            return responseSuccess([
                'message' => 'Orçamento criado com sucesso',
                'data' => $orcamento->load(['paciente', 'dentista', 'itens.servicoProduto'])
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return responseError([
                'message' => 'Erro ao criar orçamento: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $orcamento = Orcamento::with(['paciente', 'dentista', 'criadoPor', 'itens.servicoProduto'])
            ->daClinica($clinicaId)
            ->findOrFail($id);

        return responseSuccess(['data' => $orcamento]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $orcamento = Orcamento::daClinica($clinicaId)->findOrFail($id);

        if (!$orcamento->podeEditar()) {
            return responseError([
                'message' => 'Este orçamento não pode ser editado',
                'statusCode' => 422
            ]);
        }

        $validator = Validator::make($request->all(), [
            'titulo' => 'sometimes|string|max:255',
            'descricao' => 'nullable|string',
            'data_validade' => 'nullable|date|after:today',
            'observacoes' => 'nullable|string',
            'itens' => 'sometimes|array|min:1',
            'itens.*.id' => 'nullable|exists:orcamento_itens,id',
            'itens.*.servico_produto_id' => 'nullable|exists:servicos_produtos,id',
            'itens.*.nome' => 'required|string|max:255',
            'itens.*.quantidade' => 'required|numeric|min:0.01',
            'itens.*.valor_unitario' => 'required|numeric|min:0.01',
            'itens.*.desconto_percentual' => 'nullable|numeric|min:0|max:100',
            'itens.*.desconto_valor' => 'nullable|numeric|min:0',
            'itens.*.dentes' => 'nullable|array',
            'itens.*.dentes.*' => 'integer|min:11|max:48',
        ]);

        if ($validator->fails()) {
            return responseError([
                'message' => 'Dados inválidos',
                'data' => $validator->errors(),
                'statusCode' => 422
            ]);
        }

        try {
            DB::beginTransaction();

            // Atualizar orçamento
            $orcamento->update($request->only([
                'titulo', 'descricao', 'data_validade', 'observacoes'
            ]));

            // Atualizar itens se fornecidos
            if ($request->has('itens')) {
                // Remover itens existentes
                $orcamento->itens()->delete();

                // Criar novos itens
                foreach ($request->itens as $index => $itemData) {
                    OrcamentoItem::create([
                        'orcamento_id' => $orcamento->id,
                        'servico_produto_id' => $itemData['servico_produto_id'] ?? null,
                        'nome' => $itemData['nome'],
                        'descricao' => $itemData['descricao'] ?? null,
                        'quantidade' => $itemData['quantidade'],
                        'valor_unitario' => $itemData['valor_unitario'],
                        'desconto_percentual' => $itemData['desconto_percentual'] ?? 0,
                        'desconto_valor' => $itemData['desconto_valor'] ?? 0,
                        'observacoes' => $itemData['observacoes'] ?? null,
                        'ordem' => $index + 1,
                        'dentes' => $itemData['dentes'] ?? null,
                    ]);
                }

                // Recalcular valores
                $orcamento->calcularValores();
            }

            DB::commit();

            return responseSuccess([
                'message' => 'Orçamento atualizado com sucesso',
                'data' => $orcamento->load(['paciente', 'dentista', 'itens.servicoProduto'])
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return responseError([
                'message' => 'Erro ao atualizar orçamento: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $orcamento = Orcamento::daClinica($clinicaId)->findOrFail($id);

        if (!$orcamento->podeEditar()) {
            return responseError([
                'message' => 'Este orçamento não pode ser excluído',
                'statusCode' => 422
            ]);
        }

        try {
            $orcamento->delete();

            return responseSuccess(['message' => 'Orçamento excluído com sucesso']);
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao excluir orçamento: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Enviar orçamento para o paciente
     */
    public function enviar(string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $orcamento = Orcamento::daClinica($clinicaId)->findOrFail($id);

        if (!$orcamento->podeEnviar()) {
            return responseError([
                'message' => 'Este orçamento não pode ser enviado',
                'statusCode' => 422
            ]);
        }

        try {
            $orcamento->enviar();

            return responseSuccess([
                'message' => 'Orçamento enviado com sucesso',
                'data' => $orcamento->fresh(['paciente', 'dentista'])
            ]);
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao enviar orçamento: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Aprovar orçamento
     */
    public function aprovar(Request $request, string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $orcamento = Orcamento::daClinica($clinicaId)->findOrFail($id);

        if (!$orcamento->podeAprovar()) {
            return responseError([
                'message' => 'Este orçamento não pode ser aprovado',
                'statusCode' => 422
            ]);
        }

        try {
            $orcamento->aprovar($request->get('aprovado_por', $user['name']));

            return responseSuccess([
                'message' => 'Orçamento aprovado com sucesso',
                'data' => $orcamento->fresh(['paciente', 'dentista'])
            ]);
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao aprovar orçamento: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Converter orçamento em fatura
     */
    public function converterParaFatura(string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $orcamento = Orcamento::daClinica($clinicaId)->findOrFail($id);

        if (!$orcamento->podeConverter()) {
            return responseError([
                'message' => 'Este orçamento não pode ser convertido em fatura',
                'statusCode' => 422
            ]);
        }

        try {
            $fatura = $orcamento->converterParaFatura();

            return responseSuccess([
                'message' => 'Orçamento convertido em fatura com sucesso',
                'data' => [
                    'orcamento' => $orcamento->fresh(['paciente', 'dentista']),
                    'fatura' => $fatura->load(['paciente', 'dentista'])
                ]
            ]);
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao converter orçamento: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Duplicar orçamento
     */
    public function duplicar(string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $orcamento = Orcamento::daClinica($clinicaId)->findOrFail($id);

        try {
            $novoOrcamento = $orcamento->duplicar();

            return responseSuccess([
                'message' => 'Orçamento duplicado com sucesso',
                'data' => $novoOrcamento->load(['paciente', 'dentista', 'itens.servicoProduto'])
            ]);
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao duplicar orçamento: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Obter orçamentos de um paciente específico
     */
    public function orcamentosPaciente(string $pacienteId)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $orcamentos = Orcamento::with(['dentista', 'itens'])
            ->daClinica($clinicaId)
            ->doPaciente($pacienteId)
            ->orderBy('created_at', 'desc')
            ->get();

        return responseSuccess(['data' => $orcamentos]);
    }

    /**
     * Obter estatísticas de orçamentos
     */
    public function estatisticas(Request $request)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $query = Orcamento::daClinica($clinicaId);

        if ($request->has('data_inicio') && $request->has('data_fim')) {
            $query->whereBetween('data_orcamento', [
                $request->data_inicio,
                $request->data_fim
            ]);
        }

        $totalRascunhos = $query->clone()->rascunhos()->count();
        $totalEnviados = $query->clone()->enviados()->count();
        $totalAprovados = $query->clone()->aprovados()->count();
        $totalConvertidos = $query->clone()->porStatus(Orcamento::STATUS_CONVERTIDO)->count();

        $valorTotalOrcamentos = $query->clone()->sum('valor_final');
        $valorAprovado = $query->clone()->aprovados()->sum('valor_final');
        $valorConvertido = $query->clone()->porStatus(Orcamento::STATUS_CONVERTIDO)->sum('valor_final');

        $taxaAprovacao = $totalEnviados > 0 ? ($totalAprovados / $totalEnviados) * 100 : 0;
        $taxaConversao = $totalAprovados > 0 ? ($totalConvertidos / $totalAprovados) * 100 : 0;

        $estatisticas = [
            'total_rascunhos' => $totalRascunhos,
            'total_enviados' => $totalEnviados,
            'total_aprovados' => $totalAprovados,
            'total_convertidos' => $totalConvertidos,
            'valor_total_orcamentos' => $valorTotalOrcamentos,
            'valor_aprovado' => $valorAprovado,
            'valor_convertido' => $valorConvertido,
            'taxa_aprovacao' => round($taxaAprovacao, 2),
            'taxa_conversao' => round($taxaConversao, 2),
        ];

        return responseSuccess(['data' => $estatisticas]);
    }
}
