<template>
  <lumi-sidenav
    class="fixed-end lumi-sidenav"
    icon="mdi-calendar-month"
    v-if="showSidenav"
    :config="sidenavConfig"
    @action="handleSidenavAction"
  ></lumi-sidenav>
  <!-- <div
    class="bg-gradient-secondary shadow-success pt-1 pb-1"
  >
    <h6 class="text-white text-center ps-3">{{data}}</h6>
  </div> -->
  <div class="main-page-content calendar-container center-self border-radius-2xl">
    <div class="row">
      <div class="col-12 position-relative">
        <lumi-calendar
          ref="lumiCalendar"
          :events="events"
          :loading="isLoading.consultas"
          :config="cfg"
          :view="currentView"
          v-model:date="data"
          @calendarClosed="void 0"
          @fetchEvents="void 0"
          @dateSelected="onDateSelected"
          @calendar:view-changed="onViewChanged"
        />
      </div>
    </div>
  </div>





  <!-- Componente ConsultaModal -->
  <ConsultaModal ref="consultaModal" @consulta-salva="carregarConsultas" />
</template>

<script>
import LumiSidenav from "@/views/components/LumiSidenav/index.vue";
import ConsultaModal from "@/components/ConsultaModal.vue";
import moment from 'moment';
import { mapMutations, mapState } from "vuex";
import { getDentistas } from "@/services/dentistasService";
import { searchPacientes } from "@/services/pacientesService";
import { getConsultas, getConsulta } from "@/services/consultasService";
import { generateCalendarPDF } from "@/services/pdfService";
import cSwal from "@/utils/cSwal.js";

const cfg = {
  viewEvent: {
    icon: true,
    text: "Ver consulta",
  },
  // reportEvent: {
  //   icon: true,
  //   text: "Reagendar",
  // },
  openPatientEvent: {
    icon: true,
    text: "Abrir paciente",
  },
  searchPlaceholder: "",
  eventName: "",
  closeText: "",
  nativeDatepicker: true,
  todayButton: true,
  firstDayOfWeek: 1,
};

// Eventos de exemplo
const events = [
  // {
  //   date: "2025-05-15T12:00:00.000Z",
  //   comment: "comment",
  //   id: "cl32rbkjk1700101o53e3e3uhn",
  //   keywords: "Consulta paga",
  //   name: "Elizangela Alves de Morais",
  // },
  // {
  //   date: "2024-09-13T15:30:00.000Z",
  //   comment: "comment",
  //   id: "cl32rbkjk1700101o53e3e3uhn",
  //   keywords: "Consulta confirmada",
  //   name: "Angélica Ribeiro",
  // },
  // {
  //   date: "2024-09-13T13:00:00.000Z",
  //   comment: "comment",
  //   id: "cl32rbkjk1700101o53e3e3uhn",
  //   keywords: "Confirmação pendente",
  //   name: "Antônio Lopes",
  // },
  // //...
]

const data = new Date()

export default {
  name: "agenda",
  components: {
    LumiSidenav,
    ConsultaModal,
  },
  computed: {
    ...mapState([
      "showSidenav",
    ])
  },
  data() {
    return {
      date: new Date(),
      cfg,
      events,
      data,
      selectedDate: new Date(), // Data selecionada como objeto Date
      currentView: 'week', // Visualização atual (day, week, month)
      dentistas: [],
      pacientes: [],
      isLoading: {
        consultas: true, // Começar com o estado de carregamento ativo
        form: false
      },
      initialLoad: true, // Flag para controlar o carregamento inicial,
      sidenavConfig: {
        groups: [
          {
            title: "AGENDA",
            buttons: [
              {
                text: "Agendar consulta",
                icon: "add",
                iconType: "material",
                action: "newAppointment"
              },
              // {
              //   text: "Reagendar/Cancelar",
              //   icon: "settings_backup_restore",
              //   iconType: "material",
              //   action: "reschedule"
              // },
              {
                text: "Imprimir",
                icon: "print",
                iconType: "material",
                action: "print"
              }
            ]
          },
          // {
          //   title: "EM CONSULTA",
          //   buttons: [
          //     {
          //       text: "Novo prontuário",
          //       icon: "person_add",
          //       iconType: "material",
          //       action: "newRecord"
          //     },
          //     {
          //       text: "Pagamento",
          //       icon: "add_card",
          //       iconType: "material",
          //       action: "payment"
          //     },
          //     {
          //       text: "Atestado",
          //       icon: "text_snippet",
          //       iconType: "material",
          //       action: "certificate"
          //     }
          //   ]
          // }
        ]
      }
    };
  },
  async created() {
    await this.carregarDentistas();
    await this.carregarPacientes();
    await this.carregarConsultas();
  },

  mounted() {
    // Adicionar event listener para o evento de visualização de consulta
    document.body.addEventListener('calendar.request.view', this.handleCalendarViewEvent);
    document.body.addEventListener('calendar.request.report', this.handleCalendarReportEvent);
    document.body.addEventListener('calendar.request.open_patient', this.handleCalendarOpenPatientEvent);

    // Adicionar event listener para cliques nas células do calendário
    document.addEventListener('calendar:cell-clicked', this.handleCalendarCellClick);
  },

  beforeUnmount() {
    // Remover event listeners para evitar memory leaks
    document.body.removeEventListener('calendar.request.view', this.handleCalendarViewEvent);
    document.body.removeEventListener('calendar.request.report', this.handleCalendarReportEvent);
    document.body.removeEventListener('calendar.request.open_patient', this.handleCalendarOpenPatientEvent);

    // Remover event listener para cliques nas células do calendário
    document.removeEventListener('calendar:cell-clicked', this.handleCalendarCellClick);
  },
  methods: {
    ...mapMutations(["navbarMinimize"]),

    // Método para fechar a sidenav
    closeSidenav() {
      // Verificar se a sidenav está aberta
      const sidenavElement = document.querySelector(".g-sidenav-show");
      if (sidenavElement && sidenavElement.classList.contains("g-sidenav-pinned")) {
        // Usar o método do Vuex para fechar a sidenav
        this.navbarMinimize();
      }
    },

    /**
     * Configura a detecção manual de mudança de visualização
     */
    setupViewChangeDetection() {
      // Adicionar event listeners aos botões de visualização
      setTimeout(() => {
        const toggleButtons = document.querySelectorAll('.calendar-toggle-btn');
        if (toggleButtons.length > 0) {
          console.log('Configurando detecção de mudança de visualização para', toggleButtons.length, 'botões');

          toggleButtons.forEach(button => {
            button.addEventListener('click', () => {
              // Dar um tempo para a mudança de visualização ser aplicada
              setTimeout(() => {
                this.detectCurrentView();
              }, 100);
            });
          });
        }
      }, 1000); // Aguardar 1 segundo para garantir que os botões estejam renderizados
    },

    /**
     * Detecta a visualização atual com base no botão ativo
     */
    detectCurrentView() {
      const activeButton = document.querySelector('.calendar-toggle-btn.active');
      if (activeButton) {
        const buttonText = activeButton.textContent.trim().toLowerCase();
        console.log('DETECÇÃO - Texto do botão ativo:', buttonText);

        let detectedView = this.currentView;

        if (buttonText.includes('dia')) {
          detectedView = 'day';
        } else if (buttonText.includes('semana')) {
          detectedView = 'week';
        } else if (buttonText.includes('mês') || buttonText.includes('mes')) {
          detectedView = 'month';
        }

        // Atualizar a propriedade currentView se for diferente
        if (this.currentView !== detectedView) {
          console.log(`DETECÇÃO - Atualizando visualização: de ${this.currentView} para ${detectedView}`);
          this.currentView = detectedView;
        }
      } else {
        console.log('DETECÇÃO - Nenhum botão de visualização ativo encontrado');
      }
    },

    async carregarDentistas() {
      try {
        const response = await getDentistas();
        if (response) {
          this.dentistas = response;
        }
      } catch (error) {
        console.error("Erro ao carregar dentistas:", error);
        cSwal.cError("Erro ao carregar a lista de dentistas.");
      }
    },

    async carregarPacientes() {
      try {
        const response = await searchPacientes();
        if (response) {
          this.pacientes = response;
        }
      } catch (error) {
        console.error("Erro ao carregar pacientes:", error);
        cSwal.cError("Erro ao carregar a lista de pacientes.");
      }
    },

    async carregarConsultas() {
      try {
        const response = await getConsultas();

        if (response) {
          // Transformar os dados da API para o formato esperado pelo componente LumiCalendar
          this.events = response.map(consulta => {

            // O horário vem como "YYYY-MM-DD HH:MM:SS" do backend
            let dataHora;

            try {
              // Verificar se temos o campo horario
              if (consulta.horario) {
                // Converter o timestamp do MySQL para um objeto Date
                // Não adicionar 'Z' para evitar que a data seja interpretada como UTC
                // Isso preserva o fuso horário local (GMT-3)
                dataHora = moment(consulta.horario).format('YYYY-MM-DDTHH:mm:ss.SSS');
              } else if (consulta.date) {
                // Alternativa: usar o campo date se disponível
                dataHora = moment(consulta.date).format('YYYY-MM-DDTHH:mm:ss.SSS');
              } else if (consulta.data) {
                // Alternativa: usar o campo data se disponível
                dataHora = moment(consulta.data).format('YYYY-MM-DDTHH:mm:ss.SSS');
              } else {
                // Fallback para data atual se não houver nenhum campo de data
                console.error("Consulta sem campo de data/hora:", consulta);
                dataHora = moment().format('YYYY-MM-DDTHH:mm:ss.SSS');
              }
            } catch (e) {
              // Fallback para data atual se houver erro
              console.error("Erro ao formatar data/hora:", e, consulta);
              dataHora = moment().format('YYYY-MM-DDTHH:mm:ss.SSS');
            }

            // Verificar se temos o nome do paciente
            let nomePaciente = '';
            if (consulta.paciente_nome) {
              nomePaciente = consulta.paciente_nome;
            } else if (consulta.name) {
              nomePaciente = consulta.name;
            } else {
              console.warn('Consulta sem nome de paciente:', consulta);
              nomePaciente = 'Paciente não identificado';
            }

            // Verificar se temos o status da consulta
            let statusConsulta = 'agendada';
            if (consulta.status) {
              statusConsulta = consulta.status;
            } else if (consulta.keywords) {
              // Tentar extrair o status das keywords
              const match = consulta.keywords.match(/Consulta\s+(\w+)/i);
              if (match && match[1]) {
                statusConsulta = match[1].toLowerCase();
              }
            }

            // Verificar se temos o valor da consulta
            let valorConsulta = '0,00';
            if (consulta.valor) {
              valorConsulta = consulta.valor;
            } else if (consulta.comment) {
              // Tentar extrair o valor do comentário
              const match = consulta.comment.match(/R\$\s*([0-9,.]+)/);
              if (match && match[1]) {
                valorConsulta = match[1];
              }
            }

            // Criar o objeto de evento para o calendário
            const eventoCalendario = {
              id: consulta.id,
              date: dataHora,
              name: nomePaciente,
              keywords: `Consulta ${statusConsulta}`,
              comment: `Valor: R$ ${valorConsulta}`,
              // Armazenar dados adicionais que podem ser úteis
              _original: {
                paciente_id_ficha: consulta.paciente?.id_ficha || null,
                dentista_id: consulta.dentista_id,
                observacoes: consulta.observacoes,
                status: statusConsulta,
                valor: valorConsulta
              }
            };

            return eventoCalendario;
          });

        } else {
          this.events = [];
        }
      } catch (error) {
        console.error("Erro ao carregar consultas:", error);
        cSwal.cError("Erro ao carregar a lista de consultas.");
        this.events = [];
      } finally {
        // Desativar o estado de carregamento após carregar os dados
        this.isLoading.consultas = false;
      }
    },



    /**
     * Manipula o evento de visualização de consulta do calendário
     */
    async handleCalendarViewEvent(event) {
      console.log('Evento de visualização recebido:', event.detail);

      // Verificar se o evento contém um ID válido
      if (!event.detail || !event.detail.id) {
        console.error('Evento de visualização sem ID válido:', event.detail);
        cSwal.cError("Erro ao identificar a consulta. Por favor, tente novamente.");
        return;
      }

      // Obter o ID da consulta
      const consultaId = event.detail.id;
      console.log('ID da consulta extraído do evento:', consultaId);

      // Buscar os dados da consulta
      await this.carregarConsultaParaEdicao(consultaId);
    },

    /**
     * Manipula o evento de relatório de consulta do calendário
     */
    handleCalendarReportEvent(event) {
      console.log('Evento de relatório recebido:', event.detail);
      // Implementação futura para relatórios
    },

    /**
     * Manipula o evento de abrir paciente do calendário
     */
    async handleCalendarOpenPatientEvent(event) {
      console.log('Evento de abrir paciente recebido:', event.detail);

      // Verificar se o evento contém um ID válido
      if (!event.detail || !event.detail.id) {
        console.error('Evento de abrir paciente sem ID válido:', event.detail);
        cSwal.cError("Erro ao identificar a consulta. Por favor, tente novamente.");
        return;
      }

      // Obter o ID da consulta
      const consultaId = event.detail.id;
      console.log('ID da consulta extraído do evento:', consultaId);

      try {
        // Buscar os dados da consulta para obter o ID do paciente
        const consulta = await getConsulta(consultaId);
        console.log('Resposta da API para consulta:', consulta);

        if (!consulta) {
          console.error('Consulta não encontrada ou retornou dados vazios');
          cSwal.cError("Erro ao carregar dados da consulta. Por favor, tente novamente.");
          return;
        }

        // Verificar se a consulta é um objeto
        if (typeof consulta !== 'object') {
          console.error('Resposta da API não é um objeto:', consulta);
          cSwal.cError("Erro: Formato de resposta inválido. Por favor, tente novamente.");
          return;
        }

        // Verificar/extrair o ID do paciente
        let pacienteIdFicha = null;

        if (consulta.paciente.id_ficha) {
          pacienteIdFicha = consulta.paciente.id_ficha;
        } else {
          // Tentar extrair do evento do calendário
          const eventoCalendario = this.events.find(e => e.id == consultaId);
          if (eventoCalendario && eventoCalendario._original && eventoCalendario._original.paciente.id_ficha) {
            pacienteIdFicha = eventoCalendario._original.paciente.id_ficha;
          } else if (eventoCalendario && eventoCalendario.name) {
            // Tentar encontrar o ID do paciente pelo nome
            const pacienteEncontrado = this.pacientes.find(p => p.nome === eventoCalendario.name);
            if (pacienteEncontrado) {
              pacienteIdFicha = pacienteEncontrado.id_ficha;
            }
          }
        }

        if (!pacienteIdFicha) {
          console.error('Não foi possível determinar o ID do paciente:', consulta);
          cSwal.cError("Erro: Não foi possível identificar o paciente. Por favor, tente novamente.");
          return;
        }

        // Navegar para a página do paciente
        console.log('Navegando para a página do paciente:', pacienteIdFicha);
        this.$router.push({
          name: "PacientePadrao",
          params: {
            id_ficha: pacienteIdFicha,
          },
        });
      } catch (error) {
        console.error("Erro ao abrir paciente:", error);
        cSwal.cError("Erro ao abrir a página do paciente. Por favor, tente novamente.");
      }
    },

    /**
     * Carrega os dados da consulta para edição
     */
    async carregarConsultaParaEdicao(consultaId) {
      try {
        console.log('Iniciando carregamento da consulta para edição, ID:', consultaId);
        this.isLoading.form = true;

        // Buscar os dados da consulta
        const consulta = await getConsulta(consultaId);
        console.log('Resposta da API para consulta:', consulta);

        if (!consulta) {
          console.error('Consulta não encontrada ou retornou dados vazios');
          cSwal.cError("Erro ao carregar dados da consulta. Por favor, tente novamente.");
          return;
        }

        // Verificar se a consulta é um objeto
        if (typeof consulta !== 'object') {
          console.error('Resposta da API não é um objeto:', consulta);
          cSwal.cError("Erro: Formato de resposta inválido. Por favor, tente novamente.");
          return;
        }

        // Verificar se temos os dados necessários para a consulta
        // Primeiro, vamos tentar encontrar o horário da consulta
        let horarioConsulta = null;

        // Verificar diferentes formatos possíveis para o horário
        if (consulta.horario) {
          console.log('Horário encontrado diretamente:', consulta.horario);
          horarioConsulta = consulta.horario;
        } else if (consulta.date) {
          console.log('Usando campo date como horário:', consulta.date);
          horarioConsulta = consulta.date;
        } else if (consulta.data) {
          console.log('Usando campo data como horário:', consulta.data);
          horarioConsulta = consulta.data;
        } else {
          // Tentar extrair do evento do calendário
          const eventoCalendario = this.events.find(e => e.id == consultaId);
          if (eventoCalendario && eventoCalendario.date) {
            console.log('Usando data do evento do calendário:', eventoCalendario.date);
            horarioConsulta = eventoCalendario.date;
          }
        }

        if (!horarioConsulta) {
          console.error('Não foi possível determinar o horário da consulta:', consulta);
          cSwal.cError("Erro: Não foi possível determinar o horário da consulta. Por favor, tente novamente.");
          return;
        }

        // Extrair a data e o horário do timestamp
        console.log('Horário da consulta a ser usado:', horarioConsulta);
        const dataHora = moment(horarioConsulta);

        if (!dataHora.isValid()) {
          console.error('Data/hora inválida:', horarioConsulta);
          cSwal.cError("Erro: Data/hora inválida. Por favor, tente novamente.");
          return;
        }

        console.log('Data/hora parseada:', dataHora.format('YYYY-MM-DD HH:mm:ss'));

        // Verificar/extrair o ID do paciente
        let pacienteId = null;
        let pacienteNome = '';

        if (consulta.paciente_id) {
          pacienteId = consulta.paciente_id;
          pacienteNome = consulta.paciente_nome || '';
        } else if (consulta.name) {
          // Se temos apenas o nome do paciente do evento do calendário
          pacienteNome = consulta.name;

          // Tentar encontrar o ID do paciente pelo nome
          const pacienteEncontrado = this.pacientes.find(p => p.nome === consulta.name);
          if (pacienteEncontrado) {
            pacienteId = pacienteEncontrado.id;
          }
        }

        if (!pacienteId && !pacienteNome) {
          // Tentar extrair do evento do calendário
          const eventoCalendario = this.events.find(e => e.id == consultaId);
          if (eventoCalendario && eventoCalendario.name) {
            pacienteNome = eventoCalendario.name;

            // Tentar encontrar o ID do paciente pelo nome
            const pacienteEncontrado = this.pacientes.find(p => p.nome === eventoCalendario.name);
            if (pacienteEncontrado) {
              pacienteId = pacienteEncontrado.id;
            }
          }
        }

        // Extrair valor da consulta
        let valorConsulta = '';
        if (consulta.valor !== undefined) {
          valorConsulta = consulta.valor;
        } else if (consulta.comment) {
          // Tentar extrair o valor do comentário (ex: "Valor: R$ 100,00")
          const match = consulta.comment.match(/R\$\s*([0-9,.]+)/);
          if (match && match[1]) {
            valorConsulta = match[1].replace(',', '.');
          }
        }

        // Não precisamos mais preencher o formulário de edição manualmente
        // O componente ConsultaModal vai buscar os dados da consulta

        // Usar o componente ConsultaModal para editar a consulta
        console.log('Abrindo modal de edição via componente ConsultaModal');
        this.$refs.consultaModal.abrirModalEditarConsulta(consultaId);
        console.log('Modal aberto via componente');

      } catch (error) {
        console.error("Erro ao carregar consulta para edição:", error);
        cSwal.cError("Erro ao carregar dados da consulta. Por favor, tente novamente.");
      } finally {
        this.isLoading.form = false;
      }
    },



    /**
     * Atualiza a visualização atual do calendário
     */
    onViewChanged(view) {
      console.log('EVENTO RECEBIDO - Visualização alterada para:', view);
      console.log('Visualização anterior:', this.currentView);
      this.currentView = view;
      console.log('Visualização atualizada para:', this.currentView);

      // Ativar o estado de carregamento ao mudar a visualização
      this.isLoading.consultas = true;
      // Recarregar as consultas para a nova visualização
      this.carregarConsultas();

      // Exibir alerta para confirmar a mudança de visualização (apenas para depuração)
      // alert(`Visualização alterada para: ${view}`);
    },

    /**
     * Atualiza a data selecionada no calendário
     */
    onDateSelected(dateSelected) {
      // Armazenar a data selecionada como objeto Date
      this.selectedDate = new Date(dateSelected);

      // Formatar a data para exibição
      // Usar locale() em vez de lang() que está depreciado
      let dataFormatada = moment(dateSelected).locale("pt-br").format('dddd, D [de] MMMM [de] YYYY')
      this.data = dataFormatada

      // Recarregar consultas quando a data for alterada, mas não na primeira vez
      // pois já carregamos no hook created()
      if (!this.initialLoad) {
        // Ativar o estado de carregamento antes de carregar as consultas
        this.isLoading.consultas = true;
        this.carregarConsultas();
      } else {
        this.initialLoad = false; // Desativar a flag após o primeiro carregamento
      }
    },

    /**
     * Handle actions from the sidenav
     */
    handleSidenavAction(action, button) {
      console.log(`Action: ${action}`, button);

      // Implementar as ações da sidenav
      switch (action) {
        case 'newAppointment':
          // Usar o componente ConsultaModal
          this.$refs.consultaModal.abrirModalNovaConsulta();
          break;
        case 'reschedule':
          // Lógica para reagendar
          alert('Funcionalidade: Reagendar');
          break;
        case 'print':
          // Lógica para imprimir
          this.printCalendar();
          break;
        case 'newRecord':
          // Lógica para criar novo prontuário
          alert('Funcionalidade: Novo prontuário');
          break;
        case 'payment':
          // Lógica para pagamento
          alert('Funcionalidade: Pagamento');
          break;
        case 'certificate':
          // Lógica para atestado
          alert('Funcionalidade: Atestado');
          break;
      }
    },

    /**
     * Generate a PDF of the current calendar view
     */
    async printCalendar() {
      try {
        // Verificar a visualização atual antes de gerar o PDF
        console.log('IMPRESSÃO - Visualização atual:', this.currentView);
        console.log('IMPRESSÃO - Botões de visualização:', document.querySelectorAll('.calendar-toggle-btn.active').length > 0 ?
          document.querySelector('.calendar-toggle-btn.active').textContent.trim() : 'Nenhum botão ativo');

        // Ativar o estado de carregamento (já deve estar ativo, mas garantimos aqui)
        this.isLoading.consultas = true;

        // Get the calendar element
        const calendarElement = document.querySelector('[data-widget-item="widget-calendar-comp"]');

        if (!calendarElement) {
          cSwal.cError("Não foi possível encontrar o calendário para impressão.");
          return;
        }

        // Detectar a visualização atual de duas formas
        let viewType = this.currentView;

        // Método alternativo: verificar qual botão de visualização está ativo
        const activeButton = document.querySelector('.calendar-toggle-btn.active');
        if (activeButton) {
          const buttonText = activeButton.textContent.trim().toLowerCase();
          console.log('Texto do botão ativo:', buttonText);

          if (buttonText.includes('dia')) {
            viewType = 'day';
          } else if (buttonText.includes('semana')) {
            viewType = 'week';
          } else if (buttonText.includes('mês') || buttonText.includes('mes')) {
            viewType = 'month';
          }

          // Atualizar a propriedade currentView se for diferente
          if (this.currentView !== viewType) {
            console.log(`Corrigindo visualização: de ${this.currentView} para ${viewType}`);
            this.currentView = viewType;
          }
        }

        console.log('Tipo de visualização detectada:', viewType);

        // Filter events based on the current view
        let filteredEvents = [...this.events];

        // Prepare date variables for filtering
        const currentDay = moment(this.selectedDate).format('YYYY-MM-DD');
        const startOfWeek = moment(this.selectedDate).startOf('week');
        const endOfWeek = moment(this.selectedDate).endOf('week');
        const startOfMonth = moment(this.selectedDate).startOf('month');
        const endOfMonth = moment(this.selectedDate).endOf('month');

        // Filtrar eventos com base na visualização atual
        console.log(`Filtrando eventos para a visualização: ${viewType}`);

        if (viewType === 'day') {
          // Para visualização diária, incluir apenas eventos do dia atual
          filteredEvents = this.events.filter(event => {
            const eventDateStr = moment(event.date).format('YYYY-MM-DD');
            return eventDateStr === currentDay;
          });
          console.log(`Total de eventos para o dia ${currentDay}: ${filteredEvents.length}`);
        }
        else if (viewType === 'week') {
          // Para visualização semanal, incluir apenas eventos da semana atual
          filteredEvents = this.events.filter(event => {
            const eventDate = moment(event.date);
            return eventDate.isSameOrAfter(startOfWeek) && eventDate.isSameOrBefore(endOfWeek);
          });
          console.log(`Total de eventos para a semana (${startOfWeek.format('DD/MM/YYYY')} a ${endOfWeek.format('DD/MM/YYYY')}): ${filteredEvents.length}`);
        }
        else if (viewType === 'month') {
          // Para visualização mensal, incluir apenas eventos do mês atual
          filteredEvents = this.events.filter(event => {
            const eventDate = moment(event.date);
            return eventDate.isSameOrAfter(startOfMonth) && eventDate.isSameOrBefore(endOfMonth);
          });
          console.log(`Total de eventos para o mês de ${startOfMonth.format('MMMM/YYYY')}: ${filteredEvents.length}`);
        }

        // Se não houver eventos filtrados, exibir mensagem
        if (filteredEvents.length === 0) {
          console.log('Nenhum evento encontrado para o período selecionado.');
        }

        // Log para depuração
        console.log('Total de eventos antes da filtragem:', this.events.length);
        console.log('Total de eventos após a filtragem:', filteredEvents.length);
        console.log('Eventos filtrados:', filteredEvents);

        // Verificar a visualização uma última vez antes de gerar o PDF
        this.detectCurrentView();

        // Usar a visualização mais recente
        if (this.currentView !== viewType) {
          console.log(`Atualizando visualização antes de gerar PDF: de ${viewType} para ${this.currentView}`);
          viewType = this.currentView;

          // Refiltragem dos eventos com a visualização correta
          if (viewType === 'day') {
            filteredEvents = this.events.filter(event => {
              const eventDateStr = moment(event.date).format('YYYY-MM-DD');
              return eventDateStr === currentDay;
            });
          } else if (viewType === 'week') {
            filteredEvents = this.events.filter(event => {
              const eventDate = moment(event.date);
              return eventDate.isSameOrAfter(startOfWeek) && eventDate.isSameOrBefore(endOfWeek);
            });
          } else if (viewType === 'month') {
            filteredEvents = this.events.filter(event => {
              const eventDate = moment(event.date);
              return eventDate.isSameOrAfter(startOfMonth) && eventDate.isSameOrBefore(endOfMonth);
            });
          }
        }

        // Generate the PDF and open in new tab
        await generateCalendarPDF(calendarElement, viewType, this.selectedDate, filteredEvents, true);

        // Show success message
        let mensagem = '';
        if (viewType === 'day') {
          mensagem = `O PDF com as consultas do dia ${moment(this.selectedDate).format('DD/MM/YYYY')} foi aberto em uma nova guia!`;
        } else if (viewType === 'week') {
          const inicio = moment(startOfWeek).format('DD/MM');
          const fim = moment(endOfWeek).format('DD/MM/YYYY');
          mensagem = `O PDF com as consultas da semana (${inicio} a ${fim}) foi aberto em uma nova guia!`;
        } else {
          const mes = moment(this.selectedDate).locale('pt-br').format('MMMM/YYYY');
          mensagem = `O PDF com as consultas do mês de ${mes} foi aberto em uma nova guia!`;
        }
        cSwal.cSuccess(mensagem);
      } catch (error) {
        console.error("Erro ao gerar PDF:", error);
        cSwal.cError("Erro ao gerar o PDF. Por favor, tente novamente.");
      } finally {
        // Hide loading indicator
        this.isLoading.consultas = false;
      }
    },

    /**
     * Método para abrir modal de nova consulta com data e horário pré-preenchidos
     */
    abrirModalConsultaComHorario(data, horario = null) {
      console.log('Abrindo modal de consulta com:', { data, horario });
      this.$refs.consultaModal.abrirModalNovaConsultaComHorario(data, horario);
    },

    /**
     * Manipula cliques nas células do calendário
     */
    handleCalendarCellClick(event) {
      console.log('Clique na célula do calendário:', event.detail);

      const { date, time, view } = event.detail;

      // Converter a data para o formato correto se necessário
      let dataFormatada = date;
      if (typeof date === 'string') {
        dataFormatada = new Date(date);
      }

      // Abrir modal com data e horário pré-preenchidos
      if (view === 'month') {
        // Para visualização mensal, apenas a data (sem horário específico)
        this.abrirModalConsultaComHorario(dataFormatada);
      } else {
        // Para visualizações diária e semanal, incluir o horário
        this.abrirModalConsultaComHorario(dataFormatada, time);
      }
    }
  }
};
</script>

<style scoped>
/* Modal styling */
.modal-content {
  border-radius: 0.75rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border: none;
}

.modal-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 1.5rem;
}

.modal-header .modal-title {
  font-weight: 600;
  font-size: 1.25rem;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 1.5rem;
}

/* Form styling */
.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.input-group {
  margin-bottom: 0.5rem;
  border-radius: 0.5rem;
  overflow: hidden;
}

.input-group-text {
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  color: #6c757d;
}

.form-control, .form-select {
  border: 1px solid #ced4da;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

.form-control:focus, .form-select:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Button styling */
.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.btn-primary:hover {
  background-color: #0069d9;
  border-color: #0062cc;
  transform: translateX(2px);
}

.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.btn-secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
  transform: translateX(2px);
}

/* Helper text */
small.text-muted {
  display: block;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  color: #6c757d;
}

/* Date and time input styling */
.date-time-input {
  font-size: 1rem !important;
  font-weight: 500;
}

/* Input MD customization */
.input-md {
  height: calc(var(--lumi-input-height) + 5px) !important;
  font-size: 0.95rem !important;
}

/* Relative time badge */
.relative-time-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(var(--lumi-input-height) + 5px);
  padding: 6px 12px;
  border-radius: 6px;
  background-color: #1470e9;
  color: #EEE;
  font-weight: 500;
  font-size: 0.95rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  width: 100%;
}

/* Observações textarea */
.observacoes-textarea {
  resize: none;
  min-height: 80px;
  font-size: 0.9rem !important;
  line-height: 1.4;
  border-color: #ced4da;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.observacoes-textarea:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Status button group styling */
.btn-group .btn {
  font-size: 0.85rem;
  padding: 0.375rem 0.5rem;
  transition: transform 0.2s ease, opacity 0.2s ease;
  border-width: 1px;
}

.status-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 0.35rem !important;
  padding-bottom: 0.35rem !important;
}

.status-icon {
  margin-bottom: 3px;
  font-size: 1rem;
}

.btn-group .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Mobile status buttons in footer */
.mobile-status-group {
  margin-top: -0.5rem;
}

.mobile-status-group .btn {
  font-size: 0.8rem;
  padding: 0.3rem 0.5rem;
  height: 38px;
}

@keyframes fade-in {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(3px);
  }
}

@keyframes float-in {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* PDF printing styles */
@media print {
  .calendar-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .calendar-view-container {
    page-break-inside: avoid;
  }

  .lumi-sidenav,
  .navbar,
  .footer {
    display: none !important;
  }
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
  .modal-dialog {
    margin: 0.5rem auto;
    max-width: 92%;
    height: auto;
  }

  .modal-dialog-scrollable .modal-content {
    max-height: 85vh;
  }

  .modal-header {
    padding: 0.75rem 1rem;
  }

  .modal-body {
    padding: 0.75rem;
    overflow-y: auto;
    max-height: calc(85vh - 130px); /* Subtract header and footer height */
  }

  .modal-footer {
    padding: 0.75rem 1rem;
  }

  .btn-group .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.35rem;
    height: auto;
  }

  .status-icon {
    display: none;
  }

  .input-md {
    height: calc(var(--lumi-input-height) + 2px) !important;
    font-size: 0.9rem !important;
  }

  .relative-time-badge {
    height: calc(var(--lumi-input-height) + 2px);
    font-size: 0.85rem;
  }

  .observacoes-textarea {
    min-height: 60px;
    font-size: 0.85rem !important;
  }

}
</style>
