<template>
  <Teleport to="body">
    <div 
      v-if="show" 
      id="unsaved-changes-fab" 
      class="unsaved-changes-fab"
      :class="{ show: isVisible }"
    >
      <div class="fab-content" @click="handleFabClick">
        <div class="fab-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="fab-text">
          <strong>{{ title }}</strong>
          <small>{{ subtitle }}</small>
        </div>
      </div>
      
      <div class="fab-tooltip" v-if="showTooltip">
        <div class="tooltip-content">
          <h6 class="mb-2">
            <i class="fas fa-edit me-2"></i>
            {{ tooltipTitle }}
          </h6>
          <p class="mb-2">
            <strong>{{ patientName }}</strong> {{ tooltipMessage }}
          </p>
          <div class="tooltip-actions">
            <button
              class="btn btn-sm btn-success me-2"
              @click="handleSaveAndContinue"
            >
              <i class="fas fa-save me-1"></i>
              Salvar e continuar
            </button>
            <button
              class="btn btn-sm btn-primary me-2"
              @click="handleReturn"
            >
              <i class="fas fa-arrow-left me-1"></i>
              {{ returnButtonText }}
            </button>
            <button
              class="btn btn-sm btn-outline-secondary"
              @click="handleDiscard"
            >
              <i class="fas fa-times me-1"></i>
              {{ discardButtonText }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script>
export default {
  name: 'UnsavedChangesFab',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: 'Alterações não salvas'
    },
    subtitle: {
      type: String,
      default: 'Clique para voltar e salvar'
    },
    patientName: {
      type: String,
      default: 'Paciente'
    },
    tooltipTitle: {
      type: String,
      default: 'Alterações pendentes no perfil'
    },
    tooltipMessage: {
      type: String,
      default: 'possui alterações não salvas.'
    },
    returnButtonText: {
      type: String,
      default: 'Voltar e salvar'
    },
    discardButtonText: {
      type: String,
      default: 'Descartar'
    },
    showTooltip: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isVisible: false
    };
  },
  watch: {
    show: {
      handler(newVal) {
        if (newVal) {
          this.$nextTick(() => {
            setTimeout(() => {
              this.isVisible = true;
            }, 100);
          });
        } else {
          this.isVisible = false;
        }
      },
      immediate: true
    }
  },
  methods: {
    handleFabClick() {
      this.$emit('fab-click');
      this.handleReturn();
    },
    handleSaveAndContinue() {
      this.$emit('save-and-continue');
    },
    handleReturn() {
      this.$emit('return');
    },
    handleDiscard() {
      this.$emit('discard');
    }
  }
};
</script>

<style scoped>
/* FAB de alterações não salvas */
.unsaved-changes-fab {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1060;
  opacity: 0;
  transform: translateY(100px) scale(0.8);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  pointer-events: none;
}

.unsaved-changes-fab.show {
  opacity: 1;
  transform: translateY(0) scale(1);
  pointer-events: auto;
}

.unsaved-changes-fab .fab-content {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  padding: 1rem 1.25rem;
  border-radius: 50px;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 280px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.unsaved-changes-fab .fab-content:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(255, 107, 107, 0.5);
}

.unsaved-changes-fab .fab-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.unsaved-changes-fab .fab-content:hover::before {
  left: 100%;
}

.unsaved-changes-fab .fab-icon {
  font-size: 1.25rem;
  animation: pulse 2s infinite;
}

.unsaved-changes-fab .fab-text strong {
  display: block;
  font-size: 0.95rem;
  margin-bottom: 0.125rem;
}

.unsaved-changes-fab .fab-text small {
  font-size: 0.8rem;
  opacity: 0.9;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Tooltip do FAB */
.unsaved-changes-fab .fab-tooltip {
  position: absolute;
  bottom: 100%;
  right: 0;
  margin-bottom: 0.5rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  pointer-events: none;
}

.unsaved-changes-fab:hover .fab-tooltip,
.unsaved-changes-fab .fab-tooltip:hover {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto;
}

/* Criar uma "ponte" invisível entre o FAB e o tooltip */
.unsaved-changes-fab .fab-tooltip::before {
  content: '';
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  height: 0.5rem;
  background: transparent;
}

.unsaved-changes-fab .tooltip-content {
  background: white;
  border-radius: 12px;
  padding: 1.25rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid #e9ecef;
  min-width: 320px;
  max-width: 400px;
}

.unsaved-changes-fab .tooltip-content h6 {
  color: #495057;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.unsaved-changes-fab .tooltip-content p {
  color: #6c757d;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.unsaved-changes-fab .tooltip-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.unsaved-changes-fab .tooltip-actions .btn {
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
}

/* Responsividade para mobile */
@media (max-width: 768px) {
  .unsaved-changes-fab {
    bottom: 20px;
    right: 20px;
  }
  
  .unsaved-changes-fab .fab-content {
    min-width: 260px;
    padding: 0.875rem 1rem;
  }
  
  .unsaved-changes-fab .tooltip-content {
    min-width: 280px;
    max-width: calc(100vw - 40px);
  }
  
  .unsaved-changes-fab .tooltip-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .unsaved-changes-fab .tooltip-actions .btn {
    width: 100%;
    padding: 0.75rem 1rem;
  }
}
</style>
