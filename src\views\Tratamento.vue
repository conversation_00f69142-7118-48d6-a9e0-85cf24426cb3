<template>
  <div>
    <!-- Stats Header - Exibido independente da tab selecionada -->
    <ConsultasStats :paciente="paciente" class="mb-3" @pacienteChange="pacienteChange" @abrirModalConsulta="abrirModalNovaConsulta" />

    <div class="p-horizontal-divider light my-2"></div>

    <div class="nav-wrapper position-relative end-0 mb-1">
      <ul class="nav nav-pills nav-fill nav-main p-1 pb-0 mx-auto" role="tablist">
        <li class="nav-item">
          <a class="nav-link mb-0 px-0 py-2 d-flex align-items-center justify-content-center"
            :class="{ 'active': activeTab === 'consultas' }" @click="selectTab('consultas')" href="javascript:;"
            role="tab" :aria-selected="activeTab === 'consultas' ? 'true' : 'false'">
            <font-awesome-icon :icon="['fas', 'calendar-alt']" class="me-1" style="font-size: 0.8rem;" />
            <span>Consultas e Histórico</span>
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link mb-0 px-0 py-2 d-flex align-items-center justify-content-center"
            :class="{ 'active': activeTab === 'imagens' }" @click="selectTab('imagens')" href="javascript:;" role="tab"
            :aria-selected="activeTab === 'imagens' ? 'true' : 'false'">
            <font-awesome-icon :icon="['fas', 'images']" class="me-1" style="font-size: 0.8rem;" />
            <span>Imagens</span>
          </a>
        </li>
      </ul>
    </div>

    <div class="p-horizontal-divider light my-2 w-100"></div>

    <Transition>
      <div v-show="activeTab === 'consultas'" class="tab-pane fade show active">
        <ConsultasHistoricoTimeline ref="consultasHistoricoTimeline" :paciente="paciente" :dentistas="dentistas"
          @update:consultas="updateConsultas" @editar-consulta="editarConsulta" @ver-historico="verHistoricoConsulta" />
      </div>
    </Transition>

    <Transition>
      <div v-show="activeTab === 'imagens'" class="tab-pane fade show active">
        <Imagens
          :paciente="paciente"
          @pacienteChange="pacienteChange"
          :hideAnalyseButton="true"
          mode="regular"
        />
      </div>
    </Transition>

    <!-- Modais globais -->
    <ConsultaModal ref="consultaModal" :paciente-id="paciente.id" @consulta-salva="recarregarConsultas" />
    <HistoricoConsultaModal ref="historicoModal" />
  </div>
</template>

<script>
import ConsultasHistoricoTimeline from "@/views/components/ConsultasHistoricoTimeline.vue";
import ConsultasStats from "@/views/components/ConsultasStats.vue";
import Imagens from "@/views/Planejamento/components/Imagens.vue";
import ConsultaModal from "@/components/ConsultaModal.vue";
import HistoricoConsultaModal from "@/components/HistoricoConsultaModal.vue";
import setNavPills from "@/assets/js/nav-pills.js";

export default {
  name: "Tratamento",
  components: {
    ConsultasHistoricoTimeline,
    ConsultasStats,
    Imagens,
    ConsultaModal,
    HistoricoConsultaModal
  },
  props: {
    paciente: {
      type: Object,
      required: true
    },
    dentistas: {
      type: Array,
      default: () => []
    }
  },
  emits: ['pacienteChange', 'update:consultas', 'editar-consulta', 'ver-historico'],
  data() {
    return {
      activeTab: 'consultas'
    };
  },
  methods: {
    selectTab(tab) {
      // Atualizar a variável de estado
      this.activeTab = tab;

    },
    updateConsultas(consultas) {
      this.$emit('update:consultas', consultas);
    },
    editarConsulta(id) {
      this.$refs.consultaModal.abrirModalEditarConsulta(id);
    },
    verHistoricoConsulta(id) {
      this.$refs.historicoModal.abrirModal(id);
    },
    recarregarConsultas() {
      if (this.$refs.consultasHistoricoTimeline) {
        this.$refs.consultasHistoricoTimeline.fetchConsultas();
      }
    },
    pacienteChange() {
      this.$emit('pacienteChange');
    },
    abrirModalNovaConsulta() {
      this.$refs.consultaModal.abrirModalNovaConsulta();
    }
  },
  mounted() {
    this.$nextTick(() => {
      // Garantir que a tab inicial (consultas) esteja ativa
      this.activeTab = 'consultas';

      // Não inicializar o nav-pills para usar o estilo de tab azul em vez do moving-tab
    });
  }
};
</script>

<style scoped>
.nav-main .nav-link.active:hover {
    color: #DDD !important;
}
/* Transition effects */
.v-enter-active,
.v-leave-active {
  transition: opacity 0.3s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}

/* Estilo de tab azul (invertido do Imagens.vue) */
.nav-pills .nav-link.active {
  background-color: #0d6efd;
  color: white;
  box-shadow: 0 4px 6px rgba(13, 110, 253, 0.2);
}

.nav-pills .nav-link {
  transition: all 0.3s ease;
  border-radius: 6px;
  padding: 10px 15px;
  font-weight: 500;
}

.nav-pills .nav-link:hover:not(.active) {
  background-color: rgba(13, 110, 253, 0.1);
}

</style>
