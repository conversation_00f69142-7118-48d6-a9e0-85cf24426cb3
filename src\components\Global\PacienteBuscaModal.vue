<template>
  <div class="modal fade" id="modalBuscaPaciente" tabindex="-1" aria-labelledby="modalBuscaPacienteLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="modalBuscaPacienteLabel">
            <i class="fas fa-search me-2"></i>
            Buscar Paciente
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <!-- Campo de Busca -->
          <div class="row mb-3">
            <div class="col-12">
              <div class="input-group">
                <span class="input-group-text">
                  <i class="fas fa-search"></i>
                </span>
                <input 
                  type="text" 
                  class="form-control" 
                  v-model="searchTerm"
                  @input="debouncedSearch"
                  placeholder="Digite o nome, ID da ficha ou telefone do paciente..."
                  ref="searchInput">
              </div>
            </div>
          </div>

          <!-- Loading -->
          <div v-if="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Buscando...</span>
            </div>
            <p class="mt-2 text-muted">Buscando pacientes...</p>
          </div>

          <!-- Lista de Pacientes -->
          <div v-else-if="pacientes.length > 0" class="pacientes-list">
            <div class="row g-2">
              <div 
                v-for="paciente in pacientes" 
                :key="paciente.id" 
                class="col-12">
                <div 
                  class="card paciente-card h-100" 
                  :class="{ 'selected': selectedPaciente?.id === paciente.id }"
                  @click="selectPaciente(paciente)">
                  <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                      <!-- Avatar -->
                      <div class="avatar avatar-sm me-3">
                        <img 
                          v-if="paciente.foto_perfil" 
                          :src="paciente.foto_perfil" 
                          :alt="paciente.nome"
                          class="avatar-img rounded-circle">
                        <div 
                          v-else 
                          class="avatar-img rounded-circle bg-gradient-primary d-flex align-items-center justify-content-center">
                          <i class="fas fa-user text-white"></i>
                        </div>
                      </div>

                      <!-- Informações do Paciente -->
                      <div class="flex-grow-1">
                        <h6 class="mb-1">{{ paciente.nome }}</h6>
                        <div class="d-flex flex-wrap gap-2">
                          <small class="badge bg-light text-dark">
                            <i class="fas fa-hashtag me-1"></i>
                            {{ String(paciente.id_ficha).padStart(3, '0') }}
                          </small>
                          <small v-if="paciente.telefone" class="badge bg-light text-dark">
                            <i class="fas fa-phone me-1"></i>
                            {{ formatPhone(paciente.telefone) }}
                          </small>
                          <small v-if="paciente.email" class="badge bg-light text-dark">
                            <i class="fas fa-envelope me-1"></i>
                            {{ paciente.email }}
                          </small>
                        </div>
                        <small v-if="paciente.data_nascimento" class="text-muted">
                          <i class="fas fa-birthday-cake me-1"></i>
                          {{ formatAge(paciente.data_nascimento) }}
                        </small>
                      </div>

                      <!-- Indicador de Seleção -->
                      <div class="ms-2">
                        <i 
                          v-if="selectedPaciente?.id === paciente.id" 
                          class="fas fa-check-circle text-success fs-5"></i>
                        <i 
                          v-else 
                          class="fas fa-circle text-muted"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Estado Vazio -->
          <div v-else-if="searchTerm && !loading" class="text-center py-4">
            <i class="fas fa-search text-muted fs-1 mb-3"></i>
            <h6 class="text-muted">Nenhum paciente encontrado</h6>
            <p class="text-muted mb-0">
              Tente buscar por nome, ID da ficha ou telefone
            </p>
          </div>

          <!-- Estado Inicial -->
          <div v-else-if="!searchTerm" class="text-center py-4">
            <i class="fas fa-users text-muted fs-1 mb-3"></i>
            <h6 class="text-muted">Digite para buscar pacientes</h6>
            <p class="text-muted mb-0">
              Comece digitando o nome, ID da ficha ou telefone
            </p>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            Cancelar
          </button>
          <button 
            type="button" 
            class="btn btn-primary" 
            :disabled="!selectedPaciente"
            @click="confirmarSelecao">
            <i class="fas fa-check me-1"></i>
            Selecionar Paciente
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { searchPacientes } from '@/services/pacientesService';
import { openModal, closeModal } from '@/utils/modalHelper';

export default {
  name: 'PacienteBuscaModal',
  data() {
    return {
      searchTerm: '',
      pacientes: [],
      selectedPaciente: null,
      loading: false,
      debounceTimer: null
    };
  },
  methods: {
    open() {
      this.reset();
      openModal('modalBuscaPaciente');
      
      // Focar no campo de busca após o modal abrir
      this.$nextTick(() => {
        if (this.$refs.searchInput) {
          this.$refs.searchInput.focus();
        }
      });
    },

    close() {
      closeModal('modalBuscaPaciente');
    },

    reset() {
      this.searchTerm = '';
      this.pacientes = [];
      this.selectedPaciente = null;
      this.loading = false;
      
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = null;
      }
    },

    debouncedSearch() {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      this.debounceTimer = setTimeout(() => {
        this.buscarPacientes();
      }, 300);
    },

    async buscarPacientes() {
      if (!this.searchTerm || this.searchTerm.length < 2) {
        this.pacientes = [];
        return;
      }

      this.loading = true;
      try {
        const response = await searchPacientes(this.searchTerm);
        this.pacientes = response || [];
      } catch (error) {
        console.error('Erro ao buscar pacientes:', error);
        this.pacientes = [];
      } finally {
        this.loading = false;
      }
    },

    selectPaciente(paciente) {
      this.selectedPaciente = paciente;
    },

    confirmarSelecao() {
      if (this.selectedPaciente) {
        this.$emit('paciente-selecionado', this.selectedPaciente);
        this.close();
      }
    },

    formatPhone(phone) {
      if (!phone) return '';
      
      // Remove tudo que não é número
      const numbers = phone.replace(/\D/g, '');
      
      // Formata como (XX) XXXXX-XXXX ou (XX) XXXX-XXXX
      if (numbers.length === 11) {
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7)}`;
      } else if (numbers.length === 10) {
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 6)}-${numbers.slice(6)}`;
      }
      
      return phone;
    },

    formatAge(dataNascimento) {
      if (!dataNascimento) return '';
      
      const hoje = new Date();
      const nascimento = new Date(dataNascimento);
      const idade = hoje.getFullYear() - nascimento.getFullYear();
      const mesAtual = hoje.getMonth();
      const mesNascimento = nascimento.getMonth();
      
      let idadeFinal = idade;
      if (mesAtual < mesNascimento || (mesAtual === mesNascimento && hoje.getDate() < nascimento.getDate())) {
        idadeFinal--;
      }
      
      return `${idadeFinal} anos`;
    }
  }
};
</script>

<style scoped>
.paciente-card {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.paciente-card:hover {
  border-color: #5e72e4;
  box-shadow: 0 4px 6px rgba(94, 114, 228, 0.1);
  transform: translateY(-1px);
}

.paciente-card.selected {
  border-color: #28a745;
  background-color: rgba(40, 167, 69, 0.05);
}

.avatar {
  width: 40px;
  height: 40px;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.pacientes-list {
  max-height: 400px;
  overflow-y: auto;
}

.pacientes-list::-webkit-scrollbar {
  width: 6px;
}

.pacientes-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.pacientes-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.pacientes-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.badge {
  font-size: 0.75rem;
}

.modal-content {
  border-radius: 15px;
}

.input-group-text {
  background-color: #f8f9fa;
  border-color: #dee2e6;
}

.form-control:focus {
  border-color: #5e72e4;
  box-shadow: 0 0 0 0.2rem rgba(94, 114, 228, 0.25);
}
</style>
