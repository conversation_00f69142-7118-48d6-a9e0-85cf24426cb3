import store from '@/store'
import usuariosService from "@/services/usuariosService"
const { API_URL } = require('@/config.json')
import axios from 'axios'
import { jwtDecode } from 'jwt-decode';
// import { history } from '../index.js'
// import { userService } from './user'
// import { jwtService } from './jwt'

// DEBUG ONLY:
// localStorage.removeItem('token');
// localStorage.removeItem('contracts');
// localStorage.removeItem('selectedContract');
// localStorage.removeItem('name');
// localStorage.removeItem('firstName');
axios.defaults.timeout = 400000;
axios.defaults.withCredentials = true;
axios.defaults.baseURL = API_URL;
axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';

axios.refreshToken = (token = null) => {
    if (token) {
        localStorage.setItem('token', token);
        const decodedToken = jwtDecode(token);
        store.commit('setToken', decodedToken);
    } else {
        // If no token is provided, check if there's one in localStorage
        const storedToken = localStorage.getItem('token');
        if (storedToken) {
            try {
                const decodedToken = jwtDecode(storedToken);
                store.commit('setToken', decodedToken);
            } catch (error) {
                console.error('Error decoding token from localStorage:', error);
            }
        }
    }

    axios.defaults.headers.common['Authorization'] = localStorage.getItem('token') ? 'Bearer ' + localStorage.getItem('token') : (localStorage.getItem('tempToken') ? 'Bearer ' + localStorage.getItem('tempToken') : '')
}

if (!axios.defaults.headers.common['Authorization'])
    axios.refreshToken()

// axios.generateTempToken = async () => {
// 	const now = parseInt(new Date().getTime() / 1000);

// 	const tempToken = localStorage.getItem('tempToken')

// 	let tempTokenData

// 	if (tempToken)
// 		tempTokenData = jwtService.decode(tempToken)

// 	if (!tempToken || now > tempTokenData.payload.exp) {
// 		const response = await axios.get('/user/temptoken')

// 		if (!response)
// 			return false;

// 		localStorage.setItem('tempToken', response.data.token);
// 	}

// 	axios.refreshToken()
// }

// axios.refreshToken()

axios.interceptors.request.use(request => {
    // console.log(request);
    // Edit request config
    return request;
}, error => {
    console.log(error);
    return Promise.reject(error);
});

axios.interceptors.response.use(response => {
    // Verificar se é uma rota de notificações e se a resposta pode ter problemas de JSON
    if (response.config.url && response.config.url.includes('/notifications')) {
        try {
            // Se a resposta for uma string, tentar fazer parse
            if (typeof response.data === 'string') {
                // Verificar se parece ser JSON malformado (múltiplos objetos concatenados)
                if (response.data.includes('}{')) {
                    console.warn('Resposta JSON malformada detectada para notificações:', response.data)
                    // Tentar extrair o primeiro objeto JSON válido
                    const firstJsonMatch = response.data.match(/^{[^}]*}/)
                    if (firstJsonMatch) {
                        response.data = JSON.parse(firstJsonMatch[0])
                        console.warn('JSON corrigido automaticamente:', response.data)
                    } else {
                        // Se não conseguir extrair, retornar erro padrão
                        response.data = { status: 'error', message: 'Server Error' }
                    }
                } else {
                    // Tentar fazer parse normal
                    response.data = JSON.parse(response.data)
                }
            }
        } catch (parseError) {
            console.warn('Erro ao processar resposta de notificações:', parseError)
            // Em caso de erro de parse, retornar estrutura de erro padrão
            response.data = { status: 'error', message: 'Server Error' }
        }
    }

    return response;
}, error => {
    if (!error.response) {
        // Handle API Offline Error
    }
    else if (error.response.status === 401) {
        usuariosService.logout();
    }
    return error;
})

export default axios