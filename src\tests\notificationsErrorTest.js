/**
 * Teste para verificar o tratamento de erros do sistema de notificações
 * Este arquivo pode ser usado para testar se os erros estão sendo tratados corretamente
 */

import notificationsService from '@/services/notificationsService'

export const testNotificationsErrorHandling = {
  /**
   * Testa o fetchUnreadCount em modo silencioso
   */
  async testSilentMode() {
    console.log('🧪 Testando modo silencioso do fetchUnreadCount...')
    
    try {
      // Testar em modo silencioso - não deve quebrar a aplicação
      const result = await notificationsService.fetchUnreadCount({ silent: true })
      console.log('✅ Modo silencioso funcionou. Resultado:', result)
      return true
    } catch (error) {
      console.error('❌ Erro no modo silencioso (não deveria acontecer):', error)
      return false
    }
  },

  /**
   * Testa o fetchUnreadCount em modo normal
   */
  async testNormalMode() {
    console.log('🧪 Testando modo normal do fetchUnreadCount...')
    
    try {
      const result = await notificationsService.fetchUnreadCount({ silent: false })
      console.log('✅ Modo normal funcionou. Resultado:', result)
      return true
    } catch (error) {
      console.log('⚠️ Erro no modo normal (esperado se houver problemas no servidor):', error.message)
      return false
    }
  },

  /**
   * Executa todos os testes
   */
  async runAllTests() {
    console.log('🚀 Iniciando testes do sistema de notificações...')
    
    const results = {
      silentMode: await this.testSilentMode(),
      normalMode: await this.testNormalMode()
    }
    
    console.log('📊 Resultados dos testes:', results)
    return results
  }
}

// Para usar no console do navegador:
// import { testNotificationsErrorHandling } from '@/utils/notificationsErrorTest'
// testNotificationsErrorHandling.runAllTests()

export default testNotificationsErrorHandling
