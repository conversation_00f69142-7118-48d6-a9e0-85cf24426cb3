<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Tabela de categorias de serviços/produtos
        Schema::create('categorias_servicos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('clinica_id');
            $table->string('nome');
            $table->text('descricao')->nullable();
            $table->string('cor', 7)->default('#007bff');
            $table->boolean('ativo')->default(true);
            $table->timestamps();

            $table->foreign('clinica_id')->references('id')->on('clinicas')->onDelete('cascade');
            $table->index(['clinica_id', 'ativo']);
        });

        // Tabela de serviços/produtos da clínica
        Schema::create('servicos_produtos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('clinica_id');
            $table->unsignedBigInteger('categoria_id')->nullable();
            $table->string('codigo', 50)->nullable();
            $table->string('nome');
            $table->text('descricao')->nullable();
            $table->enum('tipo', ['servico', 'produto', 'procedimento'])->default('servico');
            $table->decimal('valor_base', 10, 2)->default(0.00);
            $table->decimal('valor_minimo', 10, 2)->nullable();
            $table->decimal('valor_maximo', 10, 2)->nullable();
            $table->string('unidade', 20)->default('un');
            $table->integer('tempo_estimado')->nullable()->comment('Tempo em minutos');
            $table->text('observacoes')->nullable();
            $table->boolean('ativo')->default(true);
            $table->timestamps();

            $table->foreign('clinica_id')->references('id')->on('clinicas')->onDelete('cascade');
            $table->foreign('categoria_id')->references('id')->on('categorias_servicos')->onDelete('set null');
            $table->unique(['clinica_id', 'codigo']);
            $table->index(['clinica_id', 'ativo']);
            $table->index('tipo');
            $table->index('nome');
        });

        // Tabela de orçamentos
        Schema::create('orcamentos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('clinica_id');
            $table->unsignedBigInteger('paciente_id')->nullable();
            $table->unsignedBigInteger('dentista_id')->nullable();
            $table->string('numero', 50)->nullable();
            $table->string('titulo');
            $table->text('descricao')->nullable();
            $table->date('data_orcamento');
            $table->date('data_validade')->nullable();
            $table->decimal('valor_total', 10, 2)->default(0.00);
            $table->decimal('desconto_percentual', 5, 2)->default(0.00);
            $table->decimal('desconto_valor', 10, 2)->default(0.00);
            $table->decimal('valor_final', 10, 2)->default(0.00);
            $table->enum('status', ['rascunho', 'enviado', 'aprovado', 'rejeitado', 'expirado', 'convertido'])->default('rascunho');
            $table->text('observacoes')->nullable();
            $table->text('observacoes_internas')->nullable();
            $table->timestamp('aprovado_em')->nullable();
            $table->string('aprovado_por')->nullable();
            $table->timestamp('convertido_em')->nullable();
            $table->unsignedBigInteger('criado_por')->nullable();
            $table->timestamps();

            $table->foreign('clinica_id')->references('id')->on('clinicas')->onDelete('cascade');
            $table->foreign('paciente_id')->references('id')->on('pacientes')->onDelete('set null');
            $table->foreign('dentista_id')->references('id')->on('dentistas')->onDelete('set null');
            $table->foreign('criado_por')->references('id')->on('users')->onDelete('set null');
            $table->unique(['clinica_id', 'numero']);
            $table->index(['clinica_id', 'status']);
            $table->index('data_orcamento');
            $table->index('data_validade');
            $table->index(['data_orcamento', 'status']);
        });

        // Tabela de itens do orçamento
        Schema::create('orcamento_itens', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('orcamento_id');
            $table->unsignedBigInteger('servico_produto_id')->nullable();
            $table->string('nome');
            $table->text('descricao')->nullable();
            $table->decimal('quantidade', 8, 2)->default(1.00);
            $table->decimal('valor_unitario', 10, 2);
            $table->decimal('desconto_percentual', 5, 2)->default(0.00);
            $table->decimal('desconto_valor', 10, 2)->default(0.00);
            $table->decimal('valor_total', 10, 2);
            $table->text('observacoes')->nullable();
            $table->integer('ordem')->default(0);
            $table->timestamps();

            $table->foreign('orcamento_id')->references('id')->on('orcamentos')->onDelete('cascade');
            $table->foreign('servico_produto_id')->references('id')->on('servicos_produtos')->onDelete('set null');
            $table->index('ordem');
            $table->index('valor_total');
        });

        // Tabela de templates de orçamento
        Schema::create('orcamento_templates', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('clinica_id');
            $table->string('nome');
            $table->text('descricao')->nullable();
            $table->string('categoria', 100)->nullable();
            $table->boolean('ativo')->default(true);
            $table->timestamps();

            $table->foreign('clinica_id')->references('id')->on('clinicas')->onDelete('cascade');
            $table->index('categoria');
        });

        // Tabela de itens dos templates
        Schema::create('orcamento_template_itens', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('template_id');
            $table->unsignedBigInteger('servico_produto_id')->nullable();
            $table->string('nome');
            $table->text('descricao')->nullable();
            $table->decimal('quantidade', 8, 2)->default(1.00);
            $table->decimal('valor_unitario', 10, 2);
            $table->text('observacoes')->nullable();
            $table->integer('ordem')->default(0);
            $table->timestamps();

            $table->foreign('template_id')->references('id')->on('orcamento_templates')->onDelete('cascade');
            $table->foreign('servico_produto_id')->references('id')->on('servicos_produtos')->onDelete('set null');
            $table->index('ordem');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orcamento_template_itens');
        Schema::dropIfExists('orcamento_templates');
        Schema::dropIfExists('orcamento_itens');
        Schema::dropIfExists('orcamentos');
        Schema::dropIfExists('servicos_produtos');
        Schema::dropIfExists('categorias_servicos');
    }
};
