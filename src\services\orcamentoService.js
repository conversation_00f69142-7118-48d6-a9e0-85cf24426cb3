import axios from '@/services/axios';

export const orcamentoService = {
  /**
   * Obter todos os orçamentos com filtros
   */
  async getOrcamentos(filters = {}) {
    const params = new URLSearchParams();
    
    if (filters.paciente_id) params.append('paciente_id', filters.paciente_id);
    if (filters.status) params.append('status', filters.status);
    if (filters.data_inicio) params.append('data_inicio', filters.data_inicio);
    if (filters.data_fim) params.append('data_fim', filters.data_fim);
    if (filters.dentista_id) params.append('dentista_id', filters.dentista_id);
    if (filters.page) params.append('page', filters.page);

    return axios.get(`/orcamentos?${params.toString()}`);
  },

  /**
   * Obter orçamento específico
   */
  async getOrcamento(id) {
    return axios.get(`/orcamentos/${id}`);
  },

  /**
   * Criar novo orçamento
   */
  async createOrcamento(data) {
    return axios.post('/orcamentos', data);
  },

  /**
   * Atualizar orçamento
   */
  async updateOrcamento(id, data) {
    return axios.put(`/orcamentos/${id}`, data);
  },

  /**
   * Excluir orçamento
   */
  async deleteOrcamento(id) {
    return axios.delete(`/orcamentos/${id}`);
  },

  /**
   * Enviar orçamento
   */
  async enviarOrcamento(id) {
    return axios.post(`/orcamentos/${id}/enviar`);
  },

  /**
   * Aprovar orçamento
   */
  async aprovarOrcamento(id, aprovadoPor = null) {
    return axios.post(`/orcamentos/${id}/aprovar`, { aprovado_por: aprovadoPor });
  },

  /**
   * Converter orçamento em fatura
   */
  async converterParaFatura(id) {
    return axios.post(`/orcamentos/${id}/converter-para-fatura`);
  },

  /**
   * Duplicar orçamento
   */
  async duplicarOrcamento(id) {
    return axios.post(`/orcamentos/${id}/duplicar`);
  },

  /**
   * Obter orçamentos de um paciente específico
   */
  async getOrcamentosPaciente(pacienteId) {
    return axios.get(`/orcamentos/paciente/${pacienteId}`);
  },

  /**
   * Obter estatísticas de orçamentos
   */
  async getEstatisticas(filters = {}) {
    const params = new URLSearchParams();

    if (filters.data_inicio) params.append('data_inicio', filters.data_inicio);
    if (filters.data_fim) params.append('data_fim', filters.data_fim);

    return axios.get(`/orcamentos/estatisticas?${params.toString()}`);
  },

  /**
   * Validar dados de orçamento
   */
  validateOrcamentoData(data) {
    const errors = {};

    if (!data.titulo || data.titulo.trim() === '') {
      errors.titulo = 'Título é obrigatório';
    }

    if (!data.itens || data.itens.length === 0) {
      errors.itens = 'Pelo menos um item é obrigatório';
    } else {
      data.itens.forEach((item, index) => {
        if (!item.nome || item.nome.trim() === '') {
          errors[`itens.${index}.nome`] = 'Nome do item é obrigatório';
        }

        if (!item.quantidade || item.quantidade <= 0) {
          errors[`itens.${index}.quantidade`] = 'Quantidade deve ser maior que zero';
        }

        if (!item.valor_unitario || item.valor_unitario <= 0) {
          errors[`itens.${index}.valor_unitario`] = 'Valor unitário deve ser maior que zero';
        }

        if (item.desconto_percentual && (item.desconto_percentual < 0 || item.desconto_percentual > 100)) {
          errors[`itens.${index}.desconto_percentual`] = 'Percentual de desconto deve estar entre 0 e 100';
        }
      });
    }

    if (data.data_validade && new Date(data.data_validade) <= new Date()) {
      errors.data_validade = 'Data de validade deve ser futura';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },

  /**
   * Calcular valor total de um item
   */
  calculateItemTotal(quantidade, valorUnitario, descontoPercentual = 0, descontoValor = 0) {
    const valorBruto = parseFloat(quantidade) * parseFloat(valorUnitario);
    let valorDesconto = parseFloat(descontoValor) || 0;

    if (valorDesconto === 0 && descontoPercentual > 0) {
      valorDesconto = (valorBruto * parseFloat(descontoPercentual)) / 100;
    }

    return Math.max(0, valorBruto - valorDesconto);
  },

  /**
   * Calcular valor total do orçamento
   */
  calculateOrcamentoTotal(itens, descontoPercentual = 0, descontoValor = 0) {
    const valorItens = itens.reduce((sum, item) => {
      return sum + this.calculateItemTotal(
        item.quantidade,
        item.valor_unitario,
        item.desconto_percentual,
        item.desconto_valor
      );
    }, 0);

    let valorDesconto = parseFloat(descontoValor) || 0;

    if (valorDesconto === 0 && descontoPercentual > 0) {
      valorDesconto = (valorItens * parseFloat(descontoPercentual)) / 100;
    }

    return {
      valor_itens: valorItens,
      valor_desconto: valorDesconto,
      valor_final: Math.max(0, valorItens - valorDesconto)
    };
  },

  /**
   * Formatar valor monetário
   */
  formatCurrency(value) {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0);
  },

  /**
   * Formatar data
   */
  formatDate(date) {
    if (!date) return '';
    return new Date(date).toLocaleDateString('pt-BR');
  },

  /**
   * Obter status badge class
   */
  getStatusBadgeClass(status) {
    const statusClasses = {
      'rascunho': 'badge-secondary',
      'enviado': 'badge-info',
      'aprovado': 'badge-success',
      'rejeitado': 'badge-danger',
      'expirado': 'badge-warning',
      'convertido': 'badge-primary'
    };
    
    return statusClasses[status] || 'badge-secondary';
  },

  /**
   * Obter texto do status
   */
  getStatusText(status) {
    const statusTexts = {
      'rascunho': 'Rascunho',
      'enviado': 'Enviado',
      'aprovado': 'Aprovado',
      'rejeitado': 'Rejeitado',
      'expirado': 'Expirado',
      'convertido': 'Convertido'
    };
    
    return statusTexts[status] || 'Desconhecido';
  },

  /**
   * Verificar se orçamento pode ser editado
   */
  canEdit(orcamento) {
    return orcamento.status === 'rascunho';
  },

  /**
   * Verificar se orçamento pode ser enviado
   */
  canSend(orcamento) {
    return orcamento.status === 'rascunho' && orcamento.itens && orcamento.itens.length > 0;
  },

  /**
   * Verificar se orçamento pode ser aprovado
   */
  canApprove(orcamento) {
    const isEnviado = orcamento.status === 'enviado';
    const naoExpirado = !orcamento.data_validade || new Date(orcamento.data_validade) >= new Date();
    return isEnviado && naoExpirado;
  },

  /**
   * Verificar se orçamento pode ser convertido
   */
  canConvert(orcamento) {
    return orcamento.status === 'aprovado';
  },

  /**
   * Verificar se orçamento está expirado
   */
  isExpired(orcamento) {
    if (!orcamento.data_validade) return false;
    return new Date(orcamento.data_validade) < new Date();
  },

  /**
   * Obter dias para vencimento
   */
  getDaysToExpiration(orcamento) {
    if (!orcamento.data_validade) return null;
    
    const today = new Date();
    const expiration = new Date(orcamento.data_validade);
    const diffTime = expiration - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  },

  /**
   * Criar item vazio para orçamento
   */
  createEmptyItem() {
    return {
      servico_produto_id: null,
      nome: '',
      descricao: '',
      quantidade: 1,
      valor_unitario: 0,
      desconto_percentual: 0,
      desconto_valor: 0,
      observacoes: '',
      ordem: 0
    };
  },

  /**
   * Criar orçamento vazio
   */
  createEmptyOrcamento() {
    return {
      paciente_id: null,
      dentista_id: null,
      titulo: '',
      descricao: '',
      data_validade: null,
      observacoes: '',
      itens: [this.createEmptyItem()]
    };
  }
};

export default orcamentoService;
