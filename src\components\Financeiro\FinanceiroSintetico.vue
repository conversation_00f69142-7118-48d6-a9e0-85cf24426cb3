<template>
  <div class="financeiro-sintetico">
    <!-- Cards de Estatísticas Principais -->
    <div class="row mb-4">
      <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
        <div class="card">
          <div class="card-body p-3">
            <div class="row">
              <div class="col-8">
                <div class="numbers">
                  <p class="text-sm mb-0 text-capitalize font-weight-bold">Receita do Mês</p>
                  <h5 class="font-weight-bolder mb-0">
                    {{ formatCurrency(estatisticas.receita_mes || 0) }}
                    <span class="text-success text-sm font-weight-bolder">+15%</span>
                  </h5>
                </div>
              </div>
              <div class="col-4 text-end">
                <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md">
                  <i class="ni ni-money-coins text-lg opacity-10" aria-hidden="true"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
        <div class="card">
          <div class="card-body p-3">
            <div class="row">
              <div class="col-8">
                <div class="numbers">
                  <p class="text-sm mb-0 text-capitalize font-weight-bold">Faturas Pendentes</p>
                  <h5 class="font-weight-bolder mb-0">
                    {{ estatisticas.quantidade_pendente || 0 }}
                    <span class="text-warning text-sm font-weight-bolder">
                      {{ formatCurrency(estatisticas.total_pendente || 0) }}
                    </span>
                  </h5>
                </div>
              </div>
              <div class="col-4 text-end">
                <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md">
                  <i class="ni ni-paper-diploma text-lg opacity-10" aria-hidden="true"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
        <div class="card">
          <div class="card-body p-3">
            <div class="row">
              <div class="col-8">
                <div class="numbers">
                  <p class="text-sm mb-0 text-capitalize font-weight-bold">Faturas Vencidas</p>
                  <h5 class="font-weight-bolder mb-0">
                    {{ estatisticas.quantidade_vencida || 0 }}
                    <span class="text-danger text-sm font-weight-bolder">
                      {{ formatCurrency(estatisticas.total_vencido || 0) }}
                    </span>
                  </h5>
                </div>
              </div>
              <div class="col-4 text-end">
                <div class="icon icon-shape bg-gradient-danger shadow text-center border-radius-md">
                  <i class="ni ni-world text-lg opacity-10" aria-hidden="true"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-xl-3 col-sm-6">
        <div class="card">
          <div class="card-body p-3">
            <div class="row">
              <div class="col-8">
                <div class="numbers">
                  <p class="text-sm mb-0 text-capitalize font-weight-bold">Taxa de Inadimplência</p>
                  <h5 class="font-weight-bolder mb-0">
                    {{ calculateInadimplenciaRate() }}%
                    <span class="text-success text-sm font-weight-bolder">-2%</span>
                  </h5>
                </div>
              </div>
              <div class="col-4 text-end">
                <div class="icon icon-shape bg-gradient-success shadow text-center border-radius-md">
                  <i class="ni ni-chart-bar-32 text-lg opacity-10" aria-hidden="true"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Gráficos -->
    <div class="row mb-4">
      <div class="col-lg-7 mb-lg-0 mb-4">
        <div class="card z-index-2">
          <div class="card-header pb-0">
            <h6>Receitas Mensais</h6>
            <p class="text-sm">
              <i class="fa fa-arrow-up text-success"></i>
              <span class="font-weight-bold">+5% a mais</span> em relação ao mês anterior
            </p>
          </div>
          <div class="card-body p-3">
            <div class="chart">
              <div class="line-chart">
                <div class="chart-bars">
                  <div
                    v-for="(valor, index) in dadosGraficoLinha.data"
                    :key="index"
                    class="chart-bar"
                    :style="{ height: getBarHeight(valor, Math.max(...dadosGraficoLinha.data)) + '%' }"
                    :title="formatCurrency(valor)"
                  >
                    <div class="bar-value">{{ formatCurrency(valor) }}</div>
                  </div>
                </div>
                <div class="chart-labels">
                  <div v-for="label in dadosGraficoLinha.labels" :key="label" class="chart-label">
                    {{ label }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-5">
        <div class="card">
          <div class="card-header pb-0 p-3">
            <h6 class="mb-0">Distribuição por Status</h6>
          </div>
          <div class="card-body p-3">
            <div class="chart">
              <div class="pie-chart">
                <div class="pie-chart-legend">
                  <div
                    v-for="item in legendItemsFiltered"
                    :key="item.index"
                    class="legend-item"
                  >
                    <div
                      class="legend-color"
                      :style="{ backgroundColor: item.color }"
                    ></div>
                    <span class="legend-label">{{ item.label }}</span>
                    <span class="legend-value">{{ item.valor }}</span>
                  </div>
                </div>
                <div class="pie-chart-visual">
                  <div class="pie-chart-center">
                    <div class="pie-total">{{ faturas.length }}</div>
                    <div class="pie-label">Total</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabelas de Resumo -->
    <div class="row">
      <div class="col-lg-7 mb-lg-0 mb-4">
        <div class="card">
          <div class="card-header pb-0 p-3">
            <div class="d-flex justify-content-between">
              <h6 class="mb-2">Próximos Vencimentos</h6>
            </div>
          </div>
          <div class="table-responsive">
            <table class="table align-items-center">
              <tbody>
                <tr v-for="fatura in proximosVencimentos" :key="fatura.id">
                  <td class="w-30">
                    <div class="d-flex px-2 py-1 align-items-center">
                      <div class="ms-4">
                        <p class="text-xs font-weight-bold mb-0">{{ fatura.paciente?.nome }}</p>
                        <h6 class="text-sm mb-0">{{ fatura.descricao }}</h6>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="text-center">
                      <p class="text-xs font-weight-bold mb-0">Valor:</p>
                      <h6 class="text-sm mb-0">{{ formatCurrency(fatura.valor_final) }}</h6>
                    </div>
                  </td>
                  <td>
                    <div class="text-center">
                      <p class="text-xs font-weight-bold mb-0">Vencimento:</p>
                      <h6 class="text-sm mb-0">{{ formatDate(fatura.data_vencimento) }}</h6>
                    </div>
                  </td>
                  <td class="align-middle text-sm">
                    <div class="col text-center">
                      <span class="badge badge-sm" :class="getStatusBadgeClass(fatura.status)">
                        {{ getStatusText(fatura.status) }}
                      </span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="col-lg-5">
        <div class="card">
          <div class="card-header pb-0 p-3">
            <h6 class="mb-0">Últimos Pagamentos</h6>
          </div>
          <div class="card-body p-3">
            <ul class="list-group">
              <li class="list-group-item border-0 d-flex justify-content-between ps-0 mb-2 border-radius-lg" 
                  v-for="pagamento in ultimosPagamentos" :key="pagamento.id">
                <div class="d-flex align-items-center">
                  <div class="icon icon-shape icon-sm me-3 bg-gradient-success shadow text-center">
                    <i class="ni ni-check-bold text-white opacity-10"></i>
                  </div>
                  <div class="d-flex flex-column">
                    <h6 class="mb-1 text-dark text-sm">{{ pagamento.paciente?.nome }}</h6>
                    <span class="text-xs">{{ formatDate(pagamento.data_pagamento) }}</span>
                  </div>
                </div>
                <div class="d-flex align-items-center text-success text-gradient text-sm font-weight-bold">
                  {{ formatCurrency(pagamento.valor_final) }}
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Métricas Adicionais -->
    <div class="row mt-4">
      <div class="col-lg-4 col-md-6 mt-4">
        <div class="card">
          <div class="card-body">
            <h6 class="mb-0">Ticket Médio</h6>
            <p class="text-sm">{{ formatCurrency(calculateTicketMedio()) }}</p>
            <div class="progress">
              <div class="progress-bar bg-gradient-success" role="progressbar" style="width: 60%"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-4 col-md-6 mt-4">
        <div class="card">
          <div class="card-body">
            <h6 class="mb-0">Tempo Médio de Recebimento</h6>
            <p class="text-sm">{{ calculateTempoMedioRecebimento() }} dias</p>
            <div class="progress">
              <div class="progress-bar bg-gradient-info" role="progressbar" style="width: 45%"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-4 col-md-6 mt-4">
        <div class="card">
          <div class="card-body">
            <h6 class="mb-0">Crescimento Mensal</h6>
            <p class="text-sm">+{{ calculateCrescimentoMensal() }}%</p>
            <div class="progress">
              <div class="progress-bar bg-gradient-warning" role="progressbar" style="width: 75%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { financeiroService } from '@/services/financeiroService';

export default {
  name: 'FinanceiroSintetico',
  props: {
    estatisticas: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    },
    faturas: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chartLine: null,
      chartPie: null
    };
  },
  computed: {
    proximosVencimentos() {
      const hoje = new Date();
      const proximosDias = new Date();
      proximosDias.setDate(hoje.getDate() + 30); // Próximos 30 dias

      return this.faturas
        .filter(fatura => {
          const vencimento = new Date(fatura.data_vencimento);
          return fatura.status === 'pendente' && vencimento >= hoje && vencimento <= proximosDias;
        })
        .sort((a, b) => new Date(a.data_vencimento) - new Date(b.data_vencimento))
        .slice(0, 5); // Mostrar apenas os 5 próximos
    },

    ultimosPagamentos() {
      return this.faturas
        .filter(fatura => fatura.status === 'pago' && fatura.data_pagamento)
        .sort((a, b) => new Date(b.data_pagamento) - new Date(a.data_pagamento))
        .slice(0, 5); // Mostrar apenas os 5 últimos
    },

    dadosGraficoPizza() {
      const statusCount = {
        pendente: 0,
        pago: 0,
        vencido: 0,
        cancelado: 0
      };

      this.faturas.forEach(fatura => {
        if (statusCount.hasOwnProperty(fatura.status)) {
          statusCount[fatura.status]++;
        }
      });

      return {
        labels: ['Pendente', 'Pago', 'Vencido', 'Cancelado'],
        data: [statusCount.pendente, statusCount.pago, statusCount.vencido, statusCount.cancelado],
        colors: ['#fb6340', '#2dce89', '#f5365c', '#8898aa']
      };
    },

    dadosGraficoLinha() {
      // Agrupar faturas por mês dos últimos 6 meses
      const meses = [];
      const valores = [];
      const hoje = new Date();

      for (let i = 5; i >= 0; i--) {
        const data = new Date(hoje.getFullYear(), hoje.getMonth() - i, 1);
        const mesAno = data.toLocaleDateString('pt-BR', { month: 'short', year: 'numeric' });
        meses.push(mesAno);

        const valorMes = this.faturas
          .filter(fatura => {
            const faturaData = new Date(fatura.data_emissao);
            return faturaData.getMonth() === data.getMonth() &&
                   faturaData.getFullYear() === data.getFullYear() &&
                   fatura.status === 'pago';
          })
          .reduce((sum, fatura) => sum + parseFloat(fatura.valor_final || 0), 0);

        valores.push(valorMes);
      }

      return { labels: meses, data: valores };
    },

    legendItemsFiltered() {
      return this.dadosGraficoPizza.data
        .map((valor, index) => ({
          valor,
          label: this.dadosGraficoPizza.labels[index],
          color: this.dadosGraficoPizza.colors[index],
          index
        }))
        .filter(item => item.valor > 0);
    }
  },
  methods: {
    formatCurrency: financeiroService.formatCurrency,
    formatDate: financeiroService.formatDate,
    getStatusBadgeClass: financeiroService.getStatusBadgeClass,
    getStatusText: financeiroService.getStatusText,

    calculateInadimplenciaRate() {
      const total = this.estatisticas.total_geral || 0;
      const vencido = this.estatisticas.total_vencido || 0;
      
      if (total === 0) return 0;
      return ((vencido / total) * 100).toFixed(1);
    },

    calculateTicketMedio() {
      const totalFaturas = (this.estatisticas.quantidade_paga || 0) + 
                          (this.estatisticas.quantidade_pendente || 0) + 
                          (this.estatisticas.quantidade_vencida || 0);
      const totalValor = this.estatisticas.total_geral || 0;
      
      if (totalFaturas === 0) return 0;
      return totalValor / totalFaturas;
    },

    calculateTempoMedioRecebimento() {
      const faturasPagas = this.faturas.filter(f => f.status === 'pago' && f.data_pagamento);

      if (faturasPagas.length === 0) return 0;

      const totalDias = faturasPagas.reduce((sum, fatura) => {
        const emissao = new Date(fatura.data_emissao);
        const pagamento = new Date(fatura.data_pagamento);
        const dias = Math.ceil((pagamento - emissao) / (1000 * 60 * 60 * 24));
        return sum + dias;
      }, 0);

      return Math.round(totalDias / faturasPagas.length);
    },

    calculateCrescimentoMensal() {
      const hoje = new Date();
      const mesAtual = new Date(hoje.getFullYear(), hoje.getMonth(), 1);
      const mesAnterior = new Date(hoje.getFullYear(), hoje.getMonth() - 1, 1);

      const receitaMesAtual = this.faturas
        .filter(f => {
          const data = new Date(f.data_emissao);
          return data >= mesAtual && f.status === 'pago';
        })
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);

      const receitaMesAnterior = this.faturas
        .filter(f => {
          const data = new Date(f.data_emissao);
          return data >= mesAnterior && data < mesAtual && f.status === 'pago';
        })
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);

      if (receitaMesAnterior === 0) return 0;

      const crescimento = ((receitaMesAtual - receitaMesAnterior) / receitaMesAnterior) * 100;
      return Math.round(crescimento * 10) / 10; // Arredondar para 1 casa decimal
    },

    getBarHeight(valor, maxValor) {
      if (maxValor === 0) return 0;
      return Math.max((valor / maxValor) * 100, 5); // Mínimo de 5% para visibilidade
    },

    async loadDashboardData() {
      try {
        // TODO: Carregar dados específicos do dashboard
        // const response = await financeiroService.getDashboardData();
        // this.proximosVencimentos = response.data.proximos_vencimentos;
        // this.ultimosPagamentos = response.data.ultimos_pagamentos;
      } catch (error) {
        console.error('Erro ao carregar dados do dashboard:', error);
      }
    },

    initCharts() {
      // TODO: Implementar gráficos usando Chart.js ou similar
      this.$nextTick(() => {
        this.initLineChart();
        this.initPieChart();
      });
    },

    initLineChart() {
      // TODO: Implementar gráfico de linha para receitas mensais
    },

    initPieChart() {
      // TODO: Implementar gráfico de pizza para distribuição por status
    }
  },

  mounted() {
    this.loadDashboardData();
    this.initCharts();
  },

  watch: {
    estatisticas: {
      handler() {
        this.initCharts();
      },
      deep: true
    }
  }
};
</script>

<style scoped>
.financeiro-sintetico .card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.icon-shape {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bg-gradient-primary {
  background: linear-gradient(87deg, #5e72e4 0, #825ee4 100%);
}

.bg-gradient-warning {
  background: linear-gradient(87deg, #fb6340 0, #fbb140 100%);
}

.bg-gradient-danger {
  background: linear-gradient(87deg, #f5365c 0, #f56036 100%);
}

.bg-gradient-success {
  background: linear-gradient(87deg, #2dce89 0, #2dcecc 100%);
}

.progress {
  height: 6px;
}

.list-group-item {
  background-color: transparent;
}

/* Gráfico de Linha (Barras) */
.line-chart {
  height: 300px;
  display: flex;
  flex-direction: column;
}

.chart-bars {
  flex: 1;
  display: flex;
  align-items: end;
  justify-content: space-around;
  padding: 20px 0;
  gap: 10px;
}

.chart-bar {
  background: linear-gradient(135deg, #5e72e4 0%, #825ee4 100%);
  border-radius: 4px 4px 0 0;
  min-height: 20px;
  flex: 1;
  max-width: 60px;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
}

.chart-bar:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(94, 114, 228, 0.3);
}

.bar-value {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.7rem;
  font-weight: 600;
  color: #344767;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chart-bar:hover .bar-value {
  opacity: 1;
}

.chart-labels {
  display: flex;
  justify-content: space-around;
  padding-top: 10px;
  border-top: 1px solid #e9ecef;
}

.chart-label {
  font-size: 0.75rem;
  color: #8898aa;
  text-align: center;
  flex: 1;
}

/* Gráfico de Pizza (Legenda) */
.pie-chart {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 300px;
}

.pie-chart-legend {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.legend-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-label {
  flex: 1;
  font-size: 0.85rem;
  color: #344767;
  font-weight: 500;
}

.legend-value {
  font-size: 0.85rem;
  font-weight: 600;
  color: #5e72e4;
  background: rgba(94, 114, 228, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  min-width: 30px;
  text-align: center;
}

.pie-chart-visual {
  flex: 0 0 150px;
  height: 150px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-chart-center {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #5e72e4 0%, #825ee4 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 15px rgba(94, 114, 228, 0.3);
}

.pie-total {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.pie-label {
  font-size: 0.8rem;
  opacity: 0.9;
  margin-top: 2px;
}
</style>
