import notificationsService from '@/services/notificationsService'
import store from '@/store'

/**
 * Utilitário para testar o sistema de notificações
 */
class NotificationsTest {
  constructor() {
    this.testResults = []
  }

  /**
   * Executar todos os testes
   */
  async runAllTests() {
    console.log('🧪 Iniciando testes do sistema de notificações...')
    
    this.testResults = []
    
    try {
      await this.testStoreInitialization()
      await this.testServiceMethods()
      await this.testPollingSystem()
      await this.testErrorHandling()
      
      this.printResults()
      return this.testResults.every(result => result.passed)
      
    } catch (error) {
      console.error('❌ Erro durante execução dos testes:', error)
      return false
    }
  }

  /**
   * Testar inicialização do store
   */
  async testStoreInitialization() {
    const testName = 'Store Initialization'
    
    try {
      // Verificar se o módulo foi registrado
      const hasModule = store.hasModule('notifications')
      this.addResult(testName, 'Module registered', hasModule)
      
      // Verificar estado inicial
      const initialState = store.state.notifications
      this.addResult(testName, 'Initial state exists', !!initialState)
      this.addResult(testName, 'Notifications array initialized', Array.isArray(initialState.notifications))
      this.addResult(testName, 'Unread count initialized', typeof initialState.unreadCount === 'number')
      
      // Verificar getters
      const allNotifications = store.getters['notifications/allNotifications']
      const unreadCount = store.getters['notifications/unreadCount']
      this.addResult(testName, 'Getters working', Array.isArray(allNotifications) && typeof unreadCount === 'number')
      
    } catch (error) {
      this.addResult(testName, 'Store initialization', false, error.message)
    }
  }

  /**
   * Testar métodos do service
   */
  async testServiceMethods() {
    const testName = 'Service Methods'
    
    try {
      // Testar se os métodos existem
      const methods = [
        'init', 'fetchNotifications', 'fetchUnreadCount', 
        'markAsRead', 'markAllAsRead', 'deleteNotification',
        'startPolling', 'stopPolling', 'hasUnreadNotifications',
        'getUnreadCount', 'getAllNotifications'
      ]
      
      methods.forEach(method => {
        const exists = typeof notificationsService[method] === 'function'
        this.addResult(testName, `Method ${method} exists`, exists)
      })
      
      // Testar getters básicos
      const hasUnread = notificationsService.hasUnreadNotifications()
      const unreadCount = notificationsService.getUnreadCount()
      const allNotifications = notificationsService.getAllNotifications()
      
      this.addResult(testName, 'hasUnreadNotifications returns boolean', typeof hasUnread === 'boolean')
      this.addResult(testName, 'getUnreadCount returns number', typeof unreadCount === 'number')
      this.addResult(testName, 'getAllNotifications returns array', Array.isArray(allNotifications))
      
    } catch (error) {
      this.addResult(testName, 'Service methods', false, error.message)
    }
  }

  /**
   * Testar sistema de polling
   */
  async testPollingSystem() {
    const testName = 'Polling System'
    
    try {
      // Testar início do polling
      notificationsService.startPolling(5000)
      const isPolling = notificationsService.isPolling
      this.addResult(testName, 'Polling starts', isPolling)
      
      // Aguardar um pouco
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // Testar parada do polling
      notificationsService.stopPolling()
      const isStoppedPolling = !notificationsService.isPolling
      this.addResult(testName, 'Polling stops', isStoppedPolling)
      
    } catch (error) {
      this.addResult(testName, 'Polling system', false, error.message)
    }
  }

  /**
   * Testar tratamento de erros
   */
  async testErrorHandling() {
    const testName = 'Error Handling'
    
    try {
      // Testar clearError
      store.commit('notifications/SET_ERROR', 'Test error')
      const hasError = store.getters['notifications/error']
      this.addResult(testName, 'Error can be set', !!hasError)
      
      notificationsService.clearError()
      const errorCleared = !store.getters['notifications/error']
      this.addResult(testName, 'Error can be cleared', errorCleared)
      
      // Testar formatação de data
      const formattedDate = notificationsService.formatNotificationDate(new Date().toISOString())
      this.addResult(testName, 'Date formatting works', typeof formattedDate === 'string')
      
      // Testar ícones e cores
      const icon = notificationsService.getNotificationIcon('info')
      const color = notificationsService.getNotificationColor('success')
      this.addResult(testName, 'Icon helper works', typeof icon === 'string')
      this.addResult(testName, 'Color helper works', typeof color === 'string')
      
    } catch (error) {
      this.addResult(testName, 'Error handling', false, error.message)
    }
  }

  /**
   * Adicionar resultado de teste
   */
  addResult(category, test, passed, error = null) {
    this.testResults.push({
      category,
      test,
      passed,
      error
    })
  }

  /**
   * Imprimir resultados dos testes
   */
  printResults() {
    console.log('\n📊 Resultados dos testes:')
    console.log('=' .repeat(50))
    
    const categories = [...new Set(this.testResults.map(r => r.category))]
    
    categories.forEach(category => {
      console.log(`\n📁 ${category}:`)
      
      const categoryResults = this.testResults.filter(r => r.category === category)
      const passed = categoryResults.filter(r => r.passed).length
      const total = categoryResults.length
      
      categoryResults.forEach(result => {
        const icon = result.passed ? '✅' : '❌'
        const error = result.error ? ` (${result.error})` : ''
        console.log(`  ${icon} ${result.test}${error}`)
      })
      
      console.log(`  📈 ${passed}/${total} testes passaram`)
    })
    
    const totalPassed = this.testResults.filter(r => r.passed).length
    const totalTests = this.testResults.length
    const percentage = Math.round((totalPassed / totalTests) * 100)
    
    console.log('\n' + '='.repeat(50))
    console.log(`🎯 Resultado geral: ${totalPassed}/${totalTests} (${percentage}%)`)
    
    if (percentage === 100) {
      console.log('🎉 Todos os testes passaram!')
    } else if (percentage >= 80) {
      console.log('⚠️  Alguns testes falharam, mas a maioria passou.')
    } else {
      console.log('🚨 Muitos testes falharam. Verifique a implementação.')
    }
  }

  /**
   * Testar criação de notificação de exemplo (apenas para desenvolvimento)
   */
  async testCreateSampleNotification() {
    if (process.env.NODE_ENV !== 'development') {
      console.warn('Teste de criação de notificação só funciona em desenvolvimento')
      return false
    }

    try {
      const sampleNotification = {
        title: 'Teste de Notificação',
        message: 'Esta é uma notificação de teste criada pelo sistema de testes.',
        type: 'info',
        user_id: 1 // Assumindo que existe um usuário com ID 1
      }

      const result = await notificationsService.createNotification(sampleNotification)
      console.log('✅ Notificação de teste criada:', result)
      return true

    } catch (error) {
      console.error('❌ Erro ao criar notificação de teste:', error)
      return false
    }
  }

  /**
   * Executar teste de performance básico
   */
  async testPerformance() {
    console.log('⚡ Testando performance...')
    
    const iterations = 100
    const startTime = performance.now()
    
    // Testar operações básicas
    for (let i = 0; i < iterations; i++) {
      notificationsService.hasUnreadNotifications()
      notificationsService.getUnreadCount()
      notificationsService.getAllNotifications()
      notificationsService.formatNotificationDate(new Date().toISOString())
    }
    
    const endTime = performance.now()
    const avgTime = (endTime - startTime) / iterations
    
    console.log(`⚡ Performance: ${avgTime.toFixed(2)}ms por operação (${iterations} iterações)`)
    
    return avgTime < 1 // Deve ser menor que 1ms por operação
  }
}

// Exportar instância para uso
export default new NotificationsTest()

// Função helper para executar testes rapidamente no console
export function runNotificationTests() {
  const tester = new NotificationsTest()
  return tester.runAllTests()
}

// Função para testar performance
export function testNotificationPerformance() {
  const tester = new NotificationsTest()
  return tester.testPerformance()
}

// Função para criar notificação de teste
export function createTestNotification() {
  const tester = new NotificationsTest()
  return tester.testCreateSampleNotification()
}
