# Sistema Global de Notificações

Este documento descreve o sistema global de notificações implementado no aplicativo Lumi.

## Visão Geral

O sistema de notificações permite:
- Exibir notificações em tempo real para usuários
- Gerenciar estado de leitura das notificações
- Polling automático para verificar novas notificações
- Interface intuitiva com dropdown na navbar
- Persistência no backend Laravel

## Componentes Principais

### Backend (Laravel)

#### 1. Migration
- **Arquivo**: `database/migrations/2025_08_24_103220_create_notifications_table.php`
- **Tabela**: `notifications`
- **Campos**:
  - `id`: ID único da notificação
  - `title`: Título da notificação
  - `message`: Mensagem da notificação
  - `type`: Tipo (info, success, warning, error)
  - `user_id`: ID do usuário destinatário
  - `read`: Status de leitura (boolean)
  - `read_at`: Timestamp de quando foi lida
  - `data`: Dados adicionais (JSON)
  - `action_url`: URL para ação (opcional)

#### 2. Model
- **Arquivo**: `app/Models/Notification.php`
- **Funcionalidades**:
  - Relacionamento com User
  - Métodos para marcar como lida/não lida
  - Scopes para filtrar por status
  - Log de atividades

#### 3. Controller
- **Arquivo**: `app/Http/Controllers/NotificationController.php`
- **Endpoints**:
  - `GET /notifications` - Listar notificações
  - `POST /notifications` - Criar notificação
  - `PUT /notifications/{id}` - Atualizar status
  - `DELETE /notifications/{id}` - Excluir notificação
  - `GET /notifications/unread-count` - Contagem não lidas
  - `POST /notifications/mark-all-read` - Marcar todas como lidas

### Frontend (Vue.js)

#### 1. Store Vuex
- **Arquivo**: `src/store/modules/notifications.js`
- **Estado**:
  - `notifications`: Array de notificações
  - `unreadCount`: Contagem de não lidas
  - `loading`: Estado de carregamento
  - `error`: Mensagens de erro

#### 2. Service
- **Arquivo**: `src/services/notificationsService.js`
- **Funcionalidades**:
  - Polling automático adaptativo
  - Métodos para todas as operações
  - Formatação de datas
  - Helpers para ícones e cores

#### 3. Componente Principal
- **Arquivo**: `src/components/NotificationDropdown.vue`
- **Características**:
  - Dropdown com foto do usuário ou sino
  - Badge com contagem de não lidas
  - Lista de notificações
  - Ações (marcar como lida, excluir)
  - Botões de Ajuda e Sair

#### 4. Plugin Global
- **Arquivo**: `src/plugins/notifications.js`
- **Funcionalidades**:
  - Inicialização automática
  - Gerenciamento de visibilidade da página
  - Mixins globais
  - Cleanup automático

## Como Usar

### 1. Criar uma Notificação (Backend)

```php
use App\Models\Notification;

Notification::create([
    'title' => 'Nova consulta agendada',
    'message' => 'Sua consulta foi agendada para amanhã às 14:00',
    'type' => 'success',
    'user_id' => $userId,
    'action_url' => '/agenda'
]);
```

### 2. Usar no Frontend (Componente)

```vue
<template>
  <div>
    <!-- O componente já está integrado na TabNavigation -->
    <notification-dropdown />
  </div>
</template>

<script>
export default {
  async mounted() {
    // Buscar notificações
    await this.$fetchNotifications()
    
    // Verificar se há não lidas
    if (this.$hasUnreadNotifications) {
      console.log(`${this.$unreadNotificationsCount} notificações não lidas`)
    }
  },
  
  methods: {
    async markAsRead(notificationId) {
      await this.$markNotificationAsRead(notificationId)
    }
  }
}
</script>
```

### 3. Usar com Composition API

```javascript
import { useNotifications } from '@/plugins/notifications'

export default {
  setup() {
    const {
      hasUnreadNotifications,
      unreadCount,
      fetchNotifications,
      markAsRead
    } = useNotifications()
    
    return {
      hasUnreadNotifications,
      unreadCount,
      fetchNotifications,
      markAsRead
    }
  }
}
```

## Configuração

### Polling
O sistema usa polling adaptativo:
- **Normal**: 30 segundos
- **Usuário inativo (2+ min)**: 90 segundos
- **Usuário muito inativo (10+ min)**: 180 segundos
- **Página oculta**: Pausado

### Tipos de Notificação
- `info`: Informações gerais (azul)
- `success`: Ações bem-sucedidas (verde)
- `warning`: Avisos (laranja)
- `error`: Erros (vermelho)

## Testes

### Executar Testes
```javascript
// No console do navegador
import { runNotificationTests } from '@/utils/notificationsTest'
runNotificationTests()

// Testar performance
import { testNotificationPerformance } from '@/utils/notificationsTest'
testNotificationPerformance()

// Criar notificação de teste
import { createTestNotification } from '@/utils/notificationsTest'
createTestNotification()
```

## Personalização

### Adicionar Novo Tipo
1. Atualizar validação no controller
2. Adicionar ícone e cor no service:

```javascript
getNotificationIcon(type) {
  const icons = {
    info: 'mdi-information',
    success: 'mdi-check-circle',
    warning: 'mdi-alert',
    error: 'mdi-alert-circle',
    custom: 'mdi-star' // Novo tipo
  }
  return icons[type] || icons.info
}
```

### Modificar Intervalo de Polling
```javascript
// Iniciar com intervalo personalizado
notificationsService.startPolling(60000) // 1 minuto
```

## Troubleshooting

### Notificações não aparecem
1. Verificar se o usuário está autenticado
2. Verificar console para erros de API
3. Executar testes: `runNotificationTests()`

### Polling não funciona
1. Verificar se a página está visível
2. Verificar conexão com API
3. Verificar se o service foi inicializado

### Performance lenta
1. Executar teste de performance
2. Verificar se há muitas notificações no estado
3. Considerar paginação

## Próximas Melhorias

- [ ] Notificações push em tempo real (WebSockets)
- [ ] Categorização avançada
- [ ] Templates de notificação
- [ ] Notificações por email
- [ ] Histórico completo de notificações
- [ ] Configurações de usuário para tipos de notificação
