<?php

function createToken($length)
{
    return substr(str_shuffle(str_repeat("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", $length)), 0, $length);
}

function imageUrl($relativeUrl)
{
    return url('/') . '/img/' . $relativeUrl;
}

function relativeUrl($fullUrl)
{
    $baseUrl = url('/');
    $imgPath = '/img/';
    $relativeUrl = str_replace($baseUrl . $imgPath, '', $fullUrl);
    return $relativeUrl;
}

function modelo3dUrl($relativeUrl)
{
    return url('/') . '/modelo3d/' . $relativeUrl;
}

function responseSuccess($config = [])
{
    $defaultConfig = [
        'status' => 'success',
        'message' => '',
        'data' => []
    ];

    // Se não é um array ou se contém chaves que não estão no defaultConfig,
    // então o conteúdo deve ser enviado como "data" na resposta
    if (!is_array($config) || count(array_diff_key($config, $defaultConfig)) > 0) {
        $data = $config;
        $config = $defaultConfig;
        $config['data'] = $data;
    }

    // Garantir que apenas chaves permitidas sejam processadas
    $allowedKeys = array_keys($defaultConfig);
    $config = array_intersect_key($config, array_flip($allowedKeys));

    $config['status'] = $config['status'] ?? $defaultConfig['status'];
    $config['message'] = $config['message'] ?? $defaultConfig['message'];
    $config['data'] = $config['data'] ?? $defaultConfig['data'];

    return response()->json($config, 200);
}

function responseDebug(...$args)
{
    $output = '';
    foreach ($args as $arg) {
        $output .= print_r($arg, true) . PHP_EOL;
        $output .= '-------' . PHP_EOL;
    }
    return response($output);
}

function responseError($config = [])
{
    $defaultConfig = [
        'status' => 'error',
        'message' => '',
        'statusCode' => 400,
        'data' => []
    ];

    $allowedKeys = array_keys($defaultConfig);
    $config = array_intersect_key($config, array_flip($allowedKeys));

    $config['status'] = $config['status'] ?? $defaultConfig['status'];
    $config['message'] = $config['message'] ?? $defaultConfig['message'];
    $config['statusCode'] = $config['statusCode'] ?? $defaultConfig['statusCode'];
    $config['data'] = $config['data'] ?? $defaultConfig['data'];

    return response()->json($config, $config['statusCode']);
}

function strContains($haystack, $needle)
{
    return strpos($haystack, $needle) !== false;
}
