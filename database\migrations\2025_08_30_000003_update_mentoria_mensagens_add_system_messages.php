<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateMentoriaMensagensAddSystemMessages extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('mentoria_mensagens', function (Blueprint $table) {
            // Tornar remetente_id nullable para mensagens do sistema
            $table->unsignedBigInteger('remetente_id')->nullable()->change();
            
            // Adicionar tipo de mensagem
            $table->string('tipo')->default('USUARIO')->after('mensagem'); // USUARIO, SISTEMA
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('mentoria_mensagens', function (Blueprint $table) {
            $table->unsignedBigInteger('remetente_id')->nullable(false)->change();
            $table->dropColumn('tipo');
        });
    }
}
