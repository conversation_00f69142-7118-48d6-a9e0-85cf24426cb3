<template>
    <div class="tratamento-content">
        <div class="row">

            <div class="col-12">
                <div class="custom-card primary">
                    <div class="custom-card-header">
                        Relatos do paciente
                        <span class="info-icon-wrapper" title="Como funciona?">
                            <font-awesome-icon :icon="['fas', 'info-circle']" class="info-circle-icon" />
                        </span>
                    </div>
                    <div class="custom-card-body p-0 pb-3" style="max-height: 450px; overflow-y: auto; overflow-x: hidden;">
                        <div class="row px-3">
                            <!-- <div v-for="(perceptions, categoria) in personalPerceptions" v-bind:key="perceptions"
                                class="col-md-6 mt-2 px-2 py-1">
                                <div class="card">
                                    <div class="card-header p-3 pb-0">
                                        <p class="text-uppercase text-sm" style="font-weight: 500">{{ categoria }}</p>
                                    </div>
                                    <div class="card-body m-0 p-3 pt-2">
                                        <div class="info-container" v-for="perception in perceptions"
                                            v-bind:key="perception.text" :class="perception.type">
                                            <div style="width: 30px; text-align: center;">
                                                <font-awesome-icon :icon="['fas', getInfoIcon(perception.type)]" />
                                            </div>
                                            <div class="">
                                                <span>{{ perception.text }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div> -->

                            <div v-if="!paciente.formulario_respondido || detalhesClinicos.length == 0"
                                style="padding: 15px 15px 0px 15px; font-size: 12pt;" class="text-center">
                                O paciente ainda não respondeu à ficha de avaliação inicial. Para enviar-lhe o
                                formulário, utilize o botão "<font-awesome-icon :icon="['fab', 'fa-whatsapp']"
                                    class="me-1 text-sm" /><span class="text-sm font-weight-bold uppercase">ENVIAR
                                    LINK</span>" na aba "<font-awesome-icon :icon="['fas', 'fa-user']"
                                    class="me-1 text-sm" />Perfil".
                            </div>

                            <div v-if="paciente.formulario_respondido" class="row">
                                <div v-for="(detalhe, index) in detalhesClinicos" v-bind:key="index"
                                    class="col-sm-6 col-md-4 col-lg-3">
                                    <div class="info-container-compact mt-2" :class="detalhe.nivel">
                                        <div class="info-icon-compact">
                                            <font-awesome-icon :icon="['fas', getInfoIcon(detalhe.nivel)]" />
                                        </div>
                                        <div class="info-content-compact">
                                            <span>{{ detalhe.detalhe }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="custom-card primary mt-4 analysis-card">
                    <div class="custom-card-header">
                        <span class="header-title">Análise extra-bucal</span>
                        <div class="header-actions">
                            <span v-if="extraBucalImages.length > 0"
                                  class="analysis-play-icon-wrapper"
                                  :class="{ 'mostly-filled': isExtraBucalMostlyFilled }"
                                  @click="startAnalysisVisualization('extraBucal')"
                                  title="Iniciar análise visual">
                                <font-awesome-icon :icon="['fas', 'play']" class="analysis-play-icon" />
                            </span>
                            <span v-if="extraBucalImages.length === 0" class="edit-icon-wrapper" :class="{ 'active': isEditing['extraBucal'] }" @click="toggleEditMode('extraBucal')">
                                <font-awesome-icon :icon="['fas', 'edit']" class="edit-icon"
                                    :title="isEditing['extraBucal'] ? 'Sair do modo de edição' : 'Editar as análises extra-bucais'" />
                            </span>
                            <span v-if="isEditing.extraBucal" class="text-capitalize text-light pointer ms-2" @click="toggleEditMode('extraBucal')"><u>Cancelar edição</u></span>
                        </div>
                    </div>
                    <div class="custom-card-body p-0 card-top-border">
                        <v-table density="compact" class="analises-table extra-bucal">
                            <tbody>
                                <tr v-for="analise in analises['Extra-bucal']" v-bind:key="analise.id"
                                    :class="analise.nivel">
                                    <td>
                                        {{ analise.analise }}
                                    </td>
                                    <td>
                                        <span v-if="!isEditing['extraBucal']">
                                            {{ analise.respostas ? analise.respostas : '-' }}
                                        </span>

                                        <select v-if="isEditing['extraBucal'] && analise.tipo == 'unica_escolha'"
                                            class="form-select select-sm custom-select" v-model="analise.selectedResposta"
                                            @change="handleAnalisesUpdate">
                                            <option hidden :value="undefined">-</option>
                                            <option v-for="alternativa in analise.alternativas"
                                                v-bind:key="alternativa.resposta" :class="'text-' + alternativa.nivel"
                                                :selected="alternativa.resposta == analise.respostas">
                                                {{ alternativa.resposta }}
                                            </option>
                                            <option value="detalhe">{{ analise.titulo_detalhe ? analise.titulo_detalhe :
                                                'Especificar...' }}</option>
                                        </select>

                                        <template v-if="analise.tipo == 'multipla_escolha' && isEditing['extraBucal']">
                                            <div v-for="alternativa in analise.alternativas"
                                                :key="alternativa.resposta">
                                                <input type="checkbox" :id="alternativa.resposta"
                                                    :value="alternativa.resposta" :name="analise.id"
                                                    v-model="alternativa.selecionada" @change="handleAnalisesUpdate" />
                                                <label :for="alternativa.resposta"
                                                    :class="'text-' + alternativa.nivel">{{ alternativa.resposta
                                                    }}</label>
                                            </div>
                                            <div>
                                                <input type="checkbox" v-model="analise.detalhar"
                                                    @change="handleAnalisesUpdate" />
                                                <label :for="analise.id + 'detalhe'">{{ analise.titulo_detalhe ?
                                                    analise.titulo_detalhe : 'Especificar...' }}</label>
                                            </div>
                                        </template>

                                        <MaterialInput
                                            v-if="isEditing['extraBucal'] && (analise.detalhar || analise.selectedResposta == 'detalhe')"
                                            type="text" class="input-sm" v-model="analise.detalhe"
                                            :input="handleAnalisesUpdate" />

                                        <MaterialInput
                                            v-if="isEditing['extraBucal'] && analise.tipo == 'texto' && !analise.detalhar"
                                            type="text" class="input-sm" v-model="analise.respostas" />
                                    </td>
                                </tr>
                            </tbody>
                        </v-table>
                    </div>
                    <div v-if="isEditing['extraBucal']" class="w-100 text-center mb-3 mt-0">
                        <button class="btn btn-sm btn-primary mt-3 mb-0 btn-save"
                            title="Salvar as alterações realizadas" @click="confirmSaveAnalises()">
                            <i class="fas fa-save me-2"></i>Salvar
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-12">
                <div class="custom-card primary mt-4 analysis-card">
                    <div class="custom-card-header">
                        <span class="header-title">Análise Intra-bucal</span>
                        <div class="header-actions">
                            <span v-if="intraBucalImages.length > 0"
                                  class="analysis-play-icon-wrapper"
                                  :class="{ 'mostly-filled': isIntraBucalMostlyFilled }"
                                  @click="startAnalysisVisualization('intraBucal')"
                                  title="Iniciar análise visual">
                                <font-awesome-icon :icon="['fas', 'play']" class="analysis-play-icon" />
                            </span>
                            <span v-if="intraBucalImages.length === 0" class="edit-icon-wrapper" :class="{ 'active': isEditing['intraBucal'] }" @click="toggleEditMode('intraBucal')">
                                <font-awesome-icon :icon="['fas', 'edit']" class="edit-icon"
                                    :title="isEditing['intraBucal'] ? 'Sair do modo de edição' : 'Editar as análises intra-bucais'" />
                            </span>
                            <span v-if="isEditing.intraBucal" class="text-capitalize text-light pointer ms-2" @click="toggleEditMode('intraBucal')"><u>Cancelar edição</u></span>
                        </div>
                    </div>
                    <div class="custom-card-body p-0 card-top-border">
                        <v-table density="compact" class="analises-table intra-bucal">
                            <tbody>
                                <tr v-for="analise in analises['Intra-bucal']" v-bind:key="analise.id"
                                    :class="analise.nivel">
                                    <td>
                                        {{ analise.analise }}
                                    </td>
                                    <td>
                                        <span v-if="!isEditing['intraBucal']">
                                            {{ analise.respostas ? analise.respostas : '-' }}
                                        </span>

                                        <select v-if="isEditing['intraBucal'] && analise.tipo == 'unica_escolha'"
                                            class="form-select select-sm custom-select" v-model="analise.selectedResposta"
                                            @change="handleAnalisesUpdate">
                                            <option hidden :value="undefined">-</option>
                                            <option v-for="alternativa in analise.alternativas"
                                                v-bind:key="alternativa.resposta" :class="'text-' + alternativa.nivel"
                                                :selected="alternativa.resposta == analise.respostas">
                                                {{ alternativa.resposta }}
                                            </option>
                                            <option value="detalhe">{{ analise.titulo_detalhe ? analise.titulo_detalhe :
                                                'Especificar...' }}</option>
                                        </select>

                                        <template v-if="analise.tipo == 'multipla_escolha' && isEditing['intraBucal']">
                                            <div v-for="alternativa in analise.alternativas"
                                                :key="alternativa.resposta">
                                                <input type="checkbox" :id="alternativa.resposta"
                                                    :value="alternativa.resposta" :name="analise.id"
                                                    v-model="alternativa.selecionada" @change="handleAnalisesUpdate" />
                                                <label :for="alternativa.resposta"
                                                    :class="'text-' + alternativa.nivel">{{ alternativa.resposta
                                                    }}</label>
                                            </div>
                                            <div>
                                                <input type="checkbox" v-model="analise.detalhar"
                                                    @change="handleAnalisesUpdate" />
                                                <label :for="analise.id + 'detalhe'">{{ analise.titulo_detalhe ?
                                                    analise.titulo_detalhe : 'Especificar...' }}</label>
                                            </div>
                                        </template>

                                        <MaterialInput
                                            v-if="isEditing['intraBucal'] && (analise.detalhar || analise.selectedResposta == 'detalhe')"
                                            type="text" class="input-sm" v-model="analise.detalhe"
                                            :input="handleAnalisesUpdate" />

                                        <MaterialInput
                                            v-if="isEditing['intraBucal'] && analise.tipo == 'texto' && !analise.detalhar"
                                            type="text" class="input-sm" v-model="analise.respostas" />
                                    </td>
                                </tr>
                            </tbody>
                        </v-table>
                    </div>
                    <div v-if="isEditing['intraBucal']" class="w-100 text-center mb-3 mt-0">
                        <button class="btn btn-sm btn-primary mt-3 mb-0 btn-save"
                            title="Salvar as alterações realizadas" @click="confirmSaveAnalises()">
                            <i class="fas fa-save me-2"></i>Salvar
                        </button>
                    </div>
                </div>

            </div>

            <div class="col-lg-4 col-md-6">
                <div class="custom-card primary mt-4 analysis-card">
                    <div class="custom-card-header">
                        <span class="header-title">Análises Radiográficas</span>
                        <div class="header-actions">
                            <span v-if="radiograficasImages.length > 0"
                                  class="analysis-play-icon-wrapper"
                                  :class="{ 'mostly-filled': isRadiograficasMostlyFilled }"
                                  @click="startAnalysisVisualization('analisesRadiograficas')"
                                  title="Iniciar análise visual">
                                <font-awesome-icon :icon="['fas', 'play']" class="analysis-play-icon" />
                            </span>
                            <span v-if="radiograficasImages.length === 0" class="edit-icon-wrapper" :class="{ 'active': isEditing['analisesRadiograficas'] }" @click="toggleEditMode('analisesRadiograficas')">
                                <font-awesome-icon :icon="['fas', 'edit']" class="edit-icon"
                                    :title="isEditing['analisesRadiograficas'] ? 'Sair do modo de edição' : 'Editar as análises radiográficas'" />
                            </span>
                            <span v-if="isEditing.analisesRadiograficas" class="text-capitalize text-light pointer ms-2" @click="toggleEditMode('analisesRadiograficas')"><u>Cancelar edição</u></span>
                        </div>
                    </div>
                    <div class="custom-card-body p-0 card-top-border">
                        <v-table density="compact" class="analises-table analises-radiograficas">
                            <tbody>
                                <tr v-for="analise in analises['Radiográficas']" v-bind:key="analise.id"
                                    :class="analise.nivel">
                                    <td>
                                        {{ analise.analise }}
                                    </td>
                                    <td>
                                        <span v-if="!isEditing['analisesRadiograficas']">
                                            {{ analise.respostas ? analise.respostas : '-' }}
                                        </span>

                                        <select
                                            v-if="isEditing['analisesRadiograficas'] && analise.tipo == 'unica_escolha'"
                                            class="form-select select-sm custom-select" v-model="analise.selectedResposta"
                                            @change="handleAnalisesUpdate">
                                            <option hidden :value="undefined">-</option>
                                            <option v-for="alternativa in analise.alternativas"
                                                v-bind:key="alternativa.resposta" :class="'text-' + alternativa.nivel"
                                                :selected="alternativa.resposta == analise.respostas">
                                                {{ alternativa.resposta }}
                                            </option>
                                            <option value="detalhe">{{ analise.titulo_detalhe ? analise.titulo_detalhe :
                                                'Especificar...' }}</option>
                                        </select>

                                        <template
                                            v-if="analise.tipo == 'multipla_escolha' && isEditing['analisesRadiograficas']">
                                            <div v-for="alternativa in analise.alternativas"
                                                :key="alternativa.resposta">
                                                <input type="checkbox" :id="alternativa.resposta"
                                                    :value="alternativa.resposta" :name="analise.id"
                                                    v-model="alternativa.selecionada" @change="handleAnalisesUpdate" />
                                                <label :for="alternativa.resposta"
                                                    :class="'text-' + alternativa.nivel">{{ alternativa.resposta
                                                    }}</label>
                                            </div>
                                            <div>
                                                <input type="checkbox" :id="analise.id + 'detalhe'" value="detalhe"
                                                    :name="analise.id" v-model="analise.detalhar"
                                                    @change="handleAnalisesUpdate" />
                                                <label :for="analise.id + 'detalhe'">{{ analise.titulo_detalhe ?
                                                    analise.titulo_detalhe : 'Especificar...' }}</label>
                                            </div>
                                        </template>

                                        <MaterialInput
                                            v-if="isEditing['analisesRadiograficas'] && (analise.detalhar || analise.selectedResposta == 'detalhe')"
                                            type="text" class="input-sm" v-model="analise.detalhe"
                                            :input="handleAnalisesUpdate" />

                                        <MaterialInput
                                            v-if="isEditing['analisesRadiograficas'] && analise.tipo == 'texto' && !analise.detalhar"
                                            type="text" class="input-sm" v-model="analise.respostas" />
                                    </td>
                                </tr>
                            </tbody>
                        </v-table>
                    </div>
                    <div v-if="isEditing['analisesRadiograficas']" class="w-100 text-center mb-3 mt-0">
                        <button class="btn btn-sm btn-primary mt-3 mb-0 btn-save"
                            title="Salvar as alterações realizadas" @click="confirmSaveAnalises()">
                            <i class="fas fa-save me-2"></i>Salvar
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="p-horizontal-divider"></div>
                <div class="next-btn-container py-3 py-md-4">
                <button
                    class="btn btn-success mb-0 diagnostico-btn"
                    @click="gerarDiagnostico"
                >
                    <i class="me-2 fas fa-book-medical"></i>
                    <span>
                        GERAR DIAGNÓSTICO
                    </span>
                    <i class="ms-2 fas fa-chevron-right"></i>
                </button>
                </div>
            </div>
        </div>

    </div>

    <!-- Modal de Visualização de Análise -->
    <Transition name="modal-fade">
        <div v-if="showAnalysisModal" class="analysis-modal-overlay" @click="closeAnalysisModal">
            <div class="analysis-modal-container" @click.stop>
                <!-- Botão de fechar -->
                <button class="analysis-modal-close" @click="closeAnalysisModal" title="Fechar visualização">
                    <font-awesome-icon :icon="['fas', 'times']" />
                </button>

                <!-- Container principal do modal -->
                <div class="analysis-modal-content">
                    <!-- Container de imagens estáticas à esquerda -->
                    <div class="analysis-images-container col-lg-7 col-md-6 col-12">
                        <div class="analysis-images-header">
                            <h5>{{ getCarouselTitle(currentAnalysisType) }}</h5>
                        </div>

                        <!-- Container único v-viewer para todas as imagens -->
                        <div class="analysis-images-wrapper" v-viewer="{ title: [1, (image, imageData) => `${image.alt}`] }">
                            <!-- Layout específico para imagens intra-bucais -->
                            <div v-if="currentAnalysisType === 'intraBucal'" class="analysis-images-grid intra-bucal-layout">
                                <!-- Primeira linha: Lateral direita, Frontal, Lateral esquerda -->
                                <div class="intra-bucal-row-1">
                                    <div
                                        v-for="imagem in getIntraBucalRow1Images()"
                                        :key="imagem.url"
                                        class="analysis-image-item"
                                    >
                                        <img
                                            :src="imagem.url"
                                            :alt="getImageDescription(imagem)"
                                            :title="getImageDescription(imagem)"
                                        />
                                        <div class="analysis-image-info">
                                            <span class="img-desc" v-if="imagem.descricao">{{ imagem.descricao }}</span>
                                            <span class="img-tag" v-if="imagem.tag_diagnostico">{{ formatImageTag(imagem.tag_diagnostico) }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Segunda linha: Oclusal superior, Oclusal inferior -->
                                <div class="intra-bucal-row-2">
                                    <div
                                        v-for="imagem in getIntraBucalRow2Images()"
                                        :key="imagem.url"
                                        class="analysis-image-item"
                                    >
                                        <img
                                            :src="imagem.url"
                                            :alt="getImageDescription(imagem)"
                                            :title="getImageDescription(imagem)"
                                        />
                                        <div class="analysis-image-info">
                                            <span class="img-desc" v-if="imagem.descricao">{{ imagem.descricao }}</span>
                                            <span class="img-tag" v-if="imagem.tag_diagnostico">{{ formatImageTag(imagem.tag_diagnostico) }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Layout específico para radiográficas -->
                            <div v-else-if="currentAnalysisType === 'analisesRadiograficas'" class="analysis-images-grid radiograficas-layout">
                                <div
                                    v-for="imagem in currentAnalysisImages"
                                    :key="imagem.url"
                                    class="analysis-image-item"
                                >
                                    <img
                                        :src="imagem.url"
                                        :alt="getImageDescription(imagem)"
                                        :title="getImageDescription(imagem)"
                                    />
                                    <div class="analysis-image-info">
                                        <span class="img-desc" v-if="imagem.descricao">{{ imagem.descricao }}</span>
                                        <span class="img-tag" v-if="imagem.tag_diagnostico">{{ formatImageTag(imagem.tag_diagnostico) }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Layout padrão para extra-bucais -->
                            <div v-else class="analysis-images-grid standard-layout">
                                <div
                                    v-for="imagem in currentAnalysisImages"
                                    :key="imagem.url"
                                    class="analysis-image-item"
                                >
                                    <img
                                        :src="imagem.url"
                                        :alt="getImageDescription(imagem)"
                                        :title="getImageDescription(imagem)"
                                    />
                                    <div class="analysis-image-info">
                                        <span class="img-desc" v-if="imagem.descricao">{{ imagem.descricao }}</span>
                                        <span class="img-tag" v-if="imagem.tag_diagnostico">{{ formatImageTag(imagem.tag_diagnostico) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Lista de análises à direita -->
                    <div class="analysis-list-container col-lg-5 col-md-6 col-12">
                        <div class="analysis-list-header">
                            <h5>{{ getAnalysisTitle(currentAnalysisType) }}</h5>
                        </div>
                        <div class="analysis-list-content">
                            <v-table density="compact" class="analises-table-modal">
                                <tbody>
                                    <tr v-for="analise in getCurrentAnalysisList()" v-bind:key="analise.id" :class="analise.nivel">
                                        <td class="analysis-question">
                                            {{ analise.analise }}
                                        </td>
                                        <td class="analysis-answer">
                                            <!-- Modo de edição sempre ativo no modal -->
                                            <select v-if="analise.tipo == 'unica_escolha'"
                                                class="form-select select-sm custom-select-modal" v-model="analise.selectedResposta"
                                                @change="handleAnalisesUpdate">
                                                <option hidden :value="undefined">-</option>
                                                <option v-for="alternativa in analise.alternativas"
                                                    v-bind:key="alternativa.resposta" :class="'text-' + alternativa.nivel"
                                                    :selected="alternativa.resposta == analise.respostas">
                                                    {{ alternativa.resposta }}
                                                </option>
                                                <option value="detalhe">{{ analise.titulo_detalhe ? analise.titulo_detalhe :
                                                    'Especificar...' }}</option>
                                            </select>

                                            <template v-if="analise.tipo == 'multipla_escolha'">
                                                <div v-for="alternativa in analise.alternativas" :key="alternativa.resposta" class="checkbox-container-modal">
                                                    <input type="checkbox" :id="'modal_' + alternativa.resposta + '_' + analise.id"
                                                        :value="alternativa.resposta" :name="'modal_' + analise.id"
                                                        v-model="alternativa.selecionada" @change="handleAnalisesUpdate" />
                                                    <label :for="'modal_' + alternativa.resposta + '_' + analise.id"
                                                        :class="'text-' + alternativa.nivel">{{ alternativa.resposta }}</label>
                                                </div>
                                                <div class="checkbox-container-modal">
                                                    <input type="checkbox" :id="'modal_' + analise.id + '_detalhe'"
                                                        v-model="analise.detalhar" @change="handleAnalisesUpdate" />
                                                    <label :for="'modal_' + analise.id + '_detalhe'">{{ analise.titulo_detalhe ?
                                                        analise.titulo_detalhe : 'Especificar...' }}</label>
                                                </div>
                                            </template>

                                            <MaterialInput
                                                v-if="analise.detalhar || analise.selectedResposta == 'detalhe'"
                                                type="text" class="input-sm-modal" v-model="analise.detalhe"
                                                :input="handleAnalisesUpdate" />

                                            <MaterialInput
                                                v-if="analise.tipo == 'texto' && !analise.detalhar"
                                                type="text" class="input-sm-modal" v-model="analise.respostas" />
                                        </td>
                                    </tr>
                                </tbody>
                            </v-table>
                        </div>

                        <!-- Botão flutuante de salvar -->
                        <div class="floating-button-container right">
                            <button class="floating-button blue" @click="confirmSaveAnalises()"
                                title="Salvar as alterações realizadas">
                                <i class="fas fa-save me-2"></i>Salvar Alterações
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </Transition>
</template>

<style>
.input-sm {
    background: #F8F9FA;
    border-radius: 6px;
    transition: all 0.3s ease;
    border: 1px solid #E0E5EB;
}

.input-sm:focus {
    background: #FFFFFF;
    box-shadow: 0 0 0 3px rgba(27, 68, 100, 0.1);
    border-color: #80A1BB;
}

/* Melhorias nos custom-cards */
.custom-card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border: 1px solid rgba(128, 161, 187, 0.3);
}

.custom-card:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.custom-card.primary {
    border: 1px solid rgba(128, 161, 187, 0.4) !important;
}

.custom-card-header {
    padding: 14px 20px !important;
    letter-spacing: 0.6px;
    font-weight: 600;
    font-size: 0.95rem;
    position: relative;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title {
    flex: 1;
    text-align: center;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.primary .custom-card-header {
    background: linear-gradient(135deg, #7096B3 0%, #1B4464 50%, #7096B3 100%);
    color: #FFF;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.primary .custom-card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.primary .custom-card-header:hover::before {
    left: 100%;
}

/* Estilos para os cards */
.card {
    border: 1px solid #E0E5EB;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
}

.card-body {
    font-size: 1rem;
    font-weight: 300;
    padding: 1.25rem;
}

/* Estilos para os custom-cards */
.custom-card {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
}

.custom-card:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.custom-card.primary {
    border: 1px solid rgba(128, 161, 187, 0.5) !important;
}

.custom-card-header {
    padding: 12px 0px !important;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.primary .custom-card-header {
    background: linear-gradient(to right, #7096B3, #1B4464, #7096B3);
    color: #FFF;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.custom-card-body {
    padding: 15px;
    background-color: #FFFFFF;
}

.card-top-border {
    border-top: 1px solid #E0E5EB;
}

/* Estilos para os relatos do paciente - versão compacta */
.info-container-compact {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    margin-bottom: 8px;
    background-color: #F9FAFC;
    border-left: 3px solid #DDD;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
    transition: all 0.2s ease;
    min-height: 42px;
}

.info-container-compact:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
}

.info-container-compact.positivo {
    border-left-color: #4CAF50;
    background-color: #F1F8F1;
}

.info-container-compact.neutro {
    border-left-color: #80A1BB;
    background-color: #F5F9FC;
}

.info-container-compact.atencao {
    border-left-color: #FFC107;
    background-color: #FFFBF0;
}

.info-container-compact.negativo {
    border-left-color: #F44336;
    background-color: #FEF5F4;
}

.info-icon-compact {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-right: 10px;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
    flex-shrink: 0;
    font-size: 0.85rem;
}

.positivo .info-icon-compact {
    color: #4CAF50;
}

.neutro .info-icon-compact {
    color: #80A1BB;
}

.atencao .info-icon-compact {
    color: #FFC107;
}

.negativo .info-icon-compact {
    color: #F44336;
}

.info-content-compact {
    flex-grow: 1;
    font-size: 0.85rem;
    line-height: 1.3;
    font-weight: 400;
}

/* Estilos para os cards de análise */
.analysis-card {
    height: fit-content;
    min-height: 400px;
}

.analysis-card .custom-card-body {
    padding: 0;
}

/* Estilos para as tabelas de análise - versão otimizada */
.analises-table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.03);
}

.analises-table td {
    padding: 8px 12px !important;
    font-size: 0.85rem;
    color: #444 !important;
    text-align: left;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #F0F0F0;
    vertical-align: middle;
}

.analises-table tr:last-child td {
    border-bottom: none;
}

.analises-table tr>td:first-child {
    border-right: 1px solid #EEE;
    font-weight: 500 !important;
    color: #333 !important;
    background-color: #FAFBFC;
}

.analises-table tr>td:last-child {
    font-weight: 400 !important;
}

.analises-table tr:nth-of-type(odd)>td:last-child {
    background: #F9FAFC;
}

.analises-table tr:hover > * {
    background-color: #F0F7FF !important;
}

.analises-table.extra-bucal tr>td:first-child {
    width: 45%;
}

.analises-table.intra-bucal tr>td:first-child {
    width: 50%;
}

.analises-table.analises-radiograficas tr>td:first-child {
    width: 50%;
}

/* Estilo para o botão de diagnóstico */
.next-btn-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.diagnostico-btn {
    padding: 10px 24px;
    font-weight: 600;
    letter-spacing: 0.5px;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(76, 175, 80, 0.2);
    transition: all 0.3s ease;
    border: none;
    background: linear-gradient(135deg, #4CAF50, #388E3C);
}

.diagnostico-btn:hover {
    box-shadow: 0 6px 15px rgba(76, 175, 80, 0.3);
    transform: translateY(-2px);
    background: linear-gradient(135deg, #5CB860, #43A047);
}

.diagnostico-btn i {
    font-size: 16px;
    transition: transform 0.3s ease;
}

.diagnostico-btn:hover i.fa-chevron-right {
    transform: translateX(3px);
}

.diagnostico-btn span {
    font-size: 11pt;
}

/* Estilo para os botões de salvar */
.btn-save {
    padding: 8px 20px;
    font-weight: 500;
    border-radius: 6px;
    box-shadow: 0 3px 6px rgba(27, 68, 100, 0.15);
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #1B4464, #56809F);
    border: none;
}

.btn-save:hover {
    box-shadow: 0 4px 8px rgba(27, 68, 100, 0.25);
    transform: translateY(-1px);
    background: linear-gradient(135deg, #28597e, #7aa3c0);
}

/* Estilo para os ícones de edição - versão elegante */
.edit-icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.15);
    margin-left: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.edit-icon-wrapper:hover {
    background-color: rgba(255, 255, 255, 0.25);
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.edit-icon-wrapper.active {
    background-color: rgba(255, 255, 255, 0.35);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.edit-icon {
    color: white;
    font-size: 14px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* Estilo para o ícone de informação - versão elegante */
.info-icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.15);
    margin-left: 10px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-icon-wrapper:hover {
    background-color: rgba(255, 255, 255, 0.25);
    transform: scale(1.15);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
}

.info-circle-icon {
    color: white;
    font-size: 12px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* Responsividade para dispositivos móveis */
@media (max-width: 768px) {
    .analysis-card {
        min-height: 350px;
    }

    .info-container-compact {
        padding: 6px 10px;
        min-height: 38px;
    }

    .info-icon-compact {
        width: 24px;
        height: 24px;
        font-size: 0.8rem;
        margin-right: 8px;
    }

    .info-content-compact {
        font-size: 0.8rem;
    }

    .analises-table td {
        padding: 6px 10px !important;
        font-size: 0.8rem;
    }

    .custom-card-header {
        padding: 12px 16px !important;
        font-size: 0.9rem;
    }

    .edit-icon-wrapper {
        width: 28px;
        height: 28px;
        margin-left: 8px;
    }

    .info-icon-wrapper {
        width: 24px;
        height: 24px;
        margin-left: 8px;
    }
}

/* Estilos para selects e checkboxes */
.custom-select {
    min-width: 200px;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #E0E5EB;
    background-color: #FFFFFF;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    font-size: 0.9rem;
    color: #444;
}

.custom-select:focus {
    border-color: #80A1BB;
    box-shadow: 0 0 0 3px rgba(27, 68, 100, 0.1);
    outline: none;
}

.custom-select option {
    padding: 8px;
}

.custom-select option.text-positivo {
    color: #4CAF50;
}

.custom-select option.text-neutro {
    color: #80A1BB;
}

.custom-select option.text-atencao {
    color: #FFC107;
}

.custom-select option.text-negativo {
    color: #F44336;
}

/* Estilo para checkboxes */
input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    vertical-align: middle;
    accent-color: #1B4464;
}

label {
    font-size: 0.9rem;
    margin-bottom: 8px;
    vertical-align: middle;
}

label.text-positivo {
    color: #4CAF50;
}

label.text-neutro {
    color: #80A1BB;
}

label.text-atencao {
    color: #FFC107;
}

label.text-negativo {
    color: #F44336;
}

/* Estilos para o botão de play - versão elegante com cores condicionais */
.analysis-play-icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #66BB6A, #4CAF50);
    margin: 0;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid rgba(255, 255, 255, 0.9);
    box-shadow: 0 3px 8px rgba(76, 175, 80, 0.25),
                0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

/* Estado quando majoritariamente preenchido - branco elegante */
.analysis-play-icon-wrapper.mostly-filled {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 6px rgba(255, 255, 255, 0.2),
                0 1px 2px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(15px);
}

.analysis-play-icon-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.analysis-play-icon-wrapper:hover::before {
    left: 100%;
}

.analysis-play-icon-wrapper:hover {
    background: linear-gradient(135deg, #81C784, #66BB6A);
    transform: scale(1.08);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.35),
                0 2px 5px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 1);
}

.analysis-play-icon-wrapper.mostly-filled:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.08);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.4),
                0 2px 4px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.5);
}

.analysis-play-icon {
    color: white;
    font-size: 13px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
    margin-left: 1px; /* Ajuste visual para centralizar o ícone de play */
    z-index: 1;
    position: relative;
    transition: color 0.3s ease;
}

/* Ícone escuro quando o botão está branco (majoritariamente preenchido) */
.analysis-play-icon-wrapper.mostly-filled .analysis-play-icon {
    color: #1B4464;
    filter: drop-shadow(0 1px 2px rgba(255, 255, 255, 0.3));
}

/* Estilos para o modal de análise */
.analysis-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(8px);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.analysis-modal-container {
    width: 95vw;
    height: 90vh;
    max-width: 1400px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.analysis-modal-close {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    color: #666;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 0;
    margin: 0;
}

.analysis-modal-close:hover {
    background: #dc3545;
    color: white;
    transform: scale(1.1);
}

.analysis-modal-content {
    display: flex;
    height: 100%;
    overflow: hidden;
}

/* Container de imagens estáticas */
.analysis-images-container {
    flex: 1;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    min-height: 0; /* Permite que o flex funcione corretamente */
}

.analysis-images-header {
    padding: 20px;
    background: rgba(255, 255, 255, 0.75);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.analysis-images-header h5 {
    margin: 0;
    font-weight: 600;
    letter-spacing: 0.5px;
    color: #666 !important;
    font-size: 1.1rem;
}

.analysis-images-wrapper {
    flex: 1;
    overflow-y: auto;
    padding: 15px 10px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 0; /* Permite que o flex funcione corretamente */
    height: 100%; /* Garante que ocupe toda a altura disponível */
}

.analysis-images-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
    align-items: center; /* Centraliza verticalmente */
    width: 100%;
    max-width: 100%;
    align-content: center; /* Centraliza o conteúdo quando há quebra de linha */
}

/* Layout específico para imagens intra-bucais */
.analysis-images-grid.intra-bucal-layout {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
    width: 100%;
}

/* Primeira linha - 3 imagens */
.intra-bucal-layout .intra-bucal-row-1 {
    display: flex;
    justify-content: center;
    gap: 15px;
    width: 100%;
}

/* Segunda linha - 2 imagens centralizadas */
.intra-bucal-layout .intra-bucal-row-2 {
    display: flex;
    justify-content: center;
    gap: 15px;
    width: 100%;
}

/* Layout padrão para extra-bucais e radiográficas */
.analysis-images-grid.standard-layout {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
}

/* Layout específico para radiográficas - uma imagem acima da outra */
.analysis-images-grid.radiograficas-layout {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
    width: 100%;
    max-height: 70vh;
    overflow-y: auto;
    min-height: 0;
}

.analysis-image-item {
    background: transparent;
    border-radius: 2px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
    width: 320px;
    max-width: 100%;
}

.analysis-image-item:hover {
    transform: scale(1.02);
}

/* Imagens maiores para layout de radiográficas */
.radiograficas-layout .analysis-image-item {
    width: 90%;
    max-width: 500px;
}



.analysis-image-item img {
    width: 100%;
    height: 220px;
    object-fit: contain;
    transition: all 0.3s ease;
    background: transparent;
    border-radius: 2px;
}

/* Imagens maiores para layout de radiográficas */
.radiograficas-layout .analysis-image-item img {
    height: 300px;
}

.analysis-image-item:hover img {
    filter: brightness(105%);
}

.analysis-image-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9), transparent);
    color: white;
    padding: 15px 12px 10px;
    font-size: 0.8rem;
    text-align: center;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.analysis-image-item:hover .analysis-image-info {
    transform: translateY(0);
}

.analysis-image-info .img-desc {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.analysis-image-info .img-tag {
    font-size: 0.7rem;
    opacity: 0.8;
    font-style: italic;
    color: #4CAF50;
}

/* Responsividade para dispositivos móveis */
@media (max-width: 768px) {
    .analysis-images-wrapper {
        padding: 10px;
    }

    .analysis-images-grid {
        gap: 10px;
    }

    .analysis-image-item {
        width: 100%;
        max-width: 280px;
    }

    .analysis-image-item img {
        height: 160px;
    }

    /* Radiográficas em telas pequenas */
    .radiograficas-layout .analysis-image-item {
        width: 95%;
        max-width: 350px;
    }

    .radiograficas-layout .analysis-image-item img {
        height: 200px;
    }

    .analysis-images-grid.radiograficas-layout {
        max-height: 60vh;
    }

    /* Em telas pequenas, volta ao layout flexível normal para intra-bucais */
    .analysis-images-grid.intra-bucal-layout {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }

    .intra-bucal-layout .intra-bucal-row-1,
    .intra-bucal-layout .intra-bucal-row-2 {
        display: contents;
    }
}

/* Container da lista de análises */
.analysis-list-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative; /* Necessário para o posicionamento absoluto dos botões flutuantes */
    min-height: 0; /* Permite que o flex funcione corretamente com overflow */
}

/* Indicador de Progresso da Ficha - Barra Guia Visual */
.analysis-list-container::after {
    content: '';
    position: absolute;
    top: 80px; /* Abaixo do header */
    right: 25px; /* Ao lado do scrollbar */
    width: 4px;
    height: calc(100% - 160px); /* Altura total menos header e footer */
    background: linear-gradient(180deg,
        rgba(27, 68, 100, 0.1) 0%,
        rgba(112, 150, 179, 0.2) 50%,
        rgba(27, 68, 100, 0.1) 100%
    );
    border-radius: 2px;
    z-index: 10;

    /* Efeito de progresso */
    background-image:
        /* Linha de progresso */
        linear-gradient(180deg,
            #1B4464 0%,
            #7096B3 50%,
            #1B4464 100%
        ),
        /* Marcadores de seção */
        repeating-linear-gradient(180deg,
            transparent 0%,
            transparent 23%,
            rgba(255, 255, 255, 0.8) 23%,
            rgba(255, 255, 255, 0.8) 27%,
            transparent 27%,
            transparent 50%
        );

    background-size:
        100% var(--scroll-progress, 0%),
        100% 100%;

    background-position:
        top,
        top;

    background-repeat: no-repeat;

    /* Animação sutil */
    animation: progressGlow 2s ease-in-out infinite alternate;

    /* Tooltip indicativo */
    cursor: help;
}

@keyframes progressGlow {
    0% {
        box-shadow: 0 0 5px rgba(27, 68, 100, 0.3);
    }
    100% {
        box-shadow: 0 0 15px rgba(112, 150, 179, 0.6);
    }
}

/* Texto indicativo de progresso */
.analysis-list-container::before {
    content: attr(data-progress-text);
    position: absolute;
    top: 55px;
    right: 15px;
    font-size: 0.7rem;
    color: #1B4464;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 11;
    background: rgba(255, 255, 255, 0.95);
    padding: 3px 8px;
    border-radius: 4px;
    border: 1px solid rgba(27, 68, 100, 0.2);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    white-space: nowrap;
}

/* Fallback quando não há atributo data */
.analysis-list-container:not([data-progress-text])::before {
    content: 'Progresso da Ficha';
}

/* Estilo especial quando há muito conteúdo para rolar */
.analysis-list-container.has-scroll .analysis-list-content::-webkit-scrollbar-thumb {
    animation: scrollbarPulse 2s ease-in-out infinite, scrollbarGlow 4s ease-in-out infinite alternate;
}

@keyframes scrollbarGlow {
    0% {
        box-shadow:
            0 0 15px rgba(27, 68, 100, 0.5),
            inset 0 1px 0 rgba(255, 255, 255, 0.6),
            inset 0 -1px 0 rgba(0, 0, 0, 0.1),
            0 4px 20px rgba(112, 150, 179, 0.4);
    }
    100% {
        box-shadow:
            0 0 25px rgba(27, 68, 100, 0.7),
            inset 0 2px 0 rgba(255, 255, 255, 0.8),
            inset 0 -2px 0 rgba(0, 0, 0, 0.1),
            0 6px 30px rgba(112, 150, 179, 0.6);
    }
}

/* Indicador de progresso mais visível quando há scroll */
.analysis-list-container.has-scroll::after {
    animation: progressGlow 1.5s ease-in-out infinite alternate;
}

/* Indicador visual de scroll - sombra na parte inferior */
.analysis-list-container::after {
    content: '';
    position: absolute;
    bottom: 80px; /* Acima do botão flutuante */
    left: 0;
    right: 14px; /* Deixa espaço para a scrollbar de 14px */
    height: 25px;
    background: linear-gradient(to bottom,
        transparent 0%,
        rgba(27, 68, 100, 0.04) 30%,
        rgba(27, 68, 100, 0.12) 100%);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 5;
    border-radius: 0 0 8px 8px;
}

/* Indicador visual de scroll - sombra na parte superior */
.analysis-list-container::before {
    content: '';
    position: absolute;
    top: 60px; /* Abaixo do header */
    left: 0;
    right: 14px; /* Deixa espaço para a scrollbar de 14px */
    height: 20px;
    background: linear-gradient(to top,
        transparent 0%,
        rgba(27, 68, 100, 0.04) 30%,
        rgba(27, 68, 100, 0.08) 100%);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 5;
    border-radius: 8px 8px 0 0;
}



.analysis-list-header {
    padding: 20px;
    background: linear-gradient(135deg, #7096B3 0%, #1B4464 50%, #7096B3 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.analysis-list-header h5 {
    margin: 0;
    font-weight: 600;
    letter-spacing: 0.5px;
    color: white !important;
    font-size: 1.1rem;
}

.analysis-list-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: white;
    min-height: 0; /* Permite que o flex funcione corretamente com overflow */

    /* Scrollbar personalizada simples e chamativa */
    scrollbar-width: auto;
    scrollbar-color: #1B4464 #e9ecef;
}

/* Scrollbar Criativo e Guia - Sempre Visível e Chamativo */
.analysis-list-content::-webkit-scrollbar {
    width: 20px; /* Mais largo para ser mais visível */
    background: linear-gradient(180deg,
        rgba(27, 68, 100, 0.05) 0%,
        rgba(112, 150, 179, 0.1) 50%,
        rgba(27, 68, 100, 0.05) 100%
    );
    border-radius: 10px;
    border: 1px solid rgba(27, 68, 100, 0.1);
}

.analysis-list-content::-webkit-scrollbar-track {
    background: linear-gradient(180deg,
        #f8f9fa 0%,
        #e9ecef 50%,
        #f8f9fa 100%
    );
    border-radius: 10px;
    border: 1px solid #dee2e6;
    position: relative;

    /* Marcadores de progresso no track */
    background-image:
        /* Linha central guia */
        linear-gradient(180deg, transparent 0%, transparent 48%, #1B4464 48%, #1B4464 52%, transparent 52%, transparent 100%),
        /* Marcadores de seção (pontos) */
        radial-gradient(circle at center, #7096B3 1px, transparent 1px),
        radial-gradient(circle at center, #7096B3 1px, transparent 1px),
        radial-gradient(circle at center, #7096B3 1px, transparent 1px),
        radial-gradient(circle at center, #7096B3 1px, transparent 1px);

    background-size:
        2px 100%,
        4px 4px,
        4px 4px,
        4px 4px,
        4px 4px;

    background-position:
        center,
        center 25%,
        center 50%,
        center 75%,
        center 90%;

    background-repeat: no-repeat;
}

.analysis-list-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg,
        #1B4464 0%,
        #7096B3 25%,
        #ffffff 50%,
        #7096B3 75%,
        #1B4464 100%
    );
    border-radius: 10px;
    border: 3px solid #ffffff;
    min-height: 60px;
    position: relative;

    /* Efeitos visuais chamativos */
    background-image:
        /* Gradiente principal */
        linear-gradient(180deg, #1B4464 0%, #7096B3 25%, #ffffff 50%, #7096B3 75%, #1B4464 100%),
        /* Padrão de setas indicando direção */
        repeating-linear-gradient(
            180deg,
            transparent 0px,
            transparent 8px,
            rgba(255, 255, 255, 0.4) 8px,
            rgba(255, 255, 255, 0.4) 10px,
            transparent 10px,
            transparent 12px,
            rgba(255, 255, 255, 0.2) 12px,
            rgba(255, 255, 255, 0.2) 14px
        ),
        /* Brilho lateral */
        linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.3) 20%,
            rgba(255, 255, 255, 0.6) 50%,
            rgba(255, 255, 255, 0.3) 80%,
            transparent 100%
        );

    background-size:
        100% 100%,
        100% 100%,
        100% 100%;

    background-position: center;
    background-repeat: no-repeat;

    /* Sombras e efeitos */
    box-shadow:
        0 0 10px rgba(27, 68, 100, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.5),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1),
        0 4px 15px rgba(112, 150, 179, 0.3);

    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Animação pulsante sutil para chamar atenção */
    animation: scrollbarPulse 3s ease-in-out infinite;
}

@keyframes scrollbarPulse {
    0%, 100% {
        box-shadow:
            0 0 10px rgba(27, 68, 100, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.5),
            inset 0 -1px 0 rgba(0, 0, 0, 0.1),
            0 4px 15px rgba(112, 150, 179, 0.3);
    }
    50% {
        box-shadow:
            0 0 20px rgba(27, 68, 100, 0.6),
            inset 0 1px 0 rgba(255, 255, 255, 0.7),
            inset 0 -1px 0 rgba(0, 0, 0, 0.1),
            0 6px 25px rgba(112, 150, 179, 0.5);
    }
}

.analysis-list-content::-webkit-scrollbar-thumb:hover {
    background-image:
        /* Gradiente mais intenso no hover */
        linear-gradient(180deg, #0f2a3d 0%, #5a8db0 25%, #ffffff 50%, #5a8db0 75%, #0f2a3d 100%),
        /* Setas mais visíveis */
        repeating-linear-gradient(
            180deg,
            transparent 0px,
            transparent 6px,
            rgba(255, 255, 255, 0.6) 6px,
            rgba(255, 255, 255, 0.6) 8px,
            transparent 8px,
            transparent 10px,
            rgba(255, 255, 255, 0.4) 10px,
            rgba(255, 255, 255, 0.4) 12px
        ),
        /* Brilho mais intenso */
        linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.4) 20%,
            rgba(255, 255, 255, 0.8) 50%,
            rgba(255, 255, 255, 0.4) 80%,
            transparent 100%
        );

    box-shadow:
        0 0 20px rgba(27, 68, 100, 0.6),
        inset 0 2px 0 rgba(255, 255, 255, 0.7),
        inset 0 -2px 0 rgba(0, 0, 0, 0.1),
        0 6px 25px rgba(112, 150, 179, 0.5);

    transform: scaleX(1.1);
    animation: none; /* Para a animação durante o hover */
}

.analysis-list-content::-webkit-scrollbar-thumb:active {
    background-image:
        linear-gradient(180deg, #0a1f2e 0%, #4a7a9d 25%, #ffffff 50%, #4a7a9d 75%, #0a1f2e 100%),
        repeating-linear-gradient(
            180deg,
            transparent 0px,
            transparent 4px,
            rgba(255, 255, 255, 0.8) 4px,
            rgba(255, 255, 255, 0.8) 6px,
            transparent 6px,
            transparent 8px,
            rgba(255, 255, 255, 0.6) 8px,
            rgba(255, 255, 255, 0.6) 10px
        ),
        linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.5) 20%,
            rgba(255, 255, 255, 1) 50%,
            rgba(255, 255, 255, 0.5) 80%,
            transparent 100%
        );

    box-shadow:
        0 0 30px rgba(27, 68, 100, 0.8),
        inset 0 3px 0 rgba(255, 255, 255, 0.8),
        inset 0 -3px 0 rgba(0, 0, 0, 0.2),
        0 8px 35px rgba(112, 150, 179, 0.7);

    transform: scaleX(1.15);
}

/* Tabela de análises no modal */
.analises-table-modal {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.analises-table-modal td {
    padding: 12px 16px !important;
    font-size: 0.9rem;
    color: #444 !important;
    text-align: left;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
}

.analises-table-modal tr:last-child td {
    border-bottom: none;
}

.analises-table-modal .analysis-question {
    border-right: 1px solid #eee;
    font-weight: 600 !important;
    color: #333 !important;
    background-color: #fafbfc;
    width: 50%;
}

.analises-table-modal .analysis-answer {
    font-weight: 400 !important;
    width: 50%;
}

.analises-table-modal tr:nth-of-type(odd) .analysis-answer {
    background: #f9fafc;
}

.analises-table-modal tr:hover > * {
    background-color: #f0f7ff !important;
}

/* Estilos para inputs e selects no modal */
.custom-select-modal {
    min-width: 180px;
    padding: 6px 10px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    background-color: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    font-size: 0.85rem;
    color: #495057;
}

.custom-select-modal:focus {
    border-color: #1B4464;
    box-shadow: 0 0 0 2px rgba(27, 68, 100, 0.1);
    outline: none;
}

.input-sm-modal {
    background: #f8f9fa;
    border-radius: 4px;
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
    padding: 6px 10px;
    font-size: 0.85rem;
    width: 100%;
    margin-top: 8px;
}

.input-sm-modal:focus {
    background: #ffffff;
    box-shadow: 0 0 0 2px rgba(27, 68, 100, 0.1);
    border-color: #1B4464;
}

.checkbox-container-modal {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    font-size: 0.85rem;
}

.checkbox-container-modal input[type="checkbox"] {
    width: 14px;
    height: 14px;
    margin-right: 6px;
    accent-color: #1B4464;
}

.checkbox-container-modal label {
    margin-bottom: 0;
    font-size: 0.85rem;
    cursor: pointer;
}

.btn-save-modal {
    padding: 8px 20px;
    font-weight: 500;
    border-radius: 6px;
    box-shadow: 0 3px 6px rgba(27, 68, 100, 0.15);
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #1B4464, #56809F);
    border: none;
    color: white;
}

.btn-save-modal:hover {
    box-shadow: 0 4px 8px rgba(27, 68, 100, 0.25);
    transform: translateY(-1px);
    background: linear-gradient(135deg, #28597e, #7aa3c0);
}

/* Estilos para botões flutuantes */
.floating-button-container {
    position: absolute;
    bottom: 20px;
    z-index: 15;
}

.floating-button-container.right {
    right: 20px;
}

.floating-button {
    padding: 12px 24px;
    border-radius: 25px;
    border: none;
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 140px;
}

.floating-button.blue {
    background: linear-gradient(135deg, #1B4464, #56809F);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.floating-button.blue:hover {
    background: linear-gradient(135deg, #28597e, #7aa3c0);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(27, 68, 100, 0.3);
}

.analysis-list-content {
    padding-bottom: 80px; /* Espaço para o botão flutuante */
}

/* Transições do modal */
.modal-fade-enter-active,
.modal-fade-leave-active {
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.modal-fade-enter-from,
.modal-fade-leave-to {
    opacity: 0;
    transform: scale(0.9);
}

.modal-fade-enter-active .analysis-modal-container,
.modal-fade-leave-active .analysis-modal-container {
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.modal-fade-enter-from .analysis-modal-container,
.modal-fade-leave-to .analysis-modal-container {
    transform: scale(0.8) translateY(50px);
    opacity: 0;
}

/* Correção do z-index do v-viewer para aparecer sobre o modal */
.viewer-container {
    z-index: 99999 !important;
}

.viewer-backdrop {
    z-index: 99998 !important;
}

/* Estilos específicos do viewer.js */
:deep(.viewer-container) {
    z-index: 99999 !important;
}

:deep(.viewer-backdrop) {
    z-index: 99998 !important;
}

:deep(.viewer-canvas) {
    z-index: 99999 !important;
}

:deep(.viewer-toolbar) {
    z-index: 100000 !important;
}

/* Responsividade para o modal */
@media (max-width: 768px) {
    .analysis-modal-container {
        width: 98vw;
        height: 95vh;
        border-radius: 12px;
    }

    .analysis-modal-content {
        flex-direction: column;
    }

    .analysis-carousel-container {
        flex: 0 0 40%;
        border-right: none;
        border-bottom: 1px solid #e9ecef;
    }

    .analysis-list-container {
        flex: 1;
    }

    .analysis-carousel-header-white {
        padding: 15px;
    }

    .analysis-list-header {
        padding: 15px;
    }

    .analysis-list-header h5 {
        font-size: 1rem;
    }

    .analysis-carousel-wrapper {
        padding: 15px 15px 70px 15px;
    }

    .analysis-list-content {
        padding: 15px 15px 70px 15px;
    }

    /* Scrollbar adaptada para tablets */
    .analysis-list-content::-webkit-scrollbar {
        width: 18px;
    }

    .analysis-list-content::-webkit-scrollbar-thumb {
        min-height: 50px;
        border: 2px solid #ffffff;
    }

    /* Indicador de progresso para tablets */
    .analysis-list-container::after {
        right: 22px;
        width: 3px;
    }

    .analysis-list-container::before {
        right: 18px;
        font-size: 0.65rem;
    }

    .analysis-carousel-item-vertical img {
        height: 200px;
        object-fit: contain;
    }

    .analysis-list-content {
        padding: 15px;
    }

    .analises-table-modal td {
        padding: 10px 12px !important;
        font-size: 0.85rem;
    }

    .analysis-modal-close {
        top: 15px;
        right: 15px;
        width: 36px;
        height: 36px;
    }
}

@media (max-width: 576px) {
    .analysis-modal-container {
        width: 100vw;
        height: 100vh;
        border-radius: 0;
    }

    .analysis-carousel-container {
        flex: 0 0 35%;
    }

    .analysis-carousel-header-white {
        padding: 12px;
    }

    .analysis-list-header {
        padding: 12px;
    }

    .analysis-carousel-wrapper {
        padding: 12px 12px 60px 12px;
    }

    .analysis-list-content {
        padding: 12px 12px 60px 12px;
    }

    /* Scrollbar para dispositivos pequenos */
    .analysis-list-content::-webkit-scrollbar {
        width: 16px;
    }

    .analysis-list-content::-webkit-scrollbar-thumb {
        min-height: 40px;
        border: 2px solid #ffffff;
    }

    /* Indicador de progresso para dispositivos pequenos */
    .analysis-list-container::after {
        right: 19px;
        width: 3px;
    }

    .analysis-list-container::before {
        right: 15px;
        font-size: 0.6rem;
    }

    .floating-button {
        padding: 10px 20px;
        font-size: 0.85rem;
        min-width: 120px;
    }

    .floating-button-icon {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }

    .analysis-carousel-item-vertical img {
        height: 150px;
        object-fit: contain;
    }

    .analysis-list-content {
        padding: 12px;
    }

    /* Scrollbar mínima mas ainda visível em telas muito pequenas */
    .analysis-list-content::-webkit-scrollbar {
        width: 14px;
    }

    .analysis-list-content::-webkit-scrollbar-thumb {
        min-height: 35px;
        border: 1px solid #ffffff;
    }

    /* Indicador de progresso para telas muito pequenas */
    .analysis-list-container::after {
        right: 17px;
        width: 2px;
    }

    .analysis-list-container::before {
        right: 14px;
        font-size: 0.55rem;
        padding: 1px 4px;
    }

    .analises-table-modal td {
        padding: 8px 10px !important;
        font-size: 0.8rem;
    }

    .analysis-play-icon-wrapper {
        width: 28px;
        height: 28px;
        margin: 0;
    }

    .analysis-play-icon {
        font-size: 12px;
    }

    .header-actions {
        gap: 6px;
    }
}
</style>

<script>
import MaterialInput from '@/components/MaterialInput.vue'
import { getAnalises, salvarAnalises } from '@/services/tratamentosService'
import cSwal from "@/utils/cSwal.js"
import { analises } from '@/helpers/planejamento/analises.js'

var isEditing = []

export default {
    name: "Analise",
    props: {
        paciente: {
            type: Object,
            default: () => { return {} },
        },
        detalhesClinicos: {
            type: Array,
            default: null,
        },
    },
    emits: ['selectTab'],
    data() {
        return {
            isEditing,
            analises,
            // Modal de análise
            showAnalysisModal: false,
            currentAnalysisType: null
        }
    },
    computed: {
        // Filtra imagens de diagnóstico do paciente
        diagnosticImages() {
            const images = Array.isArray(this.paciente?.imagens) ? this.paciente.imagens : [];
            return images.filter(img => {
                return img.is_diagnostico === true || img.is_diagnostico === 1;
            });
        },
        // Imagens para análise extra-bucal
        extraBucalImages() {
            return this.diagnosticImages.filter(img => {
                const tag = img.tag_diagnostico || '';
                return tag.startsWith('extra_');
            });
        },
        // Imagens para análise intra-bucal
        intraBucalImages() {
            return this.diagnosticImages.filter(img => {
                const tag = img.tag_diagnostico || '';
                return tag.startsWith('intra_');
            });
        },
        // Imagens para análises radiográficas
        radiograficasImages() {
            return this.diagnosticImages.filter(img => {
                const tag = img.tag_diagnostico || '';
                return tag.startsWith('radio_');
            });
        },
        // Imagens para o tipo de análise atual
        currentAnalysisImages() {
            switch (this.currentAnalysisType) {
                case 'extraBucal': {
                    // Para extra-bucal, incluir apenas as fotos extra-bucais e a telerradiografia (excluir panorâmica)
                    const telerradiografiaImages = this.radiograficasImages.filter(img =>
                        img.tag_diagnostico === 'radio_telerradiografia'
                    );
                    return [...this.extraBucalImages, ...telerradiografiaImages];
                }
                case 'intraBucal':
                    return this.intraBucalImages;
                case 'analisesRadiograficas':
                    return this.radiograficasImages;
                default:
                    return [];
            }
        },
        // Verifica se a análise extra-bucal está majoritariamente preenchida
        isExtraBucalMostlyFilled() {
            return this.isAnalysisMostlyFilled('Extra-bucal');
        },
        // Verifica se a análise intra-bucal está majoritariamente preenchida
        isIntraBucalMostlyFilled() {
            return this.isAnalysisMostlyFilled('Intra-bucal');
        },
        // Verifica se as análises radiográficas estão majoritariamente preenchidas
        isRadiograficasMostlyFilled() {
            return this.isAnalysisMostlyFilled('Radiográficas');
        }
    },
    methods: {
        gerarDiagnostico() {
            this.$emit('selectTab', 'diagnostico');
        },
        async confirmSaveAnalises() {
            cSwal.cConfirm('Deseja realmente <b>salvar as alterações</b>? As informações anteriores serão sobrescritas.', async () => {
                await this._salvarAnalises();
            })
        },
        async _getAnalises() {
            const analises = await getAnalises(this.paciente.id)

            if (analises) {
                this.originalAnalises = JSON.parse(JSON.stringify(analises));
                this.analises = JSON.parse(JSON.stringify(analises));
                this.handleAnalisesUpdate()
            }
        },
        async _salvarAnalises() {
            cSwal.loading('Salvando as alterações...')
            const save = await salvarAnalises(this.analises, this.paciente.id)
            cSwal.loaded()

            if (save) {
                cSwal.cSuccess('As alterações na análise foram salvas.')
                this.isEditing['extraBucal'] = false
                this.isEditing['intraBucal'] = false
                this.isEditing['analisesRadiograficas'] = false
                this.$emit('pacienteChange')
            }
            else {
                cSwal.cError('Ocorreu um erro ao salvar as alterações.')
            }

        },
        toggleEditMode(section) {
            const editingSection = Object.keys(this.isEditing).find(key => this.isEditing[key]);

            let editingSectionStr = ''
            switch (editingSection) {
                case 'extraBucal':
                    editingSectionStr = 'EXTRA-BUCAL'
                    break;
                case 'intraBucal':
                    editingSectionStr = 'INTRA-BUCAL'
                    break;
                case 'analisesRadiograficas':
                    editingSectionStr = 'ANÁLISES RADIOGRÁFICAS'
                    break;
            }
            let togglingSectionStr = ''
            switch (section) {
                case 'extraBucal':
                    togglingSectionStr = 'EXTRA-BUCAL'
                    break;
                case 'intraBucal':
                    togglingSectionStr = 'INTRA-BUCAL'
                    break;
                case 'analisesRadiograficas':
                    togglingSectionStr = 'ANÁLISES RADIOGRÁFICAS'
                    break;
            }

            if (this.isEditing[section]) {
                cSwal.cConfirm(`Deseja realmente <b>cancelar a edição</b> da análise ${editingSectionStr}? As alterações serão perdidas.`, () => {
                    this.isEditing[section] = false
                    if (this.originalAnalises) {
                        this.analises = JSON.parse(JSON.stringify(this.originalAnalises))
                        this.updateRespostas()
                    }
                })
                return
            }

            if (editingSection && editingSection !== section) {
                cSwal.cWarning(`Finalize a edição da seção ${editingSectionStr} antes de editar esta.`);
                return;
            }

            this.isEditing[section] = !this.isEditing[section];

            // Se tiver cancelando a edição, volta a apresentar o objeto original na tela (perde as alterações)
            if (!this.isEditing[section]) {
                this.analises = JSON.parse(JSON.stringify(this.originalAnalises))
            }
            // Se tiver entrando em modo de edição
            else {
                cSwal.cAlert('<div style="text-align: justify;">Você abriu o modo de edição da seção <b>' + togglingSectionStr + '</b>.<br><br>Quando as alterações forem salvas, o Lumi Plan poderá definir novas sugestões de tratamento para este caso.</div>')
            }
        },
        handleAnalisesUpdate() {
            this.updateRespostas()
            this.updateNivel()
        },
        updateRespostas() {
            Object.values(this.analises).forEach((categoria) => {
                categoria.forEach((analise) => {
                    let resposta = '';

                    if (analise.tipo === 'unica_escolha') {
                        const alternativaSelecionada = analise.alternativas.find(alternativa => alternativa.selecionada);

                        analise.selectedResposta = analise.selectedResposta ? analise.selectedResposta : (alternativaSelecionada ? alternativaSelecionada.resposta : null)

                        if (analise.selectedResposta && analise.selectedResposta != 'detalhe') {
                            resposta = analise.selectedResposta.trim()
                        }
                        else if (analise.selectedResposta && analise.selectedResposta == 'detalhe') {
                            resposta = analise.detalhe.trim()
                        }
                        else if (analise.detalhe) {
                            resposta = analise.detalhe
                            analise.selectedResposta = 'detalhe'
                            analise.detalhar = true
                        }
                        else {
                            analise.selectedResposta = undefined
                        }

                        analise.alternativas.forEach((alternativa) => {
                            if (analise.selectedResposta == alternativa.resposta) {
                                alternativa.selecionada = true
                            }
                            else {
                                alternativa.selecionada = false
                            }
                        });
                    }

                    else if (analise.tipo === 'multipla_escolha') {
                        const selectedAlternativas = analise.alternativas.filter((alternativa) => alternativa.selecionada);

                        if (selectedAlternativas.length > 0)
                            resposta = selectedAlternativas.map((alternativa) => alternativa.resposta.trim()).join(', ').trim()

                        if (analise.detalhar && analise.detalhe.trim())
                            resposta += resposta ? `, ${analise.detalhe.trim()}` : analise.detalhe.trim()
                    }

                    analise.respostas = resposta;
                });
            });
        },
        updateNivel() {
            Object.values(this.analises).forEach((categoria) => {
                categoria.forEach((analise) => {
                    if (analise.respostas) {
                        const selectedAlternativa = analise.alternativas.find((alternativa) => alternativa.resposta === analise.respostas);
                        if (selectedAlternativa) {
                            analise.nivel = selectedAlternativa.nivel;
                        } else {
                            analise.nivel = 'neutro';
                        }
                    }

                    if (analise.tipo === 'multipla_escolha') {
                        const selectedAlternativas = analise.alternativas.filter((alternativa) => alternativa.selecionada);

                        if (selectedAlternativas.length == 0)
                            analise.nivel = 'neutro'
                        else {
                            const piorNivel = selectedAlternativas.reduce((pior, atual) => {
                                const niveis = ['negativo', 'atencao', 'neutro', 'positivo'];
                                return niveis.indexOf(atual.nivel) < niveis.indexOf(pior) ? atual.nivel : pior;
                            }, 'positivo'); // inicializa com o melhor nível possível
                            analise.nivel = piorNivel;
                        }
                    }
                });
            });
        },
        getInfoIcon(type) {
            var icon = null
            switch (type) {
                case 'positivo':
                    icon = 'thumbs-up'
                    break
                case 'negativo':
                    icon = 'thumbs-down'
                    break
                case 'atencao':
                    icon = 'circle-exclamation'
                    break
                case 'neutro':
                    icon = 'info-circle'
                    break
            }

            return icon
        },

        // Métodos para o modal de análise
        startAnalysisVisualization(analysisType) {
            this.currentAnalysisType = analysisType;
            this.showAnalysisModal = true;

            // Prevenir scroll do body quando o modal estiver aberto
            document.body.style.overflow = 'hidden';

            // Verificar scroll após o modal ser renderizado
            this.$nextTick(() => {
                this.checkScrollIndicator();
            });
        },

        closeAnalysisModal() {
            this.showAnalysisModal = false;
            this.currentAnalysisType = null;

            // Restaurar scroll do body
            document.body.style.overflow = '';
        },

        // Verifica se há conteúdo para rolar e adiciona/remove a classe indicadora
        checkScrollIndicator() {
            const analysisListContent = document.querySelector('.analysis-list-content');
            const analysisListContainer = document.querySelector('.analysis-list-container');

            if (analysisListContent && analysisListContainer) {
                const hasScroll = analysisListContent.scrollHeight > analysisListContent.clientHeight;

                if (hasScroll) {
                    analysisListContainer.classList.add('has-scroll');
                } else {
                    analysisListContainer.classList.remove('has-scroll');
                }

                // Inicializar o progresso
                this.updateScrollIndicator();

                // Adicionar listener para atualizar o indicador quando o usuário rolar
                analysisListContent.addEventListener('scroll', this.updateScrollIndicator);
            }
        },

        // Atualiza o indicador baseado na posição do scroll
        updateScrollIndicator() {
            const analysisListContent = document.querySelector('.analysis-list-content');
            const analysisListContainer = document.querySelector('.analysis-list-container');

            if (analysisListContent && analysisListContainer) {
                const { scrollTop, scrollHeight, clientHeight } = analysisListContent;

                // Calcular progresso do scroll (0% a 100%)
                const maxScroll = scrollHeight - clientHeight;
                const scrollProgress = maxScroll > 0 ? (scrollTop / maxScroll) * 100 : 0;

                // Atualizar a variável CSS para o progresso da barra guia
                analysisListContainer.style.setProperty('--scroll-progress', `${scrollProgress}%`);

                // Adicionar classe para indicar se há conteúdo para rolar
                const hasScroll = scrollHeight > clientHeight;
                if (hasScroll) {
                    analysisListContainer.classList.add('has-scroll');
                } else {
                    analysisListContainer.classList.remove('has-scroll');
                }

                // Atualizar o texto do indicador baseado no progresso
                const progressText = scrollProgress < 10 ? 'Início da Ficha' :
                                   scrollProgress > 90 ? 'Final da Ficha' :
                                   `${Math.round(scrollProgress)}% Concluído`;

                // Atualizar o atributo data para o CSS usar no ::before
                analysisListContainer.setAttribute('data-progress-text', progressText);
            }
        },



        getAnalysisTitle(analysisType) {
            switch (analysisType) {
                case 'extraBucal':
                    return 'Análise Extra-bucal';
                case 'intraBucal':
                    return 'Análise Intra-bucal';
                case 'analisesRadiograficas':
                    return 'Análises Radiográficas';
                default:
                    return 'Análise';
            }
        },

        getCarouselTitle(analysisType) {
            switch (analysisType) {
                case 'extraBucal':
                    return 'Fotos e radiografias do paciente';
                case 'intraBucal':
                    return 'Fotos intra-bucais do paciente';
                case 'analisesRadiograficas':
                    return 'Radiografias do paciente';
                default:
                    return 'Imagens do paciente';
            }
        },

        getCurrentAnalysisList() {
            switch (this.currentAnalysisType) {
                case 'extraBucal':
                    return this.analises['Extra-bucal'] || [];
                case 'intraBucal':
                    return this.analises['Intra-bucal'] || [];
                case 'analisesRadiograficas':
                    return this.analises['Radiográficas'] || [];
                default:
                    return [];
            }
        },

        formatImageTag(tag) {
            if (!tag) return '';

            // Remove prefixos e formata o texto
            const formatted = tag
                .replace(/^(extra_|intra_|radio_)/, '')
                .replace(/_/g, ' ')
                .replace(/\b\w/g, l => l.toUpperCase());

            return formatted;
        },

        // Métodos para organizar imagens intra-bucais em linhas
        getIntraBucalRow1Images() {
            const row1Tags = ['intra_lateral_direita', 'intra_frontal', 'intra_lateral_esquerda'];
            return this.intraBucalImages.filter(img =>
                row1Tags.includes(img.tag_diagnostico)
            ).sort((a, b) => {
                const indexA = row1Tags.indexOf(a.tag_diagnostico);
                const indexB = row1Tags.indexOf(b.tag_diagnostico);
                return indexA - indexB;
            });
        },

        getIntraBucalRow2Images() {
            const row2Tags = ['intra_oclusal_superior', 'intra_oclusal_inferior'];
            return this.intraBucalImages.filter(img =>
                row2Tags.includes(img.tag_diagnostico)
            ).sort((a, b) => {
                const indexA = row2Tags.indexOf(a.tag_diagnostico);
                const indexB = row2Tags.indexOf(b.tag_diagnostico);
                return indexA - indexB;
            });
        },

        getImageDescription(image) {
            if (!image) return '';
            return image.descricao || image.tag_diagnostico || 'Imagem';
        },

        // Método helper para verificar se uma análise está majoritariamente preenchida
        isAnalysisMostlyFilled(categoria) {
            const analisesCategoria = this.analises[categoria];
            if (!analisesCategoria || analisesCategoria.length === 0) {
                return false;
            }

            const total = analisesCategoria.length;
            const filledCount = analisesCategoria.filter(analise => {
                // Considera preenchido se tem resposta ou se tem detalhes preenchidos
                return (analise.respostas && analise.respostas.trim() !== '') ||
                       (analise.detalhe && analise.detalhe.trim() !== '');
            }).length;

            // Considera majoritariamente preenchido se metade ou mais dos itens estão preenchidos
            return filledCount >= Math.ceil(total / 2);
        },
    },
    watch: {
        // Observa mudanças no modal para revalidar o scroll
        showAnalysisModal(newVal) {
            if (newVal) {
                this.$nextTick(() => {
                    this.checkScrollIndicator();
                });
            }
        },

        // Observa mudanças nas análises que podem afetar a altura do conteúdo
        analises: {
            handler() {
                if (this.showAnalysisModal) {
                    this.$nextTick(() => {
                        this.checkScrollIndicator();
                    });
                }
            },
            deep: true
        }
    },
    components: {
        MaterialInput,
    },
    async mounted() {
        await this._getAnalises();
    },
    beforeMount() {
    },
    beforeUnmount() {
        // Garantir que o overflow do body seja restaurado
        document.body.style.overflow = '';

        // Remover event listeners
        const analysisListContent = document.querySelector('.analysis-list-content');
        if (analysisListContent) {
            analysisListContent.removeEventListener('scroll', this.updateScrollIndicator);
        }
    }
};
</script>