<?php

namespace App\Http\Controllers;

use App\Models\ServicoProduto;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ServicoProdutoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $query = ServicoProduto::daClinica($clinicaId);

        // Filtros

        if ($request->has('tipo')) {
            $query->porTipo($request->tipo);
        }

        if ($request->has('ativo')) {
            if ($request->ativo == '1') {
                $query->ativos();
            } else {
                $query->where('ativo', false);
            }
        }

        if ($request->has('busca')) {
            $query->buscar($request->busca);
        }

        $servicosProdutos = $query->orderBy('nome')
                                 ->paginate(20);

        return responseSuccess(['data' => $servicosProdutos]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $validator = Validator::make($request->all(), [
            'dentista_id' => 'nullable|exists:dentistas,id',
            'codigo' => 'nullable|string|max:50|unique:servicos_produtos,codigo,NULL,id,clinica_id,' . $clinicaId,
            'nome' => 'required|string|max:255',
            'descricao' => 'nullable|string',
            'tipo' => 'required|in:servico,produto,procedimento',
            'valor_base' => 'required|numeric|min:0.01',
            'valor_minimo' => 'nullable|numeric|min:0',
            'valor_maximo' => 'nullable|numeric|min:0',
            'unidade' => 'nullable|string|max:20',
            'tempo_estimado' => 'nullable|integer|min:1',
            'observacoes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return responseError([
                'message' => 'Dados inválidos',
                'data' => $validator->errors(),
                'statusCode' => 422
            ]);
        }

        // Validar se valor_minimo <= valor_base <= valor_maximo
        if ($request->valor_minimo && $request->valor_minimo > $request->valor_base) {
            return responseError([
                'message' => 'Valor mínimo não pode ser maior que o valor base',
                'statusCode' => 422
            ]);
        }

        if ($request->valor_maximo && $request->valor_maximo < $request->valor_base) {
            return responseError([
                'message' => 'Valor máximo não pode ser menor que o valor base',
                'statusCode' => 422
            ]);
        }

        try {
            $servicoProduto = ServicoProduto::create([
                'clinica_id' => $clinicaId,
                'dentista_id' => $request->dentista_id,
                'codigo' => $request->codigo,
                'nome' => $request->nome,
                'descricao' => $request->descricao,
                'tipo' => $request->tipo,
                'valor_base' => $request->valor_base,
                'valor_minimo' => $request->valor_minimo,
                'valor_maximo' => $request->valor_maximo,
                'unidade' => $request->unidade ?? 'un',
                'tempo_estimado' => $request->tempo_estimado,
                'observacoes' => $request->observacoes,
                'ativo' => true,
            ]);

            return responseSuccess([
                'message' => 'Serviço/Produto criado com sucesso',
                'data' => $servicoProduto
            ]);

        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao criar serviço/produto: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $servicoProduto = ServicoProduto::daClinica($clinicaId)
            ->findOrFail($id);

        return responseSuccess(['data' => $servicoProduto]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $servicoProduto = ServicoProduto::daClinica($clinicaId)->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'dentista_id' => 'nullable|exists:dentistas,id',
            'codigo' => 'nullable|string|max:50|unique:servicos_produtos,codigo,' . $id . ',id,clinica_id,' . $clinicaId,
            'nome' => 'sometimes|string|max:255',
            'descricao' => 'nullable|string',
            'tipo' => 'sometimes|in:servico,produto,procedimento',
            'valor_base' => 'sometimes|numeric|min:0.01',
            'valor_minimo' => 'nullable|numeric|min:0',
            'valor_maximo' => 'nullable|numeric|min:0',
            'unidade' => 'nullable|string|max:20',
            'tempo_estimado' => 'nullable|integer|min:1',
            'observacoes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return responseError([
                'message' => 'Dados inválidos',
                'data' => $validator->errors(),
                'statusCode' => 422
            ]);
        }

        // Validar faixa de valores
        $valorBase = $request->get('valor_base', $servicoProduto->valor_base);
        $valorMinimo = $request->get('valor_minimo', $servicoProduto->valor_minimo);
        $valorMaximo = $request->get('valor_maximo', $servicoProduto->valor_maximo);

        if ($valorMinimo && $valorMinimo > $valorBase) {
            return responseError([
                'message' => 'Valor mínimo não pode ser maior que o valor base',
                'statusCode' => 422
            ]);
        }

        if ($valorMaximo && $valorMaximo < $valorBase) {
            return responseError([
                'message' => 'Valor máximo não pode ser menor que o valor base',
                'statusCode' => 422
            ]);
        }

        try {
            $servicoProduto->update($request->only([
                'codigo', 'nome', 'descricao', 'tipo',
                'valor_base', 'valor_minimo', 'valor_maximo', 'unidade',
                'tempo_estimado', 'observacoes'
            ]));

            return responseSuccess([
                'message' => 'Serviço/Produto atualizado com sucesso',
                'data' => $servicoProduto
            ]);

        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao atualizar serviço/produto: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $servicoProduto = ServicoProduto::daClinica($clinicaId)->findOrFail($id);

        // Verificar se está sendo usado em orçamentos
        if ($servicoProduto->getQuantidadeUsadaOrcamentos() > 0) {
            return responseError([
                'message' => 'Este serviço/produto não pode ser excluído pois está sendo usado em orçamentos',
                'statusCode' => 422
            ]);
        }

        try {
            $servicoProduto->delete();

            return responseSuccess(['message' => 'Serviço/Produto excluído com sucesso']);
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao excluir serviço/produto: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Ativar/Desativar serviço/produto
     */
    public function toggleStatus(string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $servicoProduto = ServicoProduto::daClinica($clinicaId)->findOrFail($id);

        try {
            if ($servicoProduto->ativo) {
                $servicoProduto->desativar();
                $message = 'Serviço/Produto desativado com sucesso';
            } else {
                $servicoProduto->ativar();
                $message = 'Serviço/Produto ativado com sucesso';
            }

            return responseSuccess([
                'message' => $message,
                'data' => $servicoProduto->fresh()
            ]);

        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao alterar status: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Obter tipos disponíveis
     */
    public function tipos()
    {
        return responseSuccess(['data' => ServicoProduto::getTipos()]);
    }

    /**
     * Buscar serviços/produtos para orçamento
     */
    public function buscarParaOrcamento(Request $request)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $query = ServicoProduto::daClinica($clinicaId)
            ->ativos();

        if ($request->has('busca')) {
            $query->buscar($request->busca);
        }

        if ($request->has('tipo')) {
            $query->porTipo($request->tipo);
        }

        $servicos = $query->orderBy('nome')
                         ->limit(50)
                         ->get();

        return responseSuccess(['data' => $servicos]);
    }
}
