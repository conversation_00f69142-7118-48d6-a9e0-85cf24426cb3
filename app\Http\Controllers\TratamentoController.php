<?php

namespace App\Http\Controllers;

use App\Models\Analise;
use App\Models\MetaTerapeutica;
use App\Models\Paciente;
use App\Models\Dentista;
use App\Models\HistoricoPaciente;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Utils;
use App\Models\TratamentoSugerido;
use App\Models\FatorClinico;
use App\Models\FatorDiagnostico;

class TratamentoController extends Controller
{

    public function iniciarTratamento(Request $request, $idPaciente)
    {
        try {
            $body = $request->all();
            $paciente = Paciente::find($idPaciente);
            $paciente->data_planejamento = $body['dataPlanejamento'];
            $paciente->data_inicio_tratamento = $body['dataInicio'];
            $paciente->data_final_prevista = $body['dataPrevista'];
            $paciente->status_tratamento = 'ATIVO';
            $paciente->save();

            // Criar um registro de histórico para o início do tratamento
            $historico = new HistoricoPaciente();
            $historico->paciente_id = $paciente->id;
            $historico->data = now()->format('Y-m-d');
            $historico->horario = now()->format('H:i:s');
            $historico->codigo_acao = 'Início do tratamento';

            // Formatar as datas para exibição
            $dataInicio = date('d/m/Y', strtotime($body['dataInicio']));
            $dataPrevista = date('d/m/Y', strtotime($body['dataPrevista']));

            $historico->descricao = "O tratamento foi iniciado em {$dataInicio} com previsão de término em {$dataPrevista}.";

            $historico->modificacoes = [
                'data_planejamento' => $body['dataPlanejamento'],
                'data_inicio' => $body['dataInicio'],
                'data_prevista' => $body['dataPrevista']
            ];
            $historico->user_id = auth()->user()->id;
            $historico->referente_tratamento = true;
            $historico->save();

            $response = responseSuccess();
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function addMetaTerapeutica(Request $request)
    {
        try {
            $body = $request->all();
            $meta = new MetaTerapeutica();
            $meta->paciente_id = $body['paciente_id'];
            $meta->descricao = $body['descricao'];
            $meta->save();

            $response = responseSuccess();
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function deleteMetaTerapeutica(Request $request, $id)
    {
        try {
            $body = $request->all();
            $meta = MetaTerapeutica::find($id);
            if (!$meta)
                throw new \Exception('Meta terapeutica não encontrada');

            // Salvar os dados da meta antes de excluí-la
            $metaData = $meta->toArray();
            $pacienteId = $meta->paciente_id;
            $descricao = $meta->descricao;

            $meta->delete();

            $response = responseSuccess();
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function salvarAnalises(Request $request)
    {
        $body = $request->all();

        try {
            $analisesRespostas = $this->getAnalisesRespostas($body['analises'], $body['paciente_id']);

            $fatoresClinicosTags = $this->getFatoresClinicosTags($analisesRespostas);

            $fatoresDiagnosticoTags = $this->getFatoresDiagnosticoTags($analisesRespostas);

            $allFatoresClinicos = FatorClinico::all();
            $fatoresClinicosIds = [];
            foreach ($allFatoresClinicos as $fatorClinico) {
                if (in_array($fatorClinico['tag'], $fatoresClinicosTags)) {
                    $fatoresClinicosIds[] = $fatorClinico['id'];
                }
            }

            $allFatoresDiagnostico = FatorDiagnostico::all();
            $fatoresDiagnosticoIds = [];
            $fatoresDiagnosticoPaciente = [];
            foreach ($allFatoresDiagnostico as $fatorDiagnostico) {
                if (in_array($fatorDiagnostico['tag'], $fatoresDiagnosticoTags)) {
                    $fatoresDiagnosticoIds[] = $fatorDiagnostico['id'];
                    $fatoresDiagnosticoPaciente[] = $fatorDiagnostico['fator_diagnostico'];
                }
            }

            // $iaAnalysis = $this->getIaAnalysis($fatoresDiagnosticoPaciente, true);

            $tratamentosSugeridosResponse = $this->getTratamentosSugeridos($fatoresClinicosTags);

            $tratamentosSugeridos = $tratamentosSugeridosResponse['tratamentos_sugeridos'];
            $allTratamentosSugeridos = TratamentoSugerido::all();
            $tratamentosSugeridosIds = [];
            foreach ($allTratamentosSugeridos as $tratamentoSugerido) {
                if (in_array($tratamentoSugerido['tag'], $tratamentosSugeridos)) {
                    $tratamentosSugeridosIds[] = $tratamentoSugerido['id'];
                }
            }

            $fatoresConsiderados = $tratamentosSugeridosResponse['fatores_considerados'];

            DB::transaction(function () use ($body, $tratamentosSugeridosIds, $fatoresClinicosIds, $fatoresDiagnosticoIds, $fatoresConsiderados) {
                // Delete existing analyses for the paciente_id
                Analise::where('paciente_id', $body['paciente_id'])->delete();

                // Instantiate the paciente model
                $paciente = Paciente::find($body['paciente_id']);

                // $paciente->diagnostico = $iaAnalysis['diagnostico'];

                $paciente->fatores_considerados = json_encode($fatoresConsiderados);

                // Detach existing data
                $paciente->fatores_clinicos()->detach();
                $paciente->tratamentos_sugeridos()->detach();
                $paciente->fatores_diagnostico()->detach();

                // Attach new data
                $paciente->fatores_clinicos()->attach($fatoresClinicosIds);
                $paciente->tratamentos_sugeridos()->attach($tratamentosSugeridosIds);
                $paciente->fatores_diagnostico()->attach($fatoresDiagnosticoIds);

                // Save the paciente model
                $paciente->save();

                $body['analises'] = json_decode($body['analises']);

                $dentista = Dentista::where('user_id', auth()->user()->id)->first();

                foreach ($body['analises'] as $categoria => $analises) {
                    foreach ($analises as $analise) {
                        $analiseModel = new Analise();
                        $analiseModel->paciente_id = $body['paciente_id'];
                        $analiseModel->dentista_id = isset($dentista->id) ? $dentista->id : NULL;
                        $analiseModel->tipo = $analise->tipo;
                        $analiseModel->categoria = $categoria;
                        $analiseModel->analise = $analise->analise;
                        $analiseModel->alternativas = json_encode($analise->alternativas);
                        $analiseModel->respostas = $analise->respostas ? $analise->respostas : '';
                        $analiseModel->detalhar = $analise->detalhar;
                        $analiseModel->detalhe = isset($analise->detalhe) ? $analise->detalhe : '';
                        $analiseModel->titulo_detalhe = $analise->titulo_detalhe;
                        $analiseModel->nivel = $analise->nivel;
                        $analiseModel->save();
                    }
                }
            });

            $response = responseSuccess();
        } catch (\Exception $e) {
            // Log::error($e->getMessage());
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    private function getFatoresDiagnosticoTags($ar)
    {
        $tags = [];

        // Extra-bucal rules
        if (isset($ar['Extra-bucal.Biotipo facial'])) {
            if ($ar['Extra-bucal.Biotipo facial'] === 'braquicefálico') {
                $tags[] = 'biotipo_facial_braquicefalico';
            } elseif ($ar['Extra-bucal.Biotipo facial'] === 'mesocefálico') {
                $tags[] = 'biotipo_facial_mesocefalico';
            } elseif ($ar['Extra-bucal.Biotipo facial'] === 'dolicocefálico') {
                $tags[] = 'biotipo_facial_dolicocefalico';
            } elseif ($ar['Extra-bucal.Biotipo facial'] === 'face curta') {
                $tags[] = 'biotipo_facial_face_curta';
            } elseif ($ar['Extra-bucal.Biotipo facial'] === 'face longa') {
                $tags[] = 'biotipo_facial_face_longa';
            }
        }

        if (isset($ar['Extra-bucal.Perfil facial'])) {
            if ($ar['Extra-bucal.Perfil facial'] === 'convexo') {
                $tags[] = 'perfil_facial_convexo';
            } elseif ($ar['Extra-bucal.Perfil facial'] === 'reto') {
                $tags[] = 'perfil_facial_reto';
            } elseif ($ar['Extra-bucal.Perfil facial'] === 'côncavo') {
                $tags[] = 'perfil_facial_concavo';
            }
        }

        if (isset($ar['Extra-bucal.Selamento labial'])) {
            if ($ar['Extra-bucal.Selamento labial'] === 'selamento forçado') {
                $tags[] = 'selamento_labial_forçado';
            } elseif ($ar['Extra-bucal.Selamento labial'] === 'sem selamento') {
                $tags[] = 'selamento_labial_sem';
            }
        }

        if (isset($ar['Extra-bucal.Exposição dos dentes no sorriso'])) {
            if ($ar['Extra-bucal.Exposição dos dentes no sorriso'] === 'pouca exposição') {
                $tags[] = 'exposicao_dentes_pouca_exposicao';
            } elseif ($ar['Extra-bucal.Exposição dos dentes no sorriso'] === 'muita exposição') {
                $tags[] = 'exposicao_dentes_muita_exposicao';
            }
        }

        if (isset($ar['Extra-bucal.Exposição gengival ao sorrir'])) {
            if ($ar['Extra-bucal.Exposição gengival ao sorrir'] === 'muita exposição') {
                $tags[] = 'exposicao_gengival_muita';
            }
        }

        if (isset($ar['Extra-bucal.ATM'])) {
            if ($ar['Extra-bucal.ATM'] === 'dor') {
                $tags[] = 'atm_dor';
            } elseif ($ar['Extra-bucal.ATM'] === 'estalido') {
                $tags[] = 'atm_estalido';
            } elseif ($ar['Extra-bucal.ATM'] === 'abertura limitada') {
                $tags[] = 'atm_abertura_limitada';
            } elseif ($ar['Extra-bucal.ATM'] === 'desvio na abertura/fechamento') {
                $tags[] = 'atm_desvio_abertura_fechamento';
            }
        }

        if (isset($ar['Extra-bucal.Respiração'])) {
            if ($ar['Extra-bucal.Respiração'] === 'bucal') {
                $tags[] = 'respiracao_bucal';
            } elseif ($ar['Extra-bucal.Respiração'] === 'mista') {
                $tags[] = 'respiracao_mista';
            } elseif ($ar['Extra-bucal.Respiração'] === 'problema alérgico') {
                $tags[] = 'respiracao_problema_alergico';
            }
        }

        if (isset($ar['Extra-bucal.Deglutição'])) {
            if ($ar['Extra-bucal.Deglutição'] === 'atípica') {
                $tags[] = 'degluticao_atipica';
            } elseif ($ar['Extra-bucal.Deglutição'] === 'adaptada') {
                $tags[] = 'degluticao_adaptada';
            }
        }

        if (isset($ar['Extra-bucal.Hábitos'])) {
            if ($ar['Extra-bucal.Hábitos'] === 'chupeta') {
                $tags[] = 'habitos_chupeta';
            } elseif ($ar['Extra-bucal.Hábitos'] === 'dedo') {
                $tags[] = 'habitos_dedo';
            } elseif ($ar['Extra-bucal.Hábitos'] === 'sucção de lábio') {
                $tags[] = 'habitos_succao_labrio';
            } elseif ($ar['Extra-bucal.Hábitos'] === 'onicofagia') {
                $tags[] = 'habitos_onicofagia';
            } elseif ($ar['Extra-bucal.Hábitos'] === 'bruxismo') {
                $tags[] = 'habitos_bruxismo';
            }
        }

        if (isset($ar['Extra-bucal.Posição da língua'])) {
            if ($ar['Extra-bucal.Posição da língua'] === 'normal') {
                $tags[] = 'posicao_lingua_normal';
            } elseif ($ar['Extra-bucal.Posição da língua'] === 'interposição') {
                $tags[] = 'posicao_lingua_interposicao';
            } elseif ($ar['Extra-bucal.Posição da língua'] === 'anteriorizada') {
                $tags[] = 'posicao_lingua_anteriorizada';
            } elseif ($ar['Extra-bucal.Posição da língua'] === 'posteriorizada') {
                $tags[] = 'posicao_lingua_posteriorizada';
            }
        }

        // Intra-bucal rules
        if (isset($ar['Intra-bucal.Dentição'])) {
            if ($ar['Intra-bucal.Dentição'] === 'permanente') {
                $tags[] = 'denticao_permanente';
            } elseif ($ar['Intra-bucal.Dentição'] === 'mista') {
                $tags[] = 'denticao_mista';
            } elseif ($ar['Intra-bucal.Dentição'] === 'decídua') {
                $tags[] = 'denticao_decidua';
            }
        }

        if (isset($ar['Intra-bucal.Diferença entre RC e MIH'])) {
            if (strpos($ar['Intra-bucal.Diferença entre RC e MIH'], 'laranja') !== false) {
                $tags[] = 'diferenca_rc_mih_laranja';
            }
        }

        if (isset($ar['Intra-bucal.Relação molar'])) {
            if ($ar['Intra-bucal.Relação molar'] === 'classe I') {
                $tags[] = 'relacao_molar_classe_i';
            } elseif ($ar['Intra-bucal.Relação molar'] === 'classe II bilateral (1 a 2mm)') {
                $tags[] = 'relacao_molar_classe_ii_pequena';
            } elseif ($ar['Intra-bucal.Relação molar'] === 'classe II bilateral (3 a 4mm)') {
                $tags[] = 'relacao_molar_classe_ii_media';
            } elseif ($ar['Intra-bucal.Relação molar'] === 'classe II bilateral (5mm+)') {
                $tags[] = 'relacao_molar_classe_ii_grande';
            } elseif ($ar['Intra-bucal.Relação molar'] === 'classe II unilateral - desvio superior (1 a 2mm)') {
                $tags[] = 'relacao_molar_classe_ii_unilateral_desvio_superior_pequena';
            } elseif ($ar['Intra-bucal.Relação molar'] === 'classe II unilateral - desvio superior (3 a 4mm)') {
                $tags[] = 'relacao_molar_classe_ii_unilateral_desvio_superior_media';
            } elseif ($ar['Intra-bucal.Relação molar'] === 'classe II unilateral - desvio superior (5mm+)') {
                $tags[] = 'relacao_molar_classe_ii_unilateral_desvio_superior_grande';
            } elseif ($ar['Intra-bucal.Relação molar'] === 'classe II unilateral - desvio inferior (1 a 2mm)') {
                $tags[] = 'relacao_molar_classe_ii_unilateral_desvio_inferior_pequena';
            } elseif ($ar['Intra-bucal.Relação molar'] === 'classe II unilateral - desvio inferior (3 a 4mm)') {
                $tags[] = 'relacao_molar_classe_ii_unilateral_desvio_inferior_media';
            } elseif ($ar['Intra-bucal.Relação molar'] === 'classe II unilateral - desvio inferior (5mm+)') {
                $tags[] = 'relacao_molar_classe_ii_unilateral_desvio_inferior_grande';
            } elseif ($ar['Intra-bucal.Relação molar'] === 'classe III (1 a 2mm)') {
                $tags[] = 'relacao_molar_classe_iii_pequena';
            } elseif ($ar['Intra-bucal.Relação molar'] === 'classe III (3 a 4mm)') {
                $tags[] = 'relacao_molar_classe_iii_media';
            } elseif ($ar['Intra-bucal.Relação molar'] === 'classe III (5mm+)') {
                $tags[] = 'relacao_molar_classe_iii_grande';
            }
        }

        if (isset($ar['Intra-bucal.Relação canina - lado DIREITO'])) {
            if ($ar['Intra-bucal.Relação canina - lado DIREITO'] === 'em chave') {
                $tags[] = 'relacao_canina_direito_em_chave';
            } elseif ($ar['Intra-bucal.Relação canina - lado DIREITO'] === 'relação Classe II') {
                $tags[] = 'relacao_canina_direito_classe_ii';
            } elseif ($ar['Intra-bucal.Relação canina - lado DIREITO'] === 'relação Classe III') {
                $tags[] = 'relacao_canina_direito_classe_iii';
            } elseif ($ar['Intra-bucal.Relação canina - lado DIREITO'] === 'ectópico') {
                $tags[] = 'relacao_canina_direito_ectopico';
            }
        }

        if (isset($ar['Intra-bucal.Relação canina - lado ESQUERDO'])) {
            if ($ar['Intra-bucal.Relação canina - lado ESQUERDO'] === 'em chave') {
                $tags[] = 'relacao_canina_esquerdo_em_chave';
            } elseif ($ar['Intra-bucal.Relação canina - lado ESQUERDO'] === 'relação Classe II') {
                $tags[] = 'relacao_canina_esquerdo_classe_ii';
            } elseif ($ar['Intra-bucal.Relação canina - lado ESQUERDO'] === 'relação Classe III') {
                $tags[] = 'relacao_canina_esquerdo_classe_iii';
            } elseif ($ar['Intra-bucal.Relação canina - lado ESQUERDO'] === 'ectópico') {
                $tags[] = 'relacao_canina_esquerdo_ectopico';
            }
        }

        if (isset($ar['Intra-bucal.Análise transversal'])) {
            if ($ar['Intra-bucal.Análise transversal'] === 'normal') {
                $tags[] = 'analise_transversal_normal';
            } elseif ($ar['Intra-bucal.Análise transversal'] === 'deficiência dentária: cruzada unilateral verdadeira') {
                $tags[] = 'analise_transversal_deficiencia_cruzada_unilateral_verdadeira';
            } elseif ($ar['Intra-bucal.Análise transversal'] === 'deficiência dentária: cruzada unilateral funcional') {
                $tags[] = 'analise_transversal_deficiencia_cruzada_unilateral_funcional';
            } elseif ($ar['Intra-bucal.Análise transversal'] === 'deficiência esquelética') {
                $tags[] = 'analise_transversal_deficiencia_esqueletica';
            } elseif ($ar['Intra-bucal.Análise transversal'] === 'deficiência dentária: superior e inferior') {
                $tags[] = 'analise_transversal_deficiencia_dentaria_superior_inferior';
            }
        }

        if (isset($ar['Intra-bucal.Análise vertical'])) {
            if ($ar['Intra-bucal.Análise vertical'] === 'Normal') {
                $tags[] = 'analise_vertical_normal';
            } elseif ($ar['Intra-bucal.Análise vertical'] === 'mordida aberta - dentária') {
                $tags[] = 'analise_vertical_mordida_aberta_dentaria';
            } elseif ($ar['Intra-bucal.Análise vertical'] === 'mordida aberta - esquelética') {
                $tags[] = 'analise_vertical_mordida_aberta_esqueletica';
            } elseif ($ar['Intra-bucal.Análise vertical'] === 'mordida profunda - dentária') {
                $tags[] = 'analise_vertical_mordida_profunda_dentaria';
            } elseif ($ar['Intra-bucal.Análise vertical'] === 'mordida profunda - esquelética') {
                $tags[] = 'analise_vertical_mordida_profunda_esqueletica';
            }
        }

        if (isset($ar['Intra-bucal.Curva de Spee'])) {
            if ($ar['Intra-bucal.Curva de Spee'] === 'pouco aumentada') {
                $tags[] = 'curva_spee_pouco_aumentada';
            } elseif ($ar['Intra-bucal.Curva de Spee'] === 'muito aumentada') {
                $tags[] = 'curva_de_spee_muito_acentuada';
            } elseif ($ar['Intra-bucal.Curva de Spee'] === 'invertida') {
                $tags[] = 'curva_spee_invertida';
            }
        }

        if (isset($ar['Intra-bucal.Linha média'])) {
            if ($ar['Intra-bucal.Linha média'] === 'desvio superior para a direita') {
                $tags[] = 'linha_media_superior_desviada';
            } elseif ($ar['Intra-bucal.Linha média'] === 'desvio superior para a esquerda') {
                $tags[] = 'linha_media_superior_desviada';
            } elseif ($ar['Intra-bucal.Linha média'] === 'desvio inferior para a direita') {
                $tags[] = 'linha_media_inferior_desviada';
            } elseif ($ar['Intra-bucal.Linha média'] === 'desvio inferior para a esquerda') {
                $tags[] = 'linha_media_inferior_desviada';
            } elseif ($ar['Intra-bucal.Linha média'] === 'normal') {
                $tags[] = 'sem_desvio_da_linha_media';
            }
        }

        if (isset($ar['Intra-bucal.Formato do arco superior'])) {
            if ($ar['Intra-bucal.Formato do arco superior'] === 'normal') {
                $tags[] = 'formato_arco_superior_normal';
            } elseif ($ar['Intra-bucal.Formato do arco superior'] === 'atrésico') {
                $tags[] = 'formato_arco_superior_atresico';
            } elseif ($ar['Intra-bucal.Formato do arco superior'] === 'hiper-expandido') {
                $tags[] = 'formato_arco_superior_hiper_expandido';
            } elseif ($ar['Intra-bucal.Formato do arco superior'] === 'triangular') {
                $tags[] = 'formato_arco_superior_triangular';
            } elseif ($ar['Intra-bucal.Formato do arco superior'] === 'quadrado') {
                $tags[] = 'formato_arco_superior_quadrado';
            }
        }

        if (isset($ar['Intra-bucal.Formato do arco inferior'])) {
            if ($ar['Intra-bucal.Formato do arco inferior'] === 'normal') {
                $tags[] = 'formato_arco_inferior_normal';
            } elseif ($ar['Intra-bucal.Formato do arco inferior'] === 'atrésico') {
                $tags[] = 'formato_arco_inferior_atresico';
            } elseif ($ar['Intra-bucal.Formato do arco inferior'] === 'hiper-expandido') {
                $tags[] = 'formato_arco_inferior_hiper_expandido';
            } elseif ($ar['Intra-bucal.Formato do arco inferior'] === 'triangular') {
                $tags[] = 'formato_arco_inferior_triangular';
            } elseif ($ar['Intra-bucal.Formato do arco inferior'] === 'quadrado') {
                $tags[] = 'formato_arco_inferior_quadrado';
            }
        }

        if (isset($ar['Intra-bucal.Apinhamentos superiores'])) {
            if ($ar['Intra-bucal.Apinhamentos superiores'] === 'leve') {
                $tags[] = 'apinhamentos_superiores_leve';
            } elseif ($ar['Intra-bucal.Apinhamentos superiores'] === 'moderado') {
                $tags[] = 'apinhamentos_superiores_moderado';
            } elseif ($ar['Intra-bucal.Apinhamentos superiores'] === 'severo') {
                $tags[] = 'apinhamentos_superiores_severo';
            }
        }

        if (isset($ar['Intra-bucal.Apinhamentos inferiores'])) {
            if ($ar['Intra-bucal.Apinhamentos inferiores'] === 'leve') {
                $tags[] = 'apinhamentos_inferiores_leve';
            } elseif ($ar['Intra-bucal.Apinhamentos inferiores'] === 'moderado') {
                $tags[] = 'apinhamentos_inferiores_moderado';
            } elseif ($ar['Intra-bucal.Apinhamentos inferiores'] === 'severo') {
                $tags[] = 'apinhamentos_inferiores_severo';
            }
        }

        if (isset($ar['Intra-bucal.Diastemas superiores'])) {
            if ($ar['Intra-bucal.Diastemas superiores'] === 'leve') {
                $tags[] = 'diastemas_superiores_leve';
            } elseif ($ar['Intra-bucal.Diastemas superiores'] === 'moderado') {
                $tags[] = 'diastemas_superiores_moderado';
            } elseif ($ar['Intra-bucal.Diastemas superiores'] === 'severo') {
                $tags[] = 'diastemas_superiores_severo';
            }
        }

        if (isset($ar['Intra-bucal.Diastemas inferiores'])) {
            if ($ar['Intra-bucal.Diastemas inferiores'] === 'leve') {
                $tags[] = 'diastemas_inferiores_leve';
            } elseif ($ar['Intra-bucal.Diastemas inferiores'] === 'moderado') {
                $tags[] = 'diastemas_inferiores_moderado';
            } elseif ($ar['Intra-bucal.Diastemas inferiores'] === 'severo') {
                $tags[] = 'diastemas_inferiores_severo';
            }
        }

        if (isset($ar['Intra-bucal.Sobremordida'])) {
            if ($ar['Intra-bucal.Sobremordida'] === 'sobremordida leve') {
                $tags[] = 'sobremordida_sobremordida_leve';
            } elseif ($ar['Intra-bucal.Sobremordida'] === 'sobremordida moderada') {
                $tags[] = 'sobremordida_sobremordida_moderada';
            } elseif ($ar['Intra-bucal.Sobremordida'] === 'sobremordida severa') {
                $tags[] = 'sobremordida_sobremordida_severa';
            } elseif ($ar['Intra-bucal.Sobremordida'] === 'mordida aberta leve') {
                $tags[] = 'sobremordida_mordida_aberta_leve';
            } elseif ($ar['Intra-bucal.Sobremordida'] === 'mordida aberta moderada') {
                $tags[] = 'sobremordida_mordida_aberta_moderada';
            } elseif ($ar['Intra-bucal.Sobremordida'] === 'mordida aberta severa') {
                $tags[] = 'sobremordida_mordida_aberta_severa';
            }
        }

        // Radiográficas rules
        if (isset($ar['Radiográficas.Ausência de dentes'])) {
            if ($ar['Radiográficas.Ausência de dentes'] === 'ausentes') {
                $tags[] = 'ausencia_dentes_ausentes';
            } elseif ($ar['Radiográficas.Ausência de dentes'] === 'retidos') {
                $tags[] = 'ausencia_dentes_retidos';
            } elseif ($ar['Radiográficas.Ausência de dentes'] === 'supranumerários') {
                $tags[] = 'ausencia_dentes_supranumerarios';
            }
        }

        if (isset($ar['Radiográficas.Inclinação dos incisivos superiores'])) {
            if ($ar['Radiográficas.Inclinação dos incisivos superiores'] === 'vestibularizados') {
                $tags[] = 'inclinacao_incisivos_superiores_vestibularizados';
            } elseif ($ar['Radiográficas.Inclinação dos incisivos superiores'] === 'lingualizados') {
                $tags[] = 'inclinacao_incisivos_superiores_lingualizados';
            }
        }

        if (isset($ar['Radiográficas.Posição dos incisivos superiores'])) {
            if ($ar['Radiográficas.Posição dos incisivos superiores'] === 'protruídos') {
                $tags[] = 'posicao_incisivos_superiores_protruidos';
            }
        }

        if (isset($ar['Radiográficas.Inclinação dos incisivos inferiores'])) {
            if ($ar['Radiográficas.Inclinação dos incisivos inferiores'] === 'vestibularizados') {
                $tags[] = 'inclinacao_incisivos_inferiores_vestibularizados';
            } elseif ($ar['Radiográficas.Inclinação dos incisivos inferiores'] === 'lingualizados') {
                $tags[] = 'inclinacao_incisivos_inferiores_lingualizados';
            }
        }

        if (isset($ar['Radiográficas.Posição dos incisivos inferiores'])) {
            if ($ar['Radiográficas.Posição dos incisivos inferiores'] === 'protruídos') {
                $tags[] = 'posicao_incisivos_inferiores_protruidos';
            }
        }

        if (isset($ar['Radiográficas.POEF'])) {
            if (trim($ar['Radiográficas.POEF']) !== '') {
                $tags[] = 'poef_preenchido';
            }
        }

        return $tags;
    }

    private function getTratamentosSugeridos($tags)
    {
        $tratamentos_sugeridos = [];
        $fatores_considerados = [];

        if (in_array('classe_i', $tags)) {
            $fatores_considerados[] = 'classe_i';

            if (
                in_array('pequenos_apinhamentos', $tags)
                && in_array('curva_spee_plana', $tags)
            ) {
                $fatores_considerados[] = 'pequenos_apinhamentos';
                $fatores_considerados[] = 'curva_spee_plana';
                $tratamentos_sugeridos[] = 'tratamento_simples';
            }
            
            else if (
                in_array('apinhamentos_severos', $tags)
                || in_array('curva_de_spee_muito_acentuada', $tags)
                || in_array('biprotrusao', $tags)
            ) {
                if (in_array('apinhamentos_severos', $tags))
                    $fatores_considerados[] = 'apinhamentos_severos';
                if (in_array('curva_de_spee_muito_acentuada', $tags))
                    $fatores_considerados[] = 'curva_de_spee_muito_acentuada';
                if (in_array('biprotrusao', $tags))
                    $fatores_considerados[] = 'biprotrusao';


                if (in_array('sem_sobremordida', $tags)) {
                    $fatores_considerados[] = 'sem_sobremordida';
                    $tratamentos_sugeridos[] = 'distalizacao_e_intrusao';
                }
                
                else if (in_array('com_sobremordida', $tags)) {
                    $fatores_considerados[] = 'com_sobremordida';

                    if (in_array('biotipo_facial_dolico', $tags)) {
                        $fatores_considerados[] = 'biotipo_facial_dolico';
                        $tratamentos_sugeridos[] = 'distalizacao_e_intrusao';
                    }
                    
                    else if (
                        in_array('biotipo_facial_braquicefalico', $tags)
                        || in_array('biotipo_facial_mesocefalico', $tags)
                    ) {
                        if (in_array('biotipo_facial_braquicefalico', $tags))
                            $fatores_considerados[] = 'biotipo_facial_braquicefalico';
                        if (in_array('biotipo_facial_mesocefalico', $tags))
                            $fatores_considerados[] = 'biotipo_facial_mesocefalico';

                        $tratamentos_sugeridos[] = 'desenvolvimento_transversal';
                    }
                }
            }
        }
        
        else if (
            (
                in_array('classe_ii_pequena', $tags)
                || in_array('classe_ii_media', $tags)
                || in_array('classe_ii_grande', $tags)
            )
            && in_array('linha_media_superior_desviada', $tags)) {
            $fatores_considerados[] = 'linha_media_superior_desviada';

            if (in_array('classe_ii_pequena', $tags)) {
                $fatores_considerados[] = 'classe_ii_pequena';
                $tratamentos_sugeridos[] = 'elastico_classe_ii';
            }
            
            else if (in_array('classe_ii_media', $tags)) {
                $fatores_considerados[] = 'classe_ii_media';

                if (
                    in_array('biotipo_facial_braquicefalico', $tags)
                    || in_array('biotipo_facial_mesocefalico', $tags)
                ) {
                    if (in_array('biotipo_facial_braquicefalico', $tags))
                        $fatores_considerados[] = 'biotipo_facial_braquicefalico';
                    if (in_array('biotipo_facial_mesocefalico', $tags))
                        $fatores_considerados[] = 'biotipo_facial_mesocefalico';
                    $tratamentos_sugeridos[] = 'distalizacao_unilateral';
                }
                
                else if (in_array('biotipo_facial_dolicocefalico', $tags)) {
                    $fatores_considerados[] = 'biotipo_facial_dolicocefalico';
                    $tratamentos_sugeridos[] = 'distalizacao_intrusao_unilateral';
                }
            }
            
            else if (in_array('classe_ii_grande', $tags)) {
                $fatores_considerados[] = 'classe_ii_grande';

                if (
                    in_array('biotipo_facial_braquicefalico', $tags)
                    || in_array('biotipo_facial_mesocefalico', $tags)
                    || in_array('biotipo_facial_dolicocefalico', $tags)
                ) {
                    if (in_array('biotipo_facial_braquicefalico', $tags))
                        $fatores_considerados[] = 'biotipo_facial_braquicefalico';
                    if (in_array('biotipo_facial_mesocefalico', $tags))
                        $fatores_considerados[] = 'biotipo_facial_mesocefalico';
                    if (in_array('biotipo_facial_dolicocefalico', $tags))
                        $fatores_considerados[] = 'biotipo_facial_dolicocefalico';

                    $tratamentos_sugeridos[] = 'distalizacao_intrusao_unilateral_izc';
                }
            }
        }
        
        else if (
            (
                in_array('classe_ii_pequena', $tags)
                || in_array('classe_ii_media', $tags)
                || in_array('classe_ii_grande', $tags)
            )
            && in_array('linha_media_inferior_desviada', $tags)) {
            $fatores_considerados[] = 'linha_media_inferior_desviada';

            if (in_array('classe_ii_pequena', $tags)) {
                $fatores_considerados[] = 'classe_ii_pequena';
                $tratamentos_sugeridos[] = 'elastico_classe_ii';
            }
            
            else if (in_array('classe_ii_media', $tags)) {
                $fatores_considerados[] = 'classe_ii_media';
                $tratamentos_sugeridos[] = 'distalizacao_cursor_inferior';
            }
            
            else if (in_array('classe_ii_grande', $tags)) {
                $fatores_considerados[] = 'classe_ii_grande';

                if (
                    in_array('biotipo_facial_braquicefalico', $tags)
                    || in_array('biotipo_facial_mesocefalico', $tags)
                ) {
                    if (in_array('biotipo_facial_braquicefalico', $tags))
                        $fatores_considerados[] = 'biotipo_facial_braquicefalico';
                    if (in_array('biotipo_facial_mesocefalico', $tags))
                        $fatores_considerados[] = 'biotipo_facial_mesocefalico';

                    $tratamentos_sugeridos[] = 'protrator_mandibular_assimetrico';
                }
                
                else if (in_array('biotipo_facial_dolicocefalico', $tags)) {
                    $fatores_considerados[] = 'biotipo_facial_dolicocefalico';

                    if (in_array('retrusao_mandibular', $tags)) {
                        $fatores_considerados[] = 'retrusao_mandibular';
                        $tratamentos_sugeridos[] = 'cirurgia_ortognatica';
                    }
                    
                    else if (in_array('protrusão_maxilar', $tags)) {
                        $fatores_considerados[] = 'protrusão_maxilar';
                        $tratamentos_sugeridos[] = 'distalizacao_superior_inferior_classe_i';
                    }
                }
            }
        }
        
        else if (in_array('classe_ii_pequena', $tags)) {
            $fatores_considerados[] = 'classe_ii_pequena';
            $tratamentos_sugeridos[] = 'elastico_classe_ii';
        }
        
        else if (in_array('classe_ii_media', $tags)) {
            $fatores_considerados[] = 'classe_ii_media';

            if (in_array('protrusao_maxilar', $tags)) {
                $fatores_considerados[] = 'protrusão_maxilar';

                if (
                    in_array('biotipo_facial_braquicefalico', $tags)
                    || in_array('biotipo_facial_mesocefalico', $tags)
                ) {
                    if (in_array('biotipo_facial_braquicefalico', $tags))
                        $fatores_considerados[] = 'biotipo_facial_braquicefalico';
                    if (in_array('biotipo_facial_mesocefalico', $tags))
                        $fatores_considerados[] = 'biotipo_facial_mesocefalico';

                    $tratamentos_sugeridos[] = 'distalizacao_com_cursor';
                }

                if (in_array('biotipo_facial_dolicocefalico', $tags)) {
                    $fatores_considerados[] = 'biotipo_facial_dolicocefalico';
                    $tratamentos_sugeridos[] = 'distalizacao_e_intrusao_ancoragem';
                }
            }
            
            else if (in_array('retrusao_mandibular', $tags)) {
                $fatores_considerados[] = 'retrusao_mandibular';

                if (
                    in_array('biotipo_facial_braquicefalico', $tags)
                    || in_array('biotipo_facial_mesocefalico', $tags)
                ) {
                    if (in_array('biotipo_facial_braquicefalico', $tags))
                        $fatores_considerados[] = 'biotipo_facial_braquicefalico';
                    if (in_array('biotipo_facial_mesocefalico', $tags))
                        $fatores_considerados[] = 'biotipo_facial_mesocefalico';

                    $tratamentos_sugeridos[] = 'cursor_simples';
                }

                if (in_array('biotipo_facial_dolicocefalico', $tags)) {
                    $fatores_considerados[] = 'biotipo_facial_dolicocefalico';
                    $tratamentos_sugeridos[] = 'cirurgia_ortognatica';
                }
            }
        }
        
        else if (in_array('classe_ii_grande', $tags)) {
            $fatores_considerados[] = 'classe_ii_grande';

            if (
                in_array('protrusao_maxilar', $tags)
                && in_array('retrusao_mandibular', $tags)
            ) {
                $fatores_considerados[] = 'protrusao_maxilar';
                $fatores_considerados[] = 'retrusao_mandibular';

                if (
                    in_array('biotipo_facial_braquicefalico', $tags)
                    || in_array('biotipo_facial_mesocefalico', $tags)
                ) {
                    if (in_array('biotipo_facial_braquicefalico', $tags))
                        $fatores_considerados[] = 'biotipo_facial_braquicefalico';
                    if (in_array('biotipo_facial_mesocefalico', $tags))
                        $fatores_considerados[] = 'biotipo_facial_mesocefalico';

                    $tratamentos_sugeridos[] = 'protrator_mandibular';
                    $tratamentos_sugeridos[] = 'cirurgia_ortognatica_2';
                }

                if (in_array('biotipo_facial_dolicocefalico', $tags)) {
                    $fatores_considerados[] = 'biotipo_facial_dolicocefalico';
                    $tratamentos_sugeridos[] = 'cirurgia_ortognatica';
                }
            }
            
            else if (in_array('protrusao_maxilar', $tags)) {
                $fatores_considerados[] = 'protrusao_maxilar';

                if (
                    in_array('biotipo_facial_braquicefalico', $tags)
                    || in_array('biotipo_facial_mesocefalico', $tags)
                ) {
                    if (in_array('biotipo_facial_braquicefalico', $tags))
                        $fatores_considerados[] = 'biotipo_facial_braquicefalico';
                    if (in_array('biotipo_facial_mesocefalico', $tags))
                        $fatores_considerados[] = 'biotipo_facial_mesocefalico';

                    $tratamentos_sugeridos[] = 'distalizacao_com_cursor';
                }

                if (in_array('biotipo_facial_dolicocefalico', $tags)) {
                    $fatores_considerados[] = 'biotipo_facial_dolicocefalico';
                    $tratamentos_sugeridos[] = 'distalizacao_intrusao';
                }
            }
            
            else if (in_array('retrusao_mandibular', $tags)) {
                $fatores_considerados[] = 'retrusao_mandibular';

                if (
                    in_array('biotipo_facial_braquicefalico', $tags)
                    || in_array('biotipo_facial_mesocefalico', $tags)
                ) {
                    if (in_array('biotipo_facial_braquicefalico', $tags))
                        $fatores_considerados[] = 'biotipo_facial_braquicefalico';
                    if (in_array('biotipo_facial_mesocefalico', $tags))
                        $fatores_considerados[] = 'biotipo_facial_mesocefalico';

                    $tratamentos_sugeridos[] = 'protrator_mandibular';
                }

                if (in_array('biotipo_facial_dolicocefalico', $tags)) {
                    $fatores_considerados[] = 'biotipo_facial_dolicocefalico';
                    $tratamentos_sugeridos[] = 'cirurgia_ortognatica';
                }
            }
        }
        
        else if (
                (
                    in_array('classe_iii_pequena', $tags)
                    || in_array('classe_iii_media', $tags)
                    || in_array('classe_iii_grande', $tags)
                )
                && in_array('anterior_ao_pico_de_crescimento_puberal', $tags)
            ) {
            $fatores_considerados[] = 'anterior_ao_pico_de_crescimento_puberal';

            if (in_array('classe_iii_pequena', $tags))
                $fatores_considerados[] = 'classe_iii_pequena';
            if (in_array('classe_iii_media', $tags))
                $fatores_considerados[] = 'classe_iii_media';
            if (in_array('classe_iii_grande', $tags))
                $fatores_considerados[] = 'classe_iii_grande';

            $tratamentos_sugeridos[] = 'disjuncao_mascara_facial';
        }

        else if (in_array('classe_iii_pequena', $tags)) {
            $fatores_considerados[] = 'classe_iii_pequena';
            $tratamentos_sugeridos[] = 'elastico_classe_iii';
        }

        else if (in_array('classe_iii_media', $tags)) {
            $fatores_considerados[] = 'classe_iii_media';
            $tratamentos_sugeridos[] = 'elastico_classe_iii_desgastes_inferior';
            $tratamentos_sugeridos[] = 'retracao_inferior_buccal_shelf';
        }
        
        else if (in_array('classe_iii_grande', $tags)) {
            $fatores_considerados[] = 'classe_iii_grande';
            $tratamentos_sugeridos[] = 'retracao_inferior_buccal_shelf';
            $tratamentos_sugeridos[] = 'cirurgia_ortognatica_2';
        }

        return [
            'tratamentos_sugeridos' => $tratamentos_sugeridos,
            'fatores_considerados' => $fatores_considerados,
        ];
    }

    private function getFatoresClinicosTags($analiseRespostas)
    {
        $ar = $analiseRespostas;

        $fatoresTags = [];

        if ($ar['Intra-bucal.Relação molar'] == 'classe I')
            $fatoresTags[] = 'classe_i';

        // Tags APINHAMENTOS
        if (!strContains($ar['Intra-bucal.Apinhamentos'], 'severo') && !strContains($ar['Intra-bucal.Apinhamentos'], 'severo') && strContains($ar['Intra-bucal.Apinhamentos'], 'inferior'))
            $fatoresTags[] = 'pequenos_apinhamentos_arcada_inferior';

        if (strContains($ar['Intra-bucal.Apinhamentos'], 'severo') && strContains($ar['Intra-bucal.Apinhamentos'], 'inferior'))
            $fatoresTags[] = 'apinhamentos_severos_arcada_inferior';

        if (strContains($ar['Intra-bucal.Apinhamentos'], 'severo') && strContains($ar['Intra-bucal.Apinhamentos'], 'inferior'))
            $fatoresTags[] = 'apinhamentos_severos_arcada_inferior';

        if (!strContains($ar['Intra-bucal.Apinhamentos'], 'severo') && !strContains($ar['Intra-bucal.Apinhamentos'], 'moderado'))
            $fatoresTags[] = 'pequenos_apinhamentos';

        if (strContains($ar['Intra-bucal.Apinhamentos'], 'severo'))
            $fatoresTags[] = 'apinhamentos_severos';


        // Tags CURVA DE SPEE
        if (strContains($ar['Intra-bucal.Curva de Spee'], 'normal'))
            $fatoresTags[] = 'curva_spee_plana';

        if (strContains($ar['Intra-bucal.Curva de Spee'], 'muito aumentada'))
            $fatoresTags[] = 'curva_de_spee_muito_acentuada';


        if (strContains($ar['Radiográficas.Posição dos incisivos superiores'], 'protruídos') && strContains($ar['Radiográficas.Posição dos incisivos inferiores'], 'protruídos'))
            $fatoresTags[] = 'biprotrusao';


        // Tags sobremordida
        if ($ar['Intra-bucal.Sobremordida'] == 'apresenta sobremordida')
            $fatoresTags[] = 'com_sobremordida';

        if ($ar['Intra-bucal.Sobremordida'] == 'não apresenta sobremordida')
            $fatoresTags[] = 'sem_sobremordida';


        // Tags Classe II
        if (strContains($ar['Intra-bucal.Relação molar'], 'classe II ') && strContains($ar['Intra-bucal.Relação molar'], '1 a 2mm'))
            $fatoresTags[] = 'classe_ii_pequena';

        if (strContains($ar['Intra-bucal.Relação molar'], 'classe II ') && strContains($ar['Intra-bucal.Relação molar'], '3 a 4mm'))
            $fatoresTags[] = 'classe_ii_media';

        if (strContains($ar['Intra-bucal.Relação molar'], 'classe II ') && strContains($ar['Intra-bucal.Relação molar'], '5mm+'))
            $fatoresTags[] = 'classe_ii_grande';


        // Tags protrusão/retrusão
        if (strContains($ar['Radiográficas.Posição dos incisivos superiores'], 'protruído'))
            $fatoresTags[] = 'protrusao_maxilar';

        if (strContains($ar['Radiográficas.Posição dos incisivos inferiores'], 'retraído'))
            $fatoresTags[] = 'retrusao_mandibular';


        // Tags Linha Média:
        if (strContains($ar['Intra-bucal.Linha média'], 'normal'))
            $fatoresTags[] = 'sem_desvio_da_linha_media';

        if (strContains($ar['Intra-bucal.Linha média'], 'desvio superior'))
            $fatoresTags[] = 'linha_media_superior_desviada';

        if (strContains($ar['Intra-bucal.Linha média'], 'desvio inferior'))
            $fatoresTags[] = 'linha_media_inferior_desviada';


        // Tags Classe III
        if (strContains($ar['Intra-bucal.Relação molar'], 'classe III ') && strContains($ar['Intra-bucal.Relação molar'], '1 a 2mm'))
            $fatoresTags[] = 'classe_iii_pequena';

        if (strContains($ar['Intra-bucal.Relação molar'], 'classe III ') && strContains($ar['Intra-bucal.Relação molar'], '3 a 4mm'))
            $fatoresTags[] = 'classe_iii_media';

        if (strContains($ar['Intra-bucal.Relação molar'], 'classe III ') && strContains($ar['Intra-bucal.Relação molar'], '5mm+'))
            $fatoresTags[] = 'classe_iii_grande';


        // Tags Pico de crescimento puberal
        if (strContains($ar['Pico de crescimento puberal'], 'Anterior'))
            $fatoresTags[] = 'anterior_ao_pico_de_crescimento_puberal';

        if (strContains($ar['Pico de crescimento puberal'], 'Posterior'))
            $fatoresTags[] = 'posterior_ao_pico_de_crescimento_puberal';


        // Tags Pico de biotipo facial
        if ($ar['Extra-bucal.Biotipo facial'] == 'braquicefálico')
            $fatoresTags[] = 'biotipo_facial_braquicefalico';

        if ($ar['Extra-bucal.Biotipo facial'] == 'mesocefálico')
            $fatoresTags[] = 'biotipo_facial_mesocefalico';

        if ($ar['Extra-bucal.Biotipo facial'] == 'dolicocefálico')
            $fatoresTags[] = 'biotipo_facial_dolicocefalico';

        if ($ar['Extra-bucal.Biotipo facial'] == 'face curta')
            $fatoresTags[] = 'biotipo_facial_face_curta';

        if ($ar['Extra-bucal.Biotipo facial'] == 'face longa')
            $fatoresTags[] = 'biotipo_facial_face_longa';


        // Remove tags de apinhamentos leves, caso possuir de apinhamento severo
        $fatoresTags = in_array('apinhamentos_severos', $fatoresTags) ? array_diff($fatoresTags, ['pequenos_apinhamentos', 'pequenos_apinhamentos_arcada_inferior']) : $fatoresTags;


        return $fatoresTags;
    }

    private function getAnalisesRespostas($analises, $paciente_id)
    {
        $analisesRespostas = [
            'Intra-bucal.Relação molar' => '',
            'Intra-bucal.Apinhamentos' => '',
            'Intra-bucal.Curva de Spee' => '',
            'Radiográficas.Posição dos incisivos superiores' => '',
            'Radiográficas.Posição dos incisivos inferiores' => '',
            'Intra-bucal.Sobremordida' => '',
            'Intra-bucal.Linha média' => '',
            'Extra-bucal.Biotipo facial' => '',
            'Pico de crescimento puberal' => '',
        ];

        foreach ($analisesRespostas as $index => &$analiseResposta) {
            if (strpos($index, '.') === false)
                continue;

            $splitted = explode('.', $index);
            $categoria = $splitted[0];
            $analise = $splitted[1];

            $analiseResposta = $this->getAnaliseResposta(
                json_decode($analises, true),
                $categoria,
                $analise
            );
        }

        $paciente = Paciente::find($paciente_id);
        if (!$paciente)
            throw new Exception('Paciente não encontrado');

        // Calcule a idade do paciente
        $idade = date_diff(date_create($paciente->data_nascimento), date_create(date('Y-m-d')))->y;

        // Preencha o campo 'Pico de crescimento puberal' com base na idade
        if ($idade > 15)
            $analisesRespostas['Pico de crescimento puberal'] = 'Posterior ao pico';
        else
            $analisesRespostas['Pico de crescimento puberal'] = 'Anterior ao pico';

        return $analisesRespostas;
    }

    private function getAnaliseResposta($array, $categoria, $analise)
    {
        // Verifica se a categoria existe no array
        if (!isset($array[$categoria])) {
            return null;
        }

        // Busca a resposta na categoria
        foreach ($array[$categoria] as $item) {
            if ($item['analise'] == $analise) {
                return $item['respostas'];
            }
        }

        // Se não encontrar a resposta, retorna uma mensagem
        return null;
    }

    public function getAnalises($paciente_id)
    {
        $analises = Analise::where('paciente_id', $paciente_id)
            ->get()
            ->groupBy('categoria');

        $resultado = [];

        foreach ($analises as $categoria => $analisesCategoria) {
            $resultado[$categoria] = [];

            foreach ($analisesCategoria as $analise) {
                $resultado[$categoria][] = [
                    'tipo' => $analise->tipo,
                    'analise' => $analise->analise,
                    'alternativas' => json_decode($analise->alternativas, true),
                    'respostas' => $analise->respostas,
                    'detalhar' => $analise->detalhar == 1,
                    'detalhe' => $analise->detalhe,
                    'titulo_detalhe' => $analise->titulo_detalhe,
                    'nivel' => $analise->nivel,
                ];
            }
        }

        return responseSuccess([
            'data' => $resultado
        ]);
    }

    // Limpa o objeto da Análise, pra IA não ficar perdida com tanta informação... Exibe apenas as respostas, e esconde todo o conteúdo irrelevante pra IA
    private function getAnaliseForIa($analiseRaw)
    {
        $analiseLimpa = [];

        foreach ($analiseRaw as $categoria => $analises) {
            $analiseLimpa[$categoria] = [];

            foreach ($analises as $analise) {
                unset($analise['id']);
                unset($analise['titulo_detalhe']);
                unset($analise['tipo']);
                unset($analise['alternativas']);
                unset($analise['selectedResposta']);

                if ($analise['detalhar'])
                    $analise['observacao'] = $analise['detalhe'];

                unset($analise['detalhar']);
                unset($analise['detalhe']);

                if ($analise['respostas'] !== null && $analise['respostas'] !== '') {
                    $analiseLimpa[$categoria][] = $analise;
                }
            }
        }

        return $analiseLimpa;
    }

    private function getIaAnalysis($analise)
    {
        // $analiseForIa = $this->getAnaliseForIa($analise);

        $analiseForIa = json_encode($analise);

        $diagnosticoInput = 'Monte um diagnóstico em formato de lista sobre o seguinte caso ortodôntico: ' . $analiseForIa . '... Lembrando que o diagnóstico vai ficar exposto na ficha... Então monte apenas o texto resumido e não se dirija diretamente a mim na resposta... Não trate como uma conversa, mas como um texto que vai ficar salvo na ficha do prontuário do paciente... E também, NÃO COMECE com "aqui está o diagnóstico que você pediu" ou algo assim... E também NÃO COMECE com "diagnóstico:" ou quaisquer outras escritas que não sejam o próprio texto do diagnóstico... Evite redundâncias e erros grotescos de gramáticas ou de português, por favor! Também, MUITO IMPORTANTE, evite ser rude com a descrição do paciente, quanto a beleza e afins... Pode usar o formato de lista para listar os fatores do diagnóstico do paciente...';

        $diagnosticoResponse = iaPrompt($diagnosticoInput);

        return [
            'diagnostico' => $diagnosticoResponse,
        ];
    }
}
