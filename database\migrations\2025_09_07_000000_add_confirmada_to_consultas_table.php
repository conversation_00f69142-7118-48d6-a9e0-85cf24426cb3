<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('consultas', function (Blueprint $table) {
            // Adicionar campo boolean para controlar se a consulta está confirmada
            $table->boolean('confirmada')->default(false)->after('status');
        });

        // Migrar dados existentes: se status for 'confirmada', marcar confirmada como true
        DB::statement("UPDATE consultas SET confirmada = 1 WHERE status = 'confirmada'");
        
        // Atualizar status 'confirmada' para 'agendada' mantendo o flag confirmada
        DB::statement("UPDATE consultas SET status = 'agendada' WHERE status = 'confirmada'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Antes de remover a coluna, restaurar o status baseado no campo confirmada
        DB::statement("UPDATE consultas SET status = 'confirmada' WHERE confirmada = 1 AND status = 'agendada'");
        
        Schema::table('consultas', function (Blueprint $table) {
            $table->dropColumn('confirmada');
        });
    }
};
