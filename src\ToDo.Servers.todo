☐ Fazer backup diário do banco de dados (logs à parte?)
☐ Fazer backup diário do servidor de arquivos
Armazenamento de imagens:
    ☐ Fazer API para consultar imagens da Backblaze (acho que já tá pronta, inclusive)
    ☐ Verificar se a Cloudflare vai cachear essas imagens (verificar se consome tráfego na Backblaze)
-- ---------------------------------------------------------
☐ Replicação MySQL
☐ Fazer backup diário do código fonte
☐ Fazer backup diário do servidor de e-mail


V2:
    ☐ Log de erros da interface e do servidor (com alerta?)