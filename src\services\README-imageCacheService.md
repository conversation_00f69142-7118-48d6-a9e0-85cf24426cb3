# Serviço de Cache de Imagens

O `imageCacheService` é um serviço singleton que gerencia o cache de imagens da aplicação, especialmente útil para imagens que são carregadas frequentemente, como as bandeiras dos países para seleção de idioma.

## Características

- **Cache inteligente**: Armazena imagens como blob URLs para acesso instantâneo
- **Carregamento com delay**: Não concorre com o carregamento inicial da aplicação
- **Singleton**: Uma única instância compartilhada em toda a aplicação
- **Gerenciamento automático de memória**: Limpa URLs de blob automaticamente
- **Pré-carregamento**: Carrega imagens importantes automaticamente

## Como usar

### Importar o serviço

```javascript
import imageCacheService from '@/services/imageCacheService';
```

### Inicializar (geralmente no componente principal)

```javascript
// Inicializar com delay de 2 segundos (padrão)
await imageCacheService.initialize();

// Ou com delay customizado
await imageCacheService.initialize(3000); // 3 segundos
```

### Obter imagens de bandeiras

```javascript
// Obter URL da bandeira (retorna blob URL se em cache, ou null)
const flagUrl = imageCacheService.getFlagImage('pt'); // ou 'en', 'es'

// Usar no template com fallback
<img :src="flagUrl || '@/assets/img/flags/pt.png'" alt="Português" />
```

### Verificar se uma imagem está em cache

```javascript
if (imageCacheService.hasImage('flag-pt')) {
    console.log('Bandeira do Brasil está em cache');
}
```

### Obter estatísticas do cache

```javascript
const stats = imageCacheService.getStats();
console.log(`Cache tem ${stats.totalImages} imagens`);
console.log(`Inicializado: ${stats.isInitialized}`);
```

## Implementação no componente Configuracoes.vue

O componente `Configuracoes.vue` foi atualizado para usar este serviço:

1. **Inicialização**: O serviço é inicializado no hook `mounted()` com delay de 2 segundos
2. **Watcher**: Um watcher verifica periodicamente se as imagens estão prontas
3. **Fallback**: As imagens usam URLs de cache quando disponíveis, senão usam as URLs originais
4. **Reatividade**: As imagens são atualizadas automaticamente quando o cache está pronto

## Benefícios

- **Performance**: Imagens carregam instantaneamente após o primeiro acesso
- **UX melhorada**: Sem delays visuais ao navegar entre páginas
- **Eficiência**: Não concorre com o carregamento inicial da aplicação
- **Reutilizável**: Pode ser usado em qualquer componente que precise das mesmas imagens

## Extensibilidade

O serviço pode ser facilmente estendido para cachear outros tipos de imagens:

```javascript
// Adicionar novas imagens ao cache
await imageCacheService.loadImageToCache(
    'minha-imagem',
    () => import('@/assets/img/minha-imagem.png')
);

// Obter a imagem
const minhaImagem = imageCacheService.getImage('minha-imagem');
```
