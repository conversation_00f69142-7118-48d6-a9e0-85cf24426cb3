export default {
    agenda: 'Agenda',
    badge: 'Distint<PERSON>',
    open: '<PERSON><PERSON><PERSON>',
    close: '<PERSON><PERSON><PERSON>',
    dismiss: 'Dispensar',
    confirmEdit: {
        ok: 'OK',
        cancel: 'Cancelar',
    },
    dataIterator: {
        noResultsText: 'Nenhum dado encontrado',
        loadingText: 'Carregando itens...',
    },
    dataTable: {
        itemsPerPageText: 'Linhas por página:',
        ariaLabel: {
            sortDescending: 'Ordenado decrescente.',
            sortAscending: 'Ordenado crescente.',
            sortNone: 'Não ordenado.',
            activateNone: 'Ative para remover a ordenação.',
            activateDescending: 'Ative para ordenar decrescente.',
            activateAscending: 'Ative para ordenar crescente.',
        },
        sortBy: 'Ordenar por',
    },
    dataFooter: {
        itemsPerPageText: 'Itens por página:',
        itemsPerPageAll: 'Todos',
        nextPage: 'Próxima página',
        prevPage: 'Página anterior',
        firstPage: 'Primeira página',
        lastPage: 'Última página',
        pageText: '{0}-{1} de {2}',
    },
    dateRangeInput: {
        divider: 'to',
    },
    datePicker: {
        itemsSelected: '{0} selecionados',
        range: {
            title: 'Selecione as datas',
            header: 'Digite as datas',
        },
        title: 'Selecione a data',
        header: 'Digite a data',
        input: {
            placeholder: 'Insira a data',
        },
    },
    noDataText: 'Não há dados disponíveis',
    carousel: {
        prev: 'Visão anterior',
        next: 'Próxima visão',
        ariaLabel: {
            delimiter: 'Slide {0} de {1} do carrossel',
        },
    },
    calendar: {
        moreEvents: 'Mais {0}',
        today: 'Hoje',
        day: 'Dia',
        week: 'Semana',
        month: 'Mês',
        appointment: 'Consulta{add}',
        view: 'Ver consulta',
        report: 'Reagendar'
    },
    input: {
        clear: 'Limpar {0}',
        prependAction: '{0} prepended action',
        appendAction: '{0} appended action',
        otp: 'Por favor insira o caracter OTP {0}',
    },
    fileInput: {
        counter: '{0} arquivo(s)',
        counterSize: '{0} arquivo(s) ({1} no total)',
    },
    timePicker: {
        am: 'AM',
        pm: 'PM',
        title: 'Select Time',
    },
    pagination: {
        ariaLabel: {
            root: 'Navegação de paginação',
            next: 'Próxima página',
            previous: 'Página anterior',
            page: 'Ir à página {0}',
            currentPage: 'Página atual, página {0}',
            first: 'Primeira página',
            last: 'Última página',
        },
    },
    stepper: {
        next: 'Próximo',
        prev: 'Anterior',
    },
    rating: {
        ariaLabel: {
            item: 'Avaliação {0} de {1}',
        },
    },
    loading: 'Carregando...',
    infiniteScroll: {
        loadMore: 'Carregar mais',
        empty: 'Não há mais dados',
    },

    // Custom:
    login: {
        submitAction: 'Entrar',
        loggingIn: 'Entrando...'
    },

    // Main Navigation
    mainNav: {
        agenda: 'Agenda',
        patients: 'Pacientes',
        orthodontists: 'Ortodontistas',
        financial: 'Financeiro',
        mentoring: 'Mentorias',
        settings: 'Configurações'
    },

    // Appointment Modal
    appointment: {
        title: {
            new: 'Agendar consulta',
            edit: 'Editar consulta'
        },
        fields: {
            patient: 'Paciente',
            orthodontist: 'Ortodontista',
            date: 'Data',
            time: 'Horário',
            value: 'Valor',
            notes: 'Observações',
            notesPlaceholder: 'Informações adicionais sobre a consulta ou sobre o paciente',
            selectPatient: 'Selecione um paciente',
            selectOrthodontist: 'Selecione um ortodontista',
            createPatient: 'Criar novo paciente',
            new: 'Novo'
        },
        status: {
            canceled: 'Cancelada',
            scheduled: 'Agendada',
            confirmed: 'Confirmada'
        },
        categories: {
            firstAppointment: 'Primeira consulta',
            assembly: 'Montagem',
            followUp: 'Acompanhamento',
            activation: 'Ativação',
            emergency: 'Emergência',
            removal: 'Remoção',
            replanning: 'Replanejamento',
            postTreatment: 'Pós-tratamento'
        },
        buttons: {
            cancel: 'Cancelar',
            save: 'Salvar'
        },
        alerts: {
            errorLoadingDentists: 'Erro ao carregar a lista de dentistas.',
            errorLoadingPatients: 'Erro ao carregar a lista de pacientes.',
            errorLoadingAppointment: 'Erro ao carregar dados da consulta. Por favor, tente novamente.',
            saving: 'Salvando consulta...',
            successSaving: 'Consulta salva com sucesso!',
            errorSaving: 'Erro ao salvar a consulta. Por favor, tente novamente.',
            requiredFields: 'Por favor, preencha todos os campos obrigatórios.'
        }
    },

    // Configurações de perfil
    profile: {
        title: 'Configurações de perfil',
        language: 'Idioma / Language',
        fullName: 'Nome completo',
        clinic: 'Clínica',
        email: 'E-mail',
        username: 'Nome de usuário',
        newPassword: 'Nova senha',
        confirmPassword: 'Confirmar nova senha',
        passwordHint: 'Preencha apenas se desejar alterar sua senha',
        confirmPasswordHint: 'Confirme a nova senha',
        saveButton: 'Salvar alterações',
        savingButton: 'Salvando...',
        loadingText: 'Carregando...'
    },
    // Navbar
    navbar: {
        search: 'Pesquisar aqui',
        notifications: {
            newMessage: 'Nova mensagem',
            from: 'de',
            newAlbum: 'Novo álbum',
            by: 'por',
            paymentCompleted: 'Pagamento concluído com sucesso',
            timeIndicators: {
                minutesAgo: 'minutos atrás',
                day: 'dia',
                days: 'dias'
            }
        }
    },

    // Patients
    patients: {
        search: 'Pesquisar...',
        emptyState: {
            noPatients: 'Ainda não existem pacientes cadastrados.',
            noResults: 'A busca não encontrou nenhum paciente.'
        },
        table: {
            headers: {
                patient: 'PACIENTE',
                nextAppointment: 'PRÓXIMA CONSULTA',
                status: 'STATUS DO TRATAMENTO',
                dentist: 'ORTODONTISTA',
                registeredAt: 'CADASTRADO EM'
            },
            pagination: {
                patientsPerPage: 'Pacientes por página',
                of: 'de',
                noResults: 'Sem resultados'
            },
            status: {
                notStarted: 'NÃO INICIADO',
                completed: 'CONCLUÍDO',
                inProgress: 'EM ANDAMENTO'
            }
        },
        modals: {
            newPatient: {
                title: 'Novo paciente',
                addButton: 'Adicionar',
                fields: {
                    name: 'Nome',
                    clinic: 'Clínica',
                    dentist: 'Ortodontista',
                    phone: 'Celular',
                    language: 'Idioma',
                    notes: 'Observações',
                    select: 'Selecionar...',
                    newOption: 'Nova...',
                    newClinicPlaceholder: 'Nome da nova clínica...'
                }
            },
            addPatient: {
                title: 'Adicionar paciente',
                linkExisting: 'Vincular a paciente existente',
                createNew: 'Criar novo paciente',
                back: 'Voltar'
            }
        },
        sidenav: {
            title: 'PACIENTES',
            newPatient: 'Novo paciente'
        },
        alerts: {
            clinicRequired: 'É necessário selecionar uma clínica.',
            addingPatient: 'Adicionando paciente...',
            patientAdded: 'O paciente foi adicionado com sucesso!',
            errorAddingPatient: 'Ocorreu um erro ao tentar adicionar o paciente.'
        }
    },
    pendingChanges: {
        title: 'Alterações pendentes',
        patientHasChanges: 'Este paciente possui {count} {count, plural, one {alteração} other {alterações}} não {count, plural, one {salva} other {salvas}}.',
        changesFound: 'Alterações encontradas:',
        lastModified: 'Última modificação:',
        understood: 'Entendi',
        categoryChanges: '{count} {count, plural, one {alteração} other {alterações}}',
        saveChanges: 'Salvar alterações',
        changesSaved: 'Alterações salvas! Rascunho salvo automaticamente',
        draftSavedAt: 'Rascunho salvo às {time}'
    }
}