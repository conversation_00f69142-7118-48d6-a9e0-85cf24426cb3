<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMentoriaMensagensTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('mentoria_mensagens', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('mentoria_id');
            $table->unsignedBigInteger('remetente_id'); // Referencia dentistas.id
            $table->text('mensagem');
            $table->boolean('lida')->default(false);
            $table->timestamps();

            $table->foreign('mentoria_id')->references('id')->on('mentorias')->onDelete('cascade');
            $table->foreign('remetente_id')->references('id')->on('dentistas')->onDelete('cascade');

            $table->index(['mentoria_id', 'created_at']);
            $table->index(['remetente_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('mentoria_mensagens');
    }
}
