/**
 * Estilos para o sistema de toasts da aplicação Lumi
 * Toasts elegantes e consistentes com o design system
 */

/* Estilos base para toasts */
.lumi-toast {
  animation: toastSlideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: none;
  border-radius: 12px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  max-width: 400px;
}

.lumi-toast .toast-header {
  padding: 0.75rem 1rem;
  border-bottom: none;
  font-weight: 600;
  letter-spacing: 0.3px;
}

.lumi-toast .toast-body {
  padding: 0.75rem 1rem;
  line-height: 1.5;
  font-size: 0.9rem;
}

/* Animações de entrada e saída */
@keyframes toastSlideIn {
  from {
    transform: translateX(100%) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

.toast-slide-in {
  animation: toastSlideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.toast-slide-out {
  animation: toastSlideOut 0.3s ease-in-out forwards;
}

@keyframes toastSlideOut {
  from {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
  to {
    transform: translateX(100%) scale(0.9);
    opacity: 0;
  }
}

/* Variações por tipo */
.lumi-toast .toast-header.bg-success {
  background: linear-gradient(135deg, #28a745, #20c997) !important;
}

.lumi-toast .toast-header.bg-danger {
  background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
}

.lumi-toast .toast-header.bg-warning {
  background: linear-gradient(135deg, #ffc107, #f39c12) !important;
  color: #212529 !important;
}

.lumi-toast .toast-header.bg-info {
  background: linear-gradient(135deg, #17a2b8, #3498db) !important;
}

/* Efeito hover no botão de fechar */
.lumi-toast .btn-close {
  transition: all 0.2s ease;
  opacity: 0.8;
}

.lumi-toast .btn-close:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* Toast específico para auto-save */
.autosave-toast {
  animation: autoSaveSlideIn 0.3s ease-out;
  border-left: 4px solid #28a745;
  background: white !important;
}

.autosave-toast .toast-header {
  background: linear-gradient(135deg, #28a745, #20c997) !important;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

.autosave-toast .toast-body {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  background: white !important;
  color: #495057 !important;
}

@keyframes autoSaveSlideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsividade para mobile */
@media (max-width: 768px) {
  .lumi-toast {
    min-width: 280px;
    max-width: calc(100vw - 40px);
    margin: 0 20px;
  }
  
  /* Ajustar posicionamento para mobile */
  .lumi-toast[style*="top: 20px; right: 20px"] {
    right: 10px !important;
    top: 10px !important;
  }
  
  .lumi-toast[style*="bottom: 20px; right: 20px"] {
    right: 10px !important;
    bottom: 10px !important;
  }
  
  .lumi-toast[style*="top: 20px; left: 20px"] {
    left: 10px !important;
    top: 10px !important;
  }
  
  .lumi-toast[style*="bottom: 20px; left: 20px"] {
    left: 10px !important;
    bottom: 10px !important;
  }
}

/* Empilhamento de toasts */
.lumi-toast:nth-of-type(2) {
  margin-top: 10px;
}

.lumi-toast:nth-of-type(3) {
  margin-top: 20px;
}

.lumi-toast:nth-of-type(4) {
  margin-top: 30px;
}

.lumi-toast:nth-of-type(n+5) {
  margin-top: 40px;
}

/* Efeito de glassmorphism sutil */
.lumi-toast::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  z-index: -1;
}

/* Indicador de progresso para toasts com duração */
.lumi-toast.with-progress::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  animation: progressBar var(--duration, 4s) linear forwards;
}

@keyframes progressBar {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Estados especiais */
.lumi-toast.toast-urgent {
  animation: toastUrgent 0.5s ease-out, toastSlideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 2px solid #dc3545;
}

@keyframes toastUrgent {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Toast compacto para auto-save */
.lumi-toast.compact {
  min-width: 250px;
}

.lumi-toast.compact .toast-header {
  padding: 0.5rem 0.75rem;
  font-size: 0.85rem;
}

.lumi-toast.compact .toast-body {
  padding: 0.5rem 0.75rem;
  font-size: 0.85rem;
}

/* Tema escuro (se necessário) */
@media (prefers-color-scheme: dark) {
  .lumi-toast {
    background-color: #2d3748;
    color: #e2e8f0;
  }
  
  .lumi-toast .toast-body {
    color: #e2e8f0;
  }
}
