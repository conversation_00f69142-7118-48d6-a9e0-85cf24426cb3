<template>
  <div
    class="calendar-day-toggle select-none flex flex-row flex-nowrap items-center transition-all mx-auto"
  >
    <!---->
    <a
      v-for="(tab, index) in tabs"
      :key="index"
      href="#"
      class="calendar-toggle-btn text-71717A inline-flex flex-col font-medium text-sm flex-shrink-0 items-center justify-center hover:opacity-90 active:animate-pulse cursor-pointer"
      :class="{
        'active': viewModel === tab,
        'inactive': viewModel !== tab,
      }"
      @click.stop.prevent="changeView(tab as T_View)"
    >
      <!-- Texto do botão acima -->
      <span class="toggle-text">{{ $t(`calendar.${tab}`) }}</span>

      <!-- Número do dia/semana/mês no meio (destacado) -->
      <span class="toggle-indicator" :class="{ 'month-long': tab === 'month' && isLongMonth }">
        <template v-if="tab === 'day'">{{ currentDay }}</template>
        <template v-else-if="tab === 'week'">{{ currentWeek }}</template>
        <template v-else>{{ currentMonthFull }}</template>
      </span>

      <!-- Número de consultas abaixo -->
      <span class="toggle-count">
        {{ getEventCount(tab) }} {{ getEventCount(tab) === 1 ? 'consulta' : 'consultas' }}
      </span>
    </a>
    <!---->
  </div>
</template>

<style scoped>
.calendar-day-toggle {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: stretch;
  width: 100%;
  margin: 0;
  padding: 2px;
  gap: 1px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.calendar-toggle-btn {
  flex: 1;
  min-width: 110px;
  text-align: center;
  border: none;
  border-radius: 5px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  color: #64748b;
  font-size: 11px;
  font-weight: 500;
  line-height: 1.3;
  height: auto;
  min-height: 56px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: transparent;
  position: relative;
}

.toggle-indicator {
  font-size: 16px;
  font-weight: 600;
  margin: 2px 0;
  line-height: 1.2;
  letter-spacing: -0.2px;
  color: inherit;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ajuste específico para o mês */
.calendar-toggle-btn:nth-child(2) .toggle-indicator {
  font-size: 14px;
}

.toggle-indicator.month-long {
  font-size: 12px;
  letter-spacing: -0.1px;
}

.toggle-text {
  font-size: 10px;
  font-weight: 500;
  margin-bottom: 1px;
  line-height: 1.2;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  opacity: 0.7;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.toggle-count {
  font-size: 9px;
  font-weight: 400;
  margin-top: 1px;
  opacity: 0.6;
  line-height: 1.1;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.calendar-toggle-btn.active {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  color: #ffffff;
  box-shadow:
    0 4px 12px rgba(14, 165, 233, 0.25),
    0 2px 6px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(14, 165, 233, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(14, 165, 233, 0.3);
  transform: translateY(-1px);
  position: relative;
}

.calendar-toggle-btn.active::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, rgba(14, 165, 233, 0.1) 0%, rgba(2, 132, 199, 0.1) 100%);
  border-radius: 7px;
  z-index: -1;
  opacity: 0.6;
}

.calendar-toggle-btn.active .toggle-indicator {
  color: #ffffff;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.calendar-toggle-btn.active .toggle-indicator.month-long {
  font-size: 12px;
  font-weight: 700;
  color: #ffffff;
}

.calendar-toggle-btn.active .toggle-text {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  opacity: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.calendar-toggle-btn.active .toggle-count {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  opacity: 1;
}

.calendar-toggle-btn.inactive {
  background: transparent;
}

.calendar-toggle-btn.inactive:hover {
  background: rgba(255, 255, 255, 0.8);
  color: #475569;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-0.5px);
}

.calendar-toggle-btn.inactive:hover .toggle-indicator {
  color: #0ea5e9;
  font-weight: 650;
}

/* Responsividade para tablets */
@media (max-width: 768px) {
  .calendar-toggle-btn {
    min-width: 90px; /* Reduzir um pouco para tablets */
    padding: 10px 12px;
  }

  .toggle-text {
    font-size: 10px; /* Aumentar um pouco */
  }

  .toggle-count {
    font-size: 10px; /* Aumentar um pouco */
  }
}

/* Responsividade para mobile */
@media (max-width: 480px) {
  .calendar-day-toggle {
    padding: 2px;
    gap: 2px;
    width: 100%; /* Garantir que use toda a largura */
  }

  .calendar-toggle-btn {
    flex: 1; /* Distribuir igualmente */
    min-width: 0; /* Permitir que encolha se necessário */
    min-height: 50px; /* Aumentar um pouco a altura */
    padding: 6px 4px; /* Ajustar padding */
  }

  .toggle-indicator {
    font-size: 15px;
  }

  .toggle-indicator.month-long {
    font-size: 11px; /* Aumentar um pouco */
  }

  /* Ajuste específico para o mês em mobile */
  .calendar-toggle-btn:nth-child(2) .toggle-indicator {
    font-size: 13px; /* Ajustar para mobile */
  }

  .toggle-text {
    font-size: 9px; /* Aumentar de 8px para 9px */
  }

  .toggle-count {
    font-size: 9px; /* Aumentar de 8px para 9px */
  }
}

/* Para telas muito pequenas */
@media (max-width: 360px) {
  .calendar-day-toggle {
    gap: 1px; /* Reduzir gap ainda mais */
  }

  .calendar-toggle-btn {
    padding: 5px 2px; /* Reduzir padding lateral */
    min-height: 48px; /* Reduzir altura um pouco */
  }

  .toggle-text {
    font-size: 8px; /* Reduzir um pouco para caber */
  }

  .toggle-count {
    font-size: 8px; /* Reduzir um pouco para caber */
  }

  .toggle-indicator {
    font-size: 14px; /* Reduzir um pouco */
  }

  .toggle-indicator.month-long {
    font-size: 10px; /* Reduzir para meses longos */
  }

  .calendar-toggle-btn:nth-child(2) .toggle-indicator {
    font-size: 12px; /* Ajustar para o mês em telas muito pequenas */
  }
}
</style>

<script setup lang="ts">
import {
  defineComponent,
  onMounted,
  ref,
  computed,
  toRefs,
  toRef,
  watch,
  inject,
} from "vue";
import type { Ref } from "vue";
import { E_View, type T_View } from "../../stores/events";
import { useEventsStore } from "../../stores/events";
import type { Appointment } from "../../stores/events";
import {
  monthName,
  getWeekInterval,
  dateToIsoString,
  fixDateTime
} from "./common";

export interface Props {
  view?: T_View;
  date?: Date;
}

const props = withDefaults(defineProps<Props>(), {
  view: "week",
  date: () => new Date(),
});

const emit = defineEmits(["calendar:view-changed"]);
const store = useEventsStore();

const viewModel: Ref<T_View> = ref(props.view); //'week' is default

// Reordenar os botões: Dia, Mês, Semana
const tabs: Ref<Record<string, string>> = ref({
  day: E_View.DAY,
  month: E_View.MONTH,
  week: E_View.WEEK,
});

// Obter a data atual
const currentDate = ref(props.date || new Date());

// Calcular o dia atual
const currentDay = computed(() => {
  return currentDate.value.getDate();
});

// Atualizar a data quando props.date mudar
watch(() => props.date, (newDate) => {
  if (newDate) {
    currentDate.value = new Date(newDate);
  }
}, { immediate: true });

// Calcular a semana atual (número da semana do ano)
const currentWeek = computed(() => {
  const date = new Date(currentDate.value);
  date.setHours(0, 0, 0, 0);
  // Definir para quinta-feira da semana atual
  date.setDate(date.getDate() + 3 - (date.getDay() + 6) % 7);
  // 4 de janeiro é sempre na semana 1
  const week1 = new Date(date.getFullYear(), 0, 4);
  // Ajustar para quinta-feira da semana 1
  week1.setDate(week1.getDate() + 3 - (week1.getDay() + 6) % 7);
  // Verificar se a data está no ano anterior ou próximo
  if (date.getFullYear() < week1.getFullYear()) {
    return getWeekNumber(new Date(date.getFullYear(), 11, 31));
  } else if (date.getFullYear() > week1.getFullYear()) {
    return 1;
  }
  // Retornar número da semana
  return 1 + Math.round(((date.getTime() - week1.getTime()) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7);
});

// Função auxiliar para calcular o número da semana
function getWeekNumber(date: Date): number {
  const target = new Date(date.valueOf());
  const dayNr = (date.getDay() + 6) % 7;
  target.setDate(target.getDate() - dayNr + 3);
  const firstThursday = target.valueOf();
  target.setMonth(0, 1);
  if (target.getDay() !== 4) {
    target.setMonth(0, 1 + ((4 - target.getDay()) + 7) % 7);
  }
  return 1 + Math.ceil((firstThursday - target.valueOf()) / 604800000);
}

// Calcular o mês atual (nome completo em uppercase)
const currentMonthFull = computed(() => {
  const months = ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'];
  return months[currentDate.value.getMonth()].toUpperCase();
});

// Verificar se o nome do mês é grande (para ajustar o estilo)
const isLongMonth = computed(() => {
  const longMonths = ['Fevereiro', 'Setembro', 'Novembro', 'Dezembro'];
  return longMonths.includes(currentMonthFull.value);
});

// Obter eventos do store
const events = computed(() => store.getEvents);

// Calcular o número de eventos para cada visualização
const getEventCount = (view: string): number => {
  if (!events.value || events.value.length === 0) return 0;

  const today = new Date(currentDate.value);
  let count = 0;

  try {
    if (view === 'day') {
      // Contar eventos do dia
      count = events.value.filter(event => {
        const eventDate = new Date(event.date);
        return eventDate.getDate() === today.getDate() &&
               eventDate.getMonth() === today.getMonth() &&
               eventDate.getFullYear() === today.getFullYear();
      }).length;
    }
    else if (view === 'week') {
      // Contar eventos da semana
      const weekInterval = getWeekInterval(today, 1); // 1 = semana começa na segunda
      count = events.value.filter(event => {
        const eventDate = new Date(event.date);
        return eventDate >= weekInterval.start && eventDate <= weekInterval.end;
      }).length;
    }
    else {
      // Contar eventos do mês
      count = events.value.filter(event => {
        const eventDate = new Date(event.date);
        return eventDate.getMonth() === today.getMonth() &&
               eventDate.getFullYear() === today.getFullYear();
      }).length;
    }
  } catch (error) {
    console.error("Erro ao calcular eventos:", error);
    return 0;
  }

  return count;
};

const changeView = (state: T_View): void => {
  viewModel.value = state;
};

watch(viewModel, () => {
  emit("calendar:view-changed", viewModel.value);
});

watch(props, () => {
  if (props.view) {
    changeView(props.view);
  }
  if (props.date) {
    currentDate.value = props.date;
  }
});

onMounted(() => {
  emit("calendar:view-changed", viewModel.value);
});
</script>
