<template>
  <div class="paciente-busca-wrapper">
    <!-- Campo de exibição do paciente selecionado -->
    <div class="elegant-input-group" :class="{ 'is-invalid': hasError }">
      <!-- ID da Ficha como addon -->
      <span v-if="pacienteSelecionado && pacienteSelecionado.id_ficha" class="elegant-addon elegant-addon-left elegant-addon-ficha">
        #{{ String(pacienteSelecionado.id_ficha).padStart(3, '0') }}
      </span>

      <input
        type="text"
        class="elegant-input"
        :value="displayValue"
        :placeholder="placeholder"
        readonly>

      <!-- Bo<PERSON><PERSON> de busca (só aparece se não estiver disabled) -->
      <button
        v-if="!disabled"
        type="button"
        class="btn btn-outline-primary elegant-search-btn"
        @click="abrirModal">
        <i class="fas fa-search"></i>
      </button>

      <!-- <PERSON><PERSON><PERSON> de limpar (só aparece se tiver paciente e não estiver disabled) -->
      <button
        v-if="pacienteSelecionado && !disabled"
        type="button"
        class="btn btn-outline-danger elegant-clear-btn"
        @click="limparSelecao"
        title="Limpar seleção">
        <i class="fas fa-times"></i>
      </button>
    </div>
    
    <!-- Feedback de erro -->
    <div v-if="hasError && errorMessage" class="invalid-feedback d-block">
      {{ errorMessage }}
    </div>

    <!-- Informações adicionais do paciente (sem ID, que agora está no input-group-text) -->
    <div v-if="pacienteSelecionado && showDetails && (pacienteSelecionado.telefone || pacienteSelecionado.email)" class="mt-2">
      <div class="d-flex flex-wrap gap-1">
        <small v-if="pacienteSelecionado.telefone" class="badge bg-light text-dark">
          <i class="fas fa-phone me-1"></i>
          {{ formatPhone(pacienteSelecionado.telefone) }}
        </small>
        <small v-if="pacienteSelecionado.email" class="badge bg-light text-dark">
          <i class="fas fa-envelope me-1"></i>
          {{ pacienteSelecionado.email }}
        </small>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PacienteBuscaButton',
  props: {
    // Paciente atualmente selecionado
    pacienteSelecionado: {
      type: Object,
      default: null
    },
    // Placeholder do campo
    placeholder: {
      type: String,
      default: 'Clique na lupa para buscar um paciente...'
    },
    // Se o componente está desabilitado
    disabled: {
      type: Boolean,
      default: false
    },
    // Se há erro de validação
    hasError: {
      type: Boolean,
      default: false
    },
    // Mensagem de erro
    errorMessage: {
      type: String,
      default: ''
    },
    // Se deve mostrar detalhes do paciente
    showDetails: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    displayValue() {
      if (this.pacienteSelecionado) {
        return this.pacienteSelecionado.nome;
      }
      return '';
    }
  },
  methods: {
    abrirModal() {
      this.$emit('abrir-busca');
    },

    limparSelecao() {
      this.$emit('limpar-selecao');
    },

    formatPhone(phone) {
      if (!phone) return '';
      
      // Remove tudo que não é número
      const numbers = phone.replace(/\D/g, '');
      
      // Formata como (XX) XXXXX-XXXX ou (XX) XXXX-XXXX
      if (numbers.length === 11) {
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7)}`;
      } else if (numbers.length === 10) {
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 6)}-${numbers.slice(6)}`;
      }
      
      return phone;
    }
  }
};
</script>

<style scoped>
.paciente-busca-wrapper {
  width: 100%;
}

/* Input Groups Elegantes - Versão Robusta */
.elegant-input-group {
  position: relative;
  display: flex;
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  background: white;
  border: 1px solid #dee2e6;
  overflow: hidden;
  transition: all 0.3s ease;
}

.elegant-input-group:focus-within {
  border-color: #2C82C9;
  box-shadow: 0 0 0 0.15rem rgba(44, 130, 201, 0.15);
}

.elegant-input {
  border: none;
  background: #fafbfc;
  flex: 1;
  padding: 0.6rem 0.8rem;
  font-size: 0.9rem;
  outline: none;
  transition: all 0.3s ease;
}

.elegant-input:focus {
  outline: none;
  background: rgba(44, 130, 201, 0.02);
}

.elegant-addon {
  background: linear-gradient(135deg, #7bb3e0 0%, #6ba3d0 100%);
  color: white;
  font-weight: 600;
  font-size: 0.85rem;
  padding: 0.6rem 0.8rem;
  width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  flex-shrink: 0;
  border: none;
}

.elegant-addon-left {
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

/* Addon especial para ID da ficha do paciente */
.elegant-addon-ficha {
  background: linear-gradient(135deg, #5a9bd4 0%, #4a8bc2 100%) !important;
}

.elegant-search-btn,
.elegant-clear-btn {
  border: none;
  border-left: 1px solid #dee2e6;
  border-radius: 0;
  padding: 0.6rem 0.8rem;
  flex-shrink: 0;
}

.elegant-search-btn:hover,
.elegant-clear-btn:hover {
  border-left: 1px solid #2C82C9;
}

.badge {
  font-size: 0.75rem;
}

.elegant-input-group.is-invalid {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 0.15rem rgba(220, 53, 69, 0.15) !important;
}

.invalid-feedback {
  display: block;
  color: #dc3545;
  font-size: 0.8rem;
  margin-top: 0.3rem;
  font-weight: 500;
}
</style>
