# Funcionalidade de Envio de Imagens no Chat de Mentoria

## Visão Geral

Esta funcionalidade permite aos usuários enviar imagens do paciente diretamente no chat de mentoria, proporcionando uma experiência similar ao WhatsApp, mas integrada com a galeria de imagens do sistema.

## Componentes Criados

### 1. ImageGallerySelector.vue
- **Propósito**: Exibe a galeria de imagens do paciente organizadas por data
- **Funcionalidades**:
  - Seleção múltipla de imagens
  - Seleção/deseleção por data
  - Interface elegante com preview das imagens
  - Contador de imagens selecionadas

### 2. ImageObservationsStep.vue
- **Propósito**: Permite adicionar observações opcionais às imagens selecionadas
- **Funcionalidades**:
  - Campo de observação individual para cada imagem
  - Aplicação de observação global para todas as imagens
  - Remoção de imagens da seleção
  - Validação de caracteres (máximo 500)

## Fluxo de Uso

1. **Iniciar**: Usuário clica no botão de imagens (📷) na área de digitação
2. **Selecionar**: Sistema exibe a galeria de imagens organizadas por data
3. **Escolher**: Usuário seleciona uma ou múltiplas imagens
4. **Observar**: Sistema permite adicionar observações opcionais
5. **Enviar**: Imagens são enviadas como referências no chat

## Integração com MentoriaModal

### Estados da View
- `chat`: Exibição normal do chat
- `gallery`: Galeria de seleção de imagens
- `observations`: Etapa de observações

### Novos Métodos
- `openImageGallery()`: Abre a galeria de imagens
- `backToChat()`: Retorna ao chat
- `proceedToObservations()`: Avança para etapa de observações
- `sendImagesMessage()`: Envia mensagem com imagens

## Estrutura de Dados

### Mensagem com Imagens
```javascript
{
  mentoria_id: number,
  mensagem: string,
  imagens: [
    {
      id: number,
      url: string,
      description: string,
      observation: string,
      data: string
    }
  ]
}
```

## Serviços Estendidos

### mentoriasService.js
- Adicionada função `enviarMensagemComImagens()`
- Suporte a fallback para compatibilidade

## Estilos e Animações

### Animações Implementadas
- `slideInRight`: Entrada da galeria
- `slideInLeft`: Entrada da etapa de observações
- `slideInDown`: Contador de seleção
- `scaleIn`: Imagens referenciadas no chat
- `fadeIn`: Transições gerais

### Responsividade
- Layout adaptativo para mobile
- Grid responsivo para imagens
- Botões e controles otimizados para touch

## Características Técnicas

### Compatibilidade
- Vue 2 com Composition API
- Bootstrap 5
- Font Awesome icons
- v-viewer para galeria de imagens

### Performance
- Lazy loading de componentes
- Otimização de re-renders
- Gestão eficiente de estado

### Acessibilidade
- Labels apropriados
- Navegação por teclado
- Contraste adequado
- Feedback visual claro

## Futuras Melhorias

1. **Backend**: Implementar endpoint específico para mensagens com imagens
2. **Upload**: Permitir upload direto de novas imagens
3. **Edição**: Funcionalidade de edição de observações
4. **Filtros**: Filtros por tipo de imagem na galeria
5. **Busca**: Busca por descrição nas imagens

## Uso

```vue
<!-- No template do MentoriaModal -->
<ImageGallerySelector
  :paciente="mentoria.paciente"
  @cancel="backToChat"
  @proceed="proceedToObservations"
/>

<ImageObservationsStep
  :selectedImages="selectedImages"
  :sending="enviandoImagens"
  @back="backToGallery"
  @send="sendImagesMessage"
/>
```

## Dependências

- `@/helpers/utils` - getImageDescription()
- `@/services/mentoriasService` - enviarMensagemComImagens()
- `@/utils/cSwal` - Notificações
- `v-viewer` - Visualização de imagens

## Notas de Implementação

- A funcionalidade usa referências às imagens existentes, não faz upload
- Compatível com o sistema de imagens diagnósticas existente
- Integração suave com o chat existente
- Fallback para sistemas sem suporte a imagens no backend
