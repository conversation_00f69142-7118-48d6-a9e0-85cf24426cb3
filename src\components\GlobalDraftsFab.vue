<template>
  <div class="global-drafts-container">
    <!-- FABs empilhados -->
    <div 
      v-for="(draft, index) in drafts" 
      :key="draft.patientId"
      class="global-draft-fab"
      :class="{ 
        'show': true,
        'stacked': index > 0 
      }"
      :style="{ 
        bottom: `${20 + (index * 70)}px`,
        zIndex: 1000 - index
      }"
    >
      <div class="fab-content" @click="toggleTooltip(draft.patientId)">
        <div class="fab-icon">
          <i class="fas fa-edit"></i>
        </div>
        <div class="fab-text">
          <strong>{{ draft.patientName }}</strong>
          <small>{{ draft.changesCount }} {{ draft.changesCount === 1 ? 'alteração' : 'alterações' }}</small>
        </div>
        <div class="fab-badge">{{ draft.changesCount }}</div>
      </div>

      <!-- Tooltip com opções -->
      <div 
        class="fab-tooltip" 
        :class="{ 'show': draft.showTooltip }"
        @click.stop
      >
        <div class="tooltip-header">
          <strong>{{ draft.patientName }}</strong>
          <button class="btn-close" @click="closeTooltip(draft.patientId)">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="tooltip-content">
          <p class="mb-2">
            <i class="fas fa-clock me-1"></i>
            Última alteração: {{ formatDate(draft.lastModified) }}
          </p>
          <p class="mb-3">
            {{ draft.changesCount }} campo{{ draft.changesCount > 1 ? 's alterado' : ' alterado' }}{{ draft.changesCount > 1 ? 's' : '' }}
          </p>
        </div>

        <div class="tooltip-actions">
          <button
            class="btn btn-sm btn-success me-2"
            @click="reviewAndSave(draft)"
          >
            Ver alterações
          </button>
          <button
            class="btn btn-sm btn-primary"
            @click="openPatient(draft)"
          >
            Abrir paciente
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GlobalDraftsFab',
  props: {
    drafts: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    toggleTooltip(patientId) {
      this.$emit('toggle-tooltip', patientId);
    },
    
    closeTooltip(patientId) {
      this.$emit('close-tooltip', patientId);
    },
    
    reviewAndSave(draft) {
      this.$emit('review-and-save', draft);
    },
    
    openPatient(draft) {
      this.$emit('open-patient', draft);
    },
    
    formatDate(timestamp) {
      const date = new Date(timestamp);
      return date.toLocaleString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  }
};
</script>

<style scoped>
.global-drafts-container {
  position: fixed;
  left: 80px;
  bottom: 20px;
  z-index: 1000;
  pointer-events: none;
}

.global-draft-fab {
  position: absolute;
  left: 0;
  width: 280px;
  background: linear-gradient(135deg, #fd7e14 0%, #e8590c 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(253, 126, 20, 0.3);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: auto;
  opacity: 0;
  transform: translateX(-100%);
}

.global-draft-fab.show {
  opacity: 1;
  transform: translateX(0);
}

.global-draft-fab.stacked {
  transform: scale(0.95) translateX(10px);
  opacity: 0.9;
}

.global-draft-fab:hover {
  transform: translateX(-5px) scale(1.02);
  box-shadow: 0 6px 25px rgba(253, 126, 20, 0.4);
}

.global-draft-fab.stacked:hover {
  transform: scale(1) translateX(-5px);
  opacity: 1;
}

.fab-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  position: relative;
}

.fab-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 16px;
}

.fab-text {
  flex: 1;
  line-height: 1.3;
}

.fab-text strong {
  display: block;
  font-size: 14px;
  margin-bottom: 2px;
  font-weight: 600;
}

.fab-text small {
  font-size: 12px;
  opacity: 0.9;
}

.fab-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ffc107;
  color: #000;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.fab-tooltip {
  position: absolute;
  bottom: 100%;
  right: 0;
  width: 320px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  margin-bottom: 12px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  color: #333;
}

.fab-tooltip.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto;
}

.tooltip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px 12px;
  border-bottom: 1px solid #e9ecef;
}

.tooltip-header strong {
  font-size: 16px;
  color: #2c3e50;
}

.btn-close {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.btn-close:hover {
  background: #f8f9fa;
  color: #dc3545;
}

.tooltip-content {
  padding: 12px 20px;
}

.tooltip-content p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

.tooltip-actions {
  display: flex;
  gap: 8px;
  padding: 12px 20px 16px;
}

.tooltip-actions .btn {
  flex: 1;
  font-size: 13px;
  padding: 8px 12px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s;
}

.tooltip-actions .btn:hover {
  transform: translateY(-1px);
}

/* Responsivo */
@media (max-width: 768px) {
  .global-drafts-container {
    left: 10px;
    bottom: 10px;
  }
  
  .global-draft-fab {
    width: 260px;
  }
  
  .fab-tooltip {
    width: 280px;
    right: -10px;
  }
  
  .tooltip-actions {
    flex-direction: column;
  }
  
  .tooltip-actions .btn {
    width: 100%;
    margin-bottom: 8px;
  }
}

/* Animação de entrada */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.global-draft-fab.show {
  animation: slideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
