<?php

namespace Database\Seeders;

use App\Models\Notification;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class NotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Buscar o primeiro usuário para criar notificações de teste
        $user = User::first();

        if (!$user) {
            $this->command->info('Nenhum usuário encontrado. Execute o seeder de usuários primeiro.');
            return;
        }

        $notifications = [
            [
                'title' => 'Bem-vindo ao sistema!',
                'message' => 'Seja bem-vindo ao sistema de notificações. Aqui você receberá todas as atualizações importantes.',
                'type' => 'info',
                'user_id' => $user->id,
            ],
            [
                'title' => 'Nova consulta agendada',
                'message' => 'Uma nova consulta foi agendada para amanhã às 14:00.',
                'type' => 'success',
                'user_id' => $user->id,
                'action_url' => '/agenda',
            ],
            [
                'title' => 'Pagamento pendente',
                'message' => 'Há um pagamento pendente que precisa de sua atenção.',
                'type' => 'warning',
                'user_id' => $user->id,
                'action_url' => '/financeiro',
            ],
            [
                'title' => 'Sistema em manutenção',
                'message' => 'O sistema passará por manutenção programada no próximo domingo.',
                'type' => 'info',
                'user_id' => $user->id,
                'read' => true,
                'read_at' => now()->subHours(2),
            ],
        ];

        foreach ($notifications as $notification) {
            Notification::create($notification);
        }

        $this->command->info('Notificações de teste criadas com sucesso!');
    }
}
