<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Carbon\Carbon;

class Orcamento extends Model
{
    use HasFactory, LogsActivity;

    protected $table = 'orcamentos';

    // Constantes de status
    const STATUS_RASCUNHO = 'rascunho';
    const STATUS_ENVIADO = 'enviado';
    const STATUS_APROVADO = 'aprovado';
    const STATUS_REJEITADO = 'rejeitado';
    const STATUS_EXPIRADO = 'expirado';
    const STATUS_CONVERTIDO = 'convertido';

    protected $fillable = [
        'clinica_id',
        'paciente_id',
        'dentista_id',
        'numero',
        'titulo',
        'descricao',
        'data_orcamento',
        'data_validade',
        'valor_total',
        'desconto_percentual',
        'desconto_valor',
        'valor_final',
        'status',
        'observacoes',
        'observacoes_internas',
        'aprovado_em',
        'aprovado_por',
        'convertido_em',
        'criado_por',
    ];

    protected $casts = [
        'data_orcamento' => 'date',
        'data_validade' => 'date',
        'valor_total' => 'decimal:2',
        'desconto_percentual' => 'decimal:2',
        'desconto_valor' => 'decimal:2',
        'valor_final' => 'decimal:2',
        'aprovado_em' => 'datetime',
        'convertido_em' => 'datetime',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*']);
    }

    // ==================== RELACIONAMENTOS ====================

    public function clinica(): BelongsTo
    {
        return $this->belongsTo(Clinica::class, 'clinica_id');
    }

    public function paciente(): BelongsTo
    {
        return $this->belongsTo(Paciente::class, 'paciente_id');
    }

    public function dentista(): BelongsTo
    {
        return $this->belongsTo(Dentista::class, 'dentista_id');
    }

    public function criadoPor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'criado_por');
    }

    public function itens(): HasMany
    {
        return $this->hasMany(OrcamentoItem::class, 'orcamento_id')->orderBy('ordem');
    }

    // ==================== SCOPES ====================

    public function scopeDaClinica(Builder $query, int $clinicaId): Builder
    {
        return $query->where('clinica_id', $clinicaId);
    }

    public function scopeDoPaciente(Builder $query, int $pacienteId): Builder
    {
        return $query->where('paciente_id', $pacienteId);
    }

    public function scopePorStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    public function scopeRascunhos(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_RASCUNHO);
    }

    public function scopeEnviados(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_ENVIADO);
    }

    public function scopeAprovados(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_APROVADO);
    }

    public function scopeExpirados(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_EXPIRADO)
                    ->orWhere(function ($q) {
                        $q->whereIn('status', [self::STATUS_ENVIADO])
                          ->where('data_validade', '<', now()->toDateString());
                    });
    }

    public function scopeVigentes(Builder $query): Builder
    {
        return $query->whereIn('status', [self::STATUS_ENVIADO, self::STATUS_APROVADO])
                    ->where(function ($q) {
                        $q->whereNull('data_validade')
                          ->orWhere('data_validade', '>=', now()->toDateString());
                    });
    }

    // ==================== MÉTODOS PRINCIPAIS ====================

    public function calcularValores(): void
    {
        $valorTotal = $this->itens->sum('valor_total');
        $valorDesconto = $this->desconto_valor;

        // Calcular desconto por percentual se não houver valor fixo
        if ($valorDesconto == 0 && $this->desconto_percentual > 0) {
            $valorDesconto = ($valorTotal * $this->desconto_percentual) / 100;
        }

        $this->valor_total = $valorTotal;
        $this->valor_final = $valorTotal - $valorDesconto;
        $this->save();
    }

    public function enviar(): void
    {
        $this->update([
            'status' => self::STATUS_ENVIADO,
            'data_orcamento' => now()->toDateString(),
        ]);
    }

    public function aprovar(string $aprovadoPor = null): void
    {
        $this->update([
            'status' => self::STATUS_APROVADO,
            'aprovado_em' => now(),
            'aprovado_por' => $aprovadoPor,
        ]);
    }

    public function rejeitar(): void
    {
        $this->update(['status' => self::STATUS_REJEITADO]);
    }

    public function converterParaFatura(): FinanceiroReceber
    {
        $fatura = FinanceiroReceber::create([
            'clinica_id' => $this->clinica_id,
            'paciente_id' => $this->paciente_id,
            'dentista_id' => $this->dentista_id,
            'descricao' => $this->titulo,
            'observacoes' => $this->observacoes,
            'valor_nominal' => $this->valor_final,
            'valor_final' => $this->valor_final,
            'data_emissao' => now(),
            'data_vencimento' => now()->addDays(30),
            'status' => FinanceiroReceber::STATUS_PENDENTE,
            'lancado_por' => auth()->id(),
        ]);

        $this->update([
            'status' => self::STATUS_CONVERTIDO,
            'convertido_em' => now(),
        ]);

        return $fatura;
    }

    public function duplicar(): self
    {
        $novoOrcamento = $this->replicate();
        $novoOrcamento->numero = $this->gerarNumero();
        $novoOrcamento->status = self::STATUS_RASCUNHO;
        $novoOrcamento->data_orcamento = now()->toDateString();
        $novoOrcamento->data_validade = null;
        $novoOrcamento->aprovado_em = null;
        $novoOrcamento->aprovado_por = null;
        $novoOrcamento->convertido_em = null;
        $novoOrcamento->save();

        // Duplicar itens
        foreach ($this->itens as $item) {
            $novoItem = $item->replicate();
            $novoItem->orcamento_id = $novoOrcamento->id;
            $novoItem->save();
        }

        return $novoOrcamento;
    }

    // ==================== MÉTODOS DE VERIFICAÇÃO ====================

    public function isRascunho(): bool
    {
        return $this->status === self::STATUS_RASCUNHO;
    }

    public function isEnviado(): bool
    {
        return $this->status === self::STATUS_ENVIADO;
    }

    public function isAprovado(): bool
    {
        return $this->status === self::STATUS_APROVADO;
    }

    public function isRejeitado(): bool
    {
        return $this->status === self::STATUS_REJEITADO;
    }

    public function isExpirado(): bool
    {
        return $this->status === self::STATUS_EXPIRADO || 
               ($this->data_validade && $this->data_validade < now()->toDateString());
    }

    public function isConvertido(): bool
    {
        return $this->status === self::STATUS_CONVERTIDO;
    }

    public function podeEditar(): bool
    {
        return in_array($this->status, [self::STATUS_RASCUNHO]);
    }

    public function podeEnviar(): bool
    {
        return $this->status === self::STATUS_RASCUNHO && $this->itens->count() > 0;
    }

    public function podeAprovar(): bool
    {
        return $this->status === self::STATUS_ENVIADO && !$this->isExpirado();
    }

    public function podeConverter(): bool
    {
        return $this->status === self::STATUS_APROVADO;
    }

    // ==================== MÉTODOS UTILITÁRIOS ====================

    public static function getStatusList(): array
    {
        return [
            self::STATUS_RASCUNHO => 'Rascunho',
            self::STATUS_ENVIADO => 'Enviado',
            self::STATUS_APROVADO => 'Aprovado',
            self::STATUS_REJEITADO => 'Rejeitado',
            self::STATUS_EXPIRADO => 'Expirado',
            self::STATUS_CONVERTIDO => 'Convertido',
        ];
    }

    public function getStatusFormatado(): string
    {
        return self::getStatusList()[$this->status] ?? $this->status;
    }

    public function getDiasParaVencimento(): ?int
    {
        if (!$this->data_validade) {
            return null;
        }

        return now()->diffInDays($this->data_validade, false);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (!$model->numero) {
                $model->numero = $model->gerarNumero();
            }
        });
    }

    private function gerarNumero(): string
    {
        $ano = now()->year;
        $ultimo = static::where('clinica_id', $this->clinica_id)
                       ->where('numero', 'like', "ORC-{$ano}-%")
                       ->orderBy('numero', 'desc')
                       ->first();

        if ($ultimo && preg_match('/(\d+)$/', $ultimo->numero, $matches)) {
            $sequencial = intval($matches[1]) + 1;
        } else {
            $sequencial = 1;
        }

        return "ORC-{$ano}-" . str_pad($sequencial, 4, '0', STR_PAD_LEFT);
    }
}
