<?php

namespace App\Http\Controllers;

use App\Models\Mentoria;
use App\Models\MentoriaMensagem;
use App\Models\Paciente;
use App\Models\Dentista;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Utils;
use App\Models\MetaTerapeutica;

class MentoriaController extends Controller
{
    public function getAll(Request $request)
    {
        try {
            $user = auth()->payload();

            // Se for system_admin, retorna todas as mentorias
            // Se não for, retorna apenas as mentorias onde o usuário é o solicitante
            if ($user['system_admin']) {
                $mentorias = Mentoria::with(['paciente', 'paciente.clinica', 'solicitante'])
                    ->leftJoin('mentoria_mensagens', function($join) {
                        $join->on('mentorias.id', '=', 'mentoria_mensagens.mentoria_id')
                             ->whereRaw('mentoria_mensagens.created_at = (SELECT MAX(created_at) FROM mentoria_mensagens WHERE mentoria_id = mentorias.id)');
                    })
                    ->select('mentorias.*', 'mentoria_mensagens.created_at as ultima_mensagem')
                    ->get();
            } else {
                // Buscar mentorias onde o solicitante_id é o dentista do usuário logado
                $dentista_id = $user['dentista']['id'] ?? null;

                if (!$dentista_id) {
                    return responseError(['message' => 'Usuário não possui dentista associado']);
                }

                $mentorias = Mentoria::with(['paciente', 'paciente.clinica', 'solicitante'])
                    ->leftJoin('mentoria_mensagens', function($join) {
                        $join->on('mentorias.id', '=', 'mentoria_mensagens.mentoria_id')
                             ->whereRaw('mentoria_mensagens.created_at = (SELECT MAX(created_at) FROM mentoria_mensagens WHERE mentoria_id = mentorias.id)');
                    })
                    ->select('mentorias.*', 'mentoria_mensagens.created_at as ultima_mensagem')
                    ->where('mentorias.solicitante_id', $dentista_id)
                    ->get();
            }

            // Processar URLs das fotos dos pacientes
            foreach ($mentorias as $mentoria) {
                if ($mentoria->paciente && $mentoria->paciente->profile_picture_url) {
                    $mentoria->paciente->profile_picture_url = imageUrl($mentoria->paciente->profile_picture_url);
                }
            }

            $response = responseSuccess(['data' => $mentorias]);
        } catch (\Exception $e) {
            // Log::error($e->getMessage());
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function solicitarMentoria(Request $request)
    {
        $body = $request->all();

        try {

            DB::transaction(function () use ($body) {
                // Delete existing analyses for the paciente_id
                Mentoria::where('paciente_id', $body['paciente_id'])->delete();

                $user = auth()->payload();
                $dentista_id = $user['dentista']['id'] ?? null;

                $mentoria = new Mentoria();
                $mentoria->paciente_id = $body['paciente_id'];
                $mentoria->solicitante_id = $dentista_id;
                $mentoria->observacao = $body['observacao'];
                $mentoria->save();
            });

            $response = responseSuccess();
        } catch (\Exception $e) {
            // Log::error($e->getMessage());
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function getMentoria(Request $request, $id)
    {
        try {
            $user = auth()->payload();

            $mentoria = Mentoria::with([
                'paciente',
                'paciente.clinica',
                'solicitante',
                'mentor',
                'mensagens.remetente'
            ])->find($id);

            if (!$mentoria) {
                return responseError(['message' => 'Mentoria não encontrada']);
            }

            // Verificar permissões - system_admin ou solicitante da mentoria
            $dentista_id = $user['dentista']['id'] ?? null;
            if (!$user['system_admin'] && $mentoria->solicitante_id != $dentista_id) {
                return responseError(['message' => 'Acesso negado']);
            }

            $response = responseSuccess(['data' => $mentoria]);
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function enviarMensagem(Request $request)
    {
        $body = $request->all();

        try {
            $user = auth()->payload();
            $dentista_id = $user['dentista']['id'] ?? null;

            if (!$dentista_id) {
                return responseError(['message' => 'Usuário não possui dentista associado']);
            }

            // Verificar se a mentoria existe e se o usuário tem permissão
            $mentoria = Mentoria::find($body['mentoria_id']);
            if (!$mentoria) {
                return responseError(['message' => 'Mentoria não encontrada']);
            }

            // Verificar permissões: system_admin ou solicitante da mentoria
            if (!$user['system_admin'] && $mentoria->solicitante_id != $dentista_id) {
                return responseError(['message' => 'Acesso negado']);
            }

            $mensagem = null;
            DB::transaction(function () use ($body, $dentista_id, $user, $mentoria, &$mensagem) {
                // Criar a mensagem
                $mensagem = new MentoriaMensagem();
                $mensagem->mentoria_id = $body['mentoria_id'];
                $mensagem->remetente_id = $dentista_id; // Usa dentista_id
                $mensagem->mensagem = $body['mensagem'];

                // Adicionar imagens se fornecidas
                if (isset($body['imagens']) && is_array($body['imagens'])) {
                    $mensagem->imagens = $body['imagens'];
                }

                $mensagem->save();

                // Criar notificações
                $this->criarNotificacoesMensagem($mentoria, $mensagem, $user);
            });

            $response = responseSuccess();
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function enviarMensagemComImagens(Request $request)
    {
        $body = $request->all();

        try {
            $user = auth()->payload();
            $dentista_id = $user['dentista']['id'] ?? null;

            if (!$dentista_id) {
                return responseError(['message' => 'Usuário não possui dentista associado']);
            }

            // Verificar se a mentoria existe e se o usuário tem permissão
            $mentoria = Mentoria::find($body['mentoria_id']);
            if (!$mentoria) {
                return responseError(['message' => 'Mentoria não encontrada']);
            }

            // Verificar permissões: system_admin ou solicitante da mentoria
            if (!$user['system_admin'] && $mentoria->solicitante_id != $dentista_id) {
                return responseError(['message' => 'Acesso negado']);
            }

            // Validar se imagens foram fornecidas
            if (!isset($body['imagens']) || !is_array($body['imagens']) || empty($body['imagens'])) {
                return responseError(['message' => 'Nenhuma imagem fornecida']);
            }

            $mensagem = null;
            DB::transaction(function () use ($body, $dentista_id, $user, $mentoria, &$mensagem) {
                // Criar a mensagem com imagens
                $mensagem = new MentoriaMensagem();
                $mensagem->mentoria_id = $body['mentoria_id'];
                $mensagem->remetente_id = $dentista_id;
                $mensagem->mensagem = $body['mensagem'];
                $mensagem->imagens = $body['imagens'];
                $mensagem->save();

                // Criar notificações
                $this->criarNotificacoesMensagem($mentoria, $mensagem, $user);
            });

            $response = responseSuccess();
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function marcarMensagensComoLidas(Request $request, $mentoriaId)
    {
        try {
            $user = auth()->payload();
            $dentista_id = $user['dentista']['id'] ?? null;

            if (!$dentista_id) {
                return responseError(['message' => 'Usuário não possui dentista associado']);
            }

            // Verificar se a mentoria existe e se o usuário tem permissão
            $mentoria = Mentoria::find($mentoriaId);
            if (!$mentoria) {
                return responseError(['message' => 'Mentoria não encontrada']);
            }

            // Verificar permissões
            if (!$user['system_admin'] && $mentoria->solicitante_id != $dentista_id) {
                return responseError(['message' => 'Acesso negado']);
            }

            // Marcar mensagens como lidas (exceto as próprias)
            MentoriaMensagem::where('mentoria_id', $mentoriaId)
                ->where('remetente_id', '!=', $dentista_id) // Usa dentista_id
                ->where('lida', false)
                ->update(['lida' => true]);

            $response = responseSuccess();
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    private function criarNotificacoesMensagem($mentoria, $mensagem, $user)
    {
        $action_url = "/pacientes?mentoria_id=" . $mentoria->id;

        if ($user['system_admin']) {
            // Mentor enviou mensagem - notificar o solicitante
            // Buscar o user_id do dentista solicitante
            $solicitante = Dentista::with('user')->find($mentoria->solicitante_id);
            if ($solicitante && $solicitante->user) {
                $notification = new Notification();
                $notification->user_id = $solicitante->user->id;
                $notification->title = 'Nova mensagem na mentoria';
                $notification->message = 'Você recebeu uma nova mensagem do mentor na mentoria do paciente ' . $mentoria->paciente->nome;
                $notification->action_url = $action_url;
                $notification->save();
            }
        } else {
            // Solicitante enviou mensagem - notificar todos os system_admin
            $systemAdmins = Dentista::whereHas('user', function($query) {
                $query->where('system_admin', true);
            })->with('user')->get();

            foreach ($systemAdmins as $admin) {
                if ($admin->user) {
                    $notification = new Notification();
                    $notification->user_id = $admin->user->id;
                    $notification->title = 'Nova mensagem na mentoria';
                    $notification->message = 'Nova mensagem na mentoria do paciente ' . $mentoria->paciente->nome . ' de ' . $mentoria->solicitante->nome;
                    $notification->action_url = $action_url;
                    $notification->save();
                }
            }
        }
    }

    public function iniciarMentoria(Request $request, $id)
    {
        try {
            $user = auth()->payload();
            $dentista_id = $user['dentista']['id'] ?? null;

            if (!$user['system_admin']) {
                return responseError(['message' => 'Apenas administradores podem iniciar mentorias']);
            }

            if (!$dentista_id) {
                return responseError(['message' => 'Usuário não possui dentista associado']);
            }

            $mentoria = Mentoria::find($id);
            if (!$mentoria) {
                return responseError(['message' => 'Mentoria não encontrada']);
            }

            if ($mentoria->status !== 'AGUARDANDO') {
                return responseError(['message' => 'Esta mentoria não pode ser iniciada']);
            }

            DB::transaction(function () use ($mentoria, $dentista_id, $user) {
                // Atualizar status e mentor
                $mentoria->status = 'EM_ANDAMENTO';
                $mentoria->mentor_id = $dentista_id;
                $mentoria->iniciada_em = now();
                $mentoria->save();

                // Criar mensagem do sistema
                $nomeUsuario = explode(' ', $user['dentista']['nome'])[0];
                $this->criarMensagemSistema($mentoria, 'Mentoria iniciada por ' . $nomeUsuario);
            });

            $response = responseSuccess(['message' => 'Mentoria iniciada com sucesso']);
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function finalizarMentoria(Request $request, $id)
    {
        try {
            $user = auth()->payload();
            $dentista_id = $user['dentista']['id'] ?? null;

            if (!$dentista_id) {
                return responseError(['message' => 'Usuário não possui dentista associado']);
            }

            $mentoria = Mentoria::find($id);
            if (!$mentoria) {
                return responseError(['message' => 'Mentoria não encontrada']);
            }

            // Verificar permissões
            if (!$user['system_admin'] && $mentoria->solicitante_id != $dentista_id) {
                return responseError(['message' => 'Acesso negado']);
            }

            if ($mentoria->status === 'FINALIZADA' || $mentoria->status === 'CANCELADA') {
                return responseError(['message' => 'Esta mentoria já foi finalizada ou cancelada']);
            }

            DB::transaction(function () use ($mentoria, $user) {
                // Atualizar status
                $mentoria->status = 'FINALIZADA';
                $mentoria->finalizada_em = now();
                $mentoria->save();

                // Criar mensagem do sistema
                $nomeUsuario = explode(' ', $user['dentista']['nome'])[0];
                $this->criarMensagemSistema($mentoria, 'Mentoria finalizada por ' . $nomeUsuario);
            });

            $response = responseSuccess(['message' => 'Mentoria finalizada com sucesso']);
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function cancelarMentoria(Request $request, $id)
    {
        try {
            $user = auth()->payload();
            $dentista_id = $user['dentista']['id'] ?? null;

            if (!$dentista_id) {
                return responseError(['message' => 'Usuário não possui dentista associado']);
            }

            $mentoria = Mentoria::find($id);
            if (!$mentoria) {
                return responseError(['message' => 'Mentoria não encontrada']);
            }

            // Verificar permissões
            if (!$user['system_admin'] && $mentoria->solicitante_id != $dentista_id) {
                return responseError(['message' => 'Acesso negado']);
            }

            if ($mentoria->status === 'FINALIZADA' || $mentoria->status === 'CANCELADA') {
                return responseError(['message' => 'Esta mentoria já foi finalizada ou cancelada']);
            }

            DB::transaction(function () use ($mentoria, $user) {
                // Atualizar status
                $mentoria->status = 'CANCELADA';
                $mentoria->cancelada_em = now();
                $mentoria->save();

                // Criar mensagem do sistema
                $nomeUsuario = explode(' ', $user['dentista']['nome'])[0];
                $this->criarMensagemSistema($mentoria, 'Mentoria cancelada por ' . $nomeUsuario);
            });

            $response = responseSuccess(['message' => 'Mentoria cancelada com sucesso']);
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function reabrirMentoria(Request $request, $id)
    {
        try {
            $user = auth()->payload();

            // Verificar se é system_admin
            if (!$user['system_admin']) {
                return responseError(['message' => 'Apenas administradores podem reabrir mentorias']);
            }

            $mentoria = Mentoria::find($id);
            if (!$mentoria) {
                return responseError(['message' => 'Mentoria não encontrada']);
            }

            if (!in_array($mentoria->status, ['CANCELADA', 'FINALIZADA'])) {
                return responseError(['message' => 'Apenas mentorias canceladas ou finalizadas podem ser reabertas']);
            }

            DB::transaction(function () use ($mentoria, $user) {
                $mentoria->status = 'AGUARDANDO';
                $mentoria->mentor_id = null;
                $mentoria->iniciada_em = null;
                $mentoria->cancelada_em = null;
                $mentoria->save();

                // Criar mensagem do sistema
                $nomeUsuario = explode(' ', $user['dentista']['nome'])[0];
                $this->criarMensagemSistema($mentoria, 'Mentoria reaberta por ' . $nomeUsuario);
            });

            $response = responseSuccess(['message' => 'Mentoria reaberta com sucesso']);
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }



    private function criarMensagemSistema($mentoria, $mensagem)
    {
        $mensagemSistema = new MentoriaMensagem();
        $mensagemSistema->mentoria_id = $mentoria->id;
        $mensagemSistema->remetente_id = null; // Mensagem do sistema
        $mensagemSistema->mensagem = $mensagem;
        $mensagemSistema->tipo = 'SISTEMA';
        $mensagemSistema->lida = true; // Mensagens do sistema já são "lidas"
        $mensagemSistema->save();
    }
}
