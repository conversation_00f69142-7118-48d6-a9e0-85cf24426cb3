# Sistema de Rascunho Automático - Ficha do Paciente

## Visão Geral

O sistema de rascunho automático foi implementado para proporcionar uma experiência fluida e segura ao editar informações do paciente na aba Perfil. O sistema detecta automaticamente alterações nos campos e salva um rascunho temporário no localStorage após um período de inatividade, preservando os dados e estados de edição até que o usuário decida salvar definitivamente no banco de dados.

## Funcionalidades Principais

### 1. Rascunho Automático
- **Detecção de Mudanças**: O sistema monitora alterações em todos os campos do perfil do paciente
- **Delay Configurável**: Salva rascunho automaticamente após 3 segundos de inatividade (configurável)
- **Feedback Visual**: Toast discreto confirma o salvamento do rascunho
- **Preservação de Estados**: Mantém modos de edição ativos e alterações pendentes

### 2. Sistema de Toast Elegante
- **Toasts Discretos**: Notificações não intrusivas para confirmação de salvamento
- **Design Consistente**: Alinhado com o design system da aplicação
- **Animações Suaves**: Transições elegantes de entrada e saída
- **Responsivo**: Adaptado para dispositivos móveis

### 3. Floating Action Button (FAB)
- **Notificação de Mudanças Pendentes**: Aparece quando há alterações não salvas e o usuário navega
- **Design Atrativo**: Gradiente vermelho com animações sutis
- **Tooltip Informativo**: Detalhes sobre as alterações pendentes
- **Ações Rápidas**: Botões para voltar e salvar ou descartar alterações

### 4. Sistema de Rascunho Persistente
- **Salvamento Local**: Salva rascunho no localStorage incluindo estados de edição
- **Recuperação Inteligente**: Verifica automaticamente se há rascunho ao carregar a página
- **Modal de Confirmação**: Pergunta ao usuário se deseja restaurar rascunho da sessão anterior
- **Preservação de Estado**: Restaura modos de edição e aba ativa
- **Limpeza Controlada**: Remove rascunho apenas após salvar no banco ou descartar

### 5. Router Guards
- **Detecção de Navegação**: Intercepta tentativas de sair da página com alterações pendentes
- **Auto-Save Preventivo**: Tenta salvar automaticamente antes da navegação
- **Fallback Seguro**: Mostra FAB se o auto-save falhar

## Componentes Criados

### 1. `toastHelper.js`
Utilitário para criar toasts consistentes em toda a aplicação:

```javascript
import { showAutoSaveToast, showInfoToast } from "@/utils/toastHelper.js";

// Toast de auto-save
showAutoSaveToast('Dados salvos automaticamente');

// Toast informativo
showInfoToast('Mensagem informativa', { duration: 5000 });
```

### 2. `UnsavedChangesFab.vue`
Componente reutilizável para o FAB de alterações não salvas:

```vue
<UnsavedChangesFab
  :show="showUnsavedChangesFab"
  :patient-name="paciente?.nome || 'Paciente'"
  @return="returnToPatientProfile"
  @discard="discardChangesAndContinue"
/>
```

### 3. `lumi-toast.scss`
Estilos elegantes para o sistema de toasts com:
- Animações suaves
- Design responsivo
- Variações por tipo (success, error, info, warning)
- Efeitos de glassmorphism

## Como Funciona

### Fluxo de Rascunho
1. **Detecção**: Watcher monitora mudanças no objeto `paciente`
2. **Agendamento**: Timer de 3 segundos é iniciado/reiniciado a cada mudança
3. **Salvamento**: Após inatividade, rascunho é salvo no localStorage
4. **Feedback**: Toast discreto confirma o salvamento do rascunho
5. **Preservação**: Estados de edição e dados são mantidos para recuperação

### Fluxo de Navegação com Mudanças Pendentes
1. **Interceptação**: `beforeRouteLeave` detecta tentativa de navegação
2. **Verificação**: Checa se há mudanças pendentes
3. **Auto-Save**: Tenta salvar automaticamente
4. **Fallback**: Se falhar, mostra FAB para ação do usuário
5. **Resolução**: Usuário pode voltar para salvar ou descartar alterações

### Fluxo de Recuperação de Cache
1. **Verificação**: Ao carregar página, verifica localStorage
2. **Validação**: Confirma se cache não é muito antigo (24h)
3. **Confirmação**: Modal pergunta se usuário quer restaurar
4. **Restauração**: Dados são aplicados ao formulário
5. **Limpeza**: Cache é removido após salvamento

## Configurações

### Variáveis Configuráveis
```javascript
data() {
  return {
    autoSaveDelay: 3000, // Delay em ms para auto-save
    // ... outras configurações
  }
}
```

### Estilos Customizáveis
- Cores do FAB: Gradiente vermelho configurável
- Duração das animações: Transições CSS customizáveis
- Posicionamento: FAB e toasts responsivos

## Benefícios para o Usuário

### Médicos/Clínicos
- **Produtividade**: Não precisam se preocupar em salvar manualmente
- **Segurança**: Dados não são perdidos por fechamento acidental
- **Feedback Claro**: Sempre sabem quando dados foram salvos
- **Experiência Fluida**: Interface não interrompe o fluxo de trabalho

### Técnicos
- **Componentes Reutilizáveis**: Sistema pode ser aplicado a outras telas
- **Código Limpo**: Separação clara de responsabilidades
- **Manutenibilidade**: Fácil de configurar e estender
- **Performance**: Otimizado para evitar salvamentos desnecessários

## Considerações de Performance

- **Debouncing**: Timer evita múltiplas chamadas de API
- **Detecção Inteligente**: Só salva quando há mudanças reais
- **Cache Eficiente**: localStorage usado apenas quando necessário
- **Cleanup**: Timers e event listeners são limpos adequadamente

## Compatibilidade

- **Navegadores**: Suporte a todos os navegadores modernos
- **Dispositivos**: Responsivo para desktop, tablet e mobile
- **Acessibilidade**: Toasts e FAB seguem padrões de acessibilidade
- **Performance**: Otimizado para dispositivos com recursos limitados

## Próximos Passos

1. **Extensão**: Aplicar sistema a outras telas de edição
2. **Métricas**: Adicionar analytics para monitorar uso
3. **Personalização**: Permitir usuários configurarem delay
4. **Integração**: Conectar com sistema de notificações push
