<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('financeiro_receber', function (Blueprint $table) {
            // Adicionar campos para relacionamento com dentista
            $table->unsignedBigInteger('dentista_id')->nullable()->after('paciente_id');
            
            // Adicionar campos para observações detalhadas
            $table->text('observacoes')->nullable()->after('descricao');
            
            // Adicionar campos para cálculos de desconto e acréscimo mais precisos
            $table->decimal('percentual_desconto', 5, 2)->default(0)->after('valor_nominal');
            $table->decimal('valor_desconto', 10, 2)->default(0)->after('percentual_desconto');
            $table->decimal('percentual_acrescimo', 5, 2)->default(0)->after('valor_desconto');
            $table->decimal('valor_acrescimo', 10, 2)->default(0)->after('percentual_acrescimo');
            
            // Adicionar campos para parcelamento inteligente
            $table->unsignedInteger('parcela_numero')->default(1)->after('meio_pagamento');
            $table->unsignedBigInteger('fatura_principal_id')->nullable()->after('parcela_numero');
            
            // Adicionar índices para melhor performance
            $table->index(['clinica_id', 'paciente_id'], 'idx_clinica_paciente');
            $table->index(['status', 'data_vencimento'], 'idx_status_vencimento');
            $table->index(['fatura_principal_id'], 'idx_fatura_principal');
            
            // Adicionar foreign keys
            $table->foreign('dentista_id')->references('id')->on('dentistas')->onDelete('set null');
            $table->foreign('fatura_principal_id')->references('id')->on('financeiro_receber')->onDelete('cascade');
        });

        // Atualizar valores padrão para registros existentes
        DB::table('financeiro_receber')->update([
            'percentual_desconto' => 0,
            'valor_desconto' => 0,
            'percentual_acrescimo' => 0,
            'valor_acrescimo' => 0,
            'parcela_numero' => 1,
            'status' => DB::raw("COALESCE(status, 'pendente')")
        ]);

        // Recalcular valor_final para registros existentes que não têm
        DB::statement('
            UPDATE financeiro_receber 
            SET valor_final = COALESCE(valor_nominal, 0) - COALESCE(valor_desconto, 0) + COALESCE(valor_acrescimo, 0)
            WHERE valor_final IS NULL OR valor_final = 0
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('financeiro_receber', function (Blueprint $table) {
            // Remover foreign keys primeiro
            $table->dropForeign(['dentista_id']);
            $table->dropForeign(['fatura_principal_id']);
            
            // Remover índices
            $table->dropIndex('idx_clinica_paciente');
            $table->dropIndex('idx_status_vencimento');
            $table->dropIndex('idx_fatura_principal');
            
            // Remover colunas adicionadas
            $table->dropColumn([
                'dentista_id',
                'observacoes',
                'percentual_desconto',
                'valor_desconto',
                'percentual_acrescimo',
                'valor_acrescimo',
                'parcela_numero',
                'fatura_principal_id'
            ]);
        });
    }
};
