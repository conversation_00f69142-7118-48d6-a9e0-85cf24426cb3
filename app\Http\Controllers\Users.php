<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Dentista;
use App\Models\Clinica;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Illuminate\Validation\Rule;

class Users extends Controller
{
    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): Response
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        event(new Registered($user));

        Auth::login($user);

        return response()->noContent();
    }

    /**
     * Update the authenticated user's profile.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateProfile(Request $request)
    {
        try {
            $user = auth()->user();
            $payload = auth()->payload();

            // Validate request data
            $validatedData = $request->validate([
                'email' => [
                    'sometimes',
                    'nullable',
                    'string',
                    'email',
                    'max:255',
                    Rule::unique('users')->ignore($user->id),
                ],
                'password' => ['sometimes', 'string', 'min:8'],
                'nome' => ['sometimes', 'string', 'max:255'],
                'username' => ['sometimes', 'string', 'max:255'],
                'clinica' => ['sometimes', 'string', 'max:255'],
                'language' => ['sometimes', 'string', 'max:10'],
            ]);

            // Update email if provided
            if (isset($validatedData['email'])) {
                $user->email = $validatedData['email'];
            }

            // Update password if provided
            if (isset($validatedData['password'])) {
                $user->password = Hash::make($validatedData['password']);
            }

            // Update username if provided
            if (isset($validatedData['username'])) {
                $user->username = $validatedData['username'];
            }

            // Update dentista name if provided and user has a dentista record
            if (isset($validatedData['nome']) && isset($payload['dentista'])) {
                $dentista = Dentista::find($payload['dentista']['id']);
                if ($dentista) {
                    $dentista->nome = $validatedData['nome'];
                    $dentista->save();
                }
            }

            // Update clinica name if provided and user has a clinica in payload
            if (isset($validatedData['clinica']) && isset($payload['clinica'])) {
                $clinica = Clinica::find($payload['clinica']['id']);
                if ($clinica) {
                    $clinica->nome = $validatedData['clinica'];
                    $clinica->save();
                }
            }

            // Update language if provided
            if (isset($validatedData['language'])) {
                $user->language = $validatedData['language'];
            }

            $user->save();

            return responseSuccess();
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage()
            ]);
        }
    }
}
