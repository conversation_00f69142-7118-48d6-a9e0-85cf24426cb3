<template>
  <div class="orcamento-form">
    <div class="row g-3">
      <!-- Paciente -->
      <div class="col-md-6">
        <label class="form-label">Paciente *</label>
        <paciente-busca-button
          :paciente-selecionado="pacienteSelecionado"
          :has-error="!!errors.paciente_id"
          :error-message="errors.paciente_id"
          :disabled="preselectedPaciente"
          placeholder="Clique na lupa para buscar um paciente..."
          @abrir-busca="$emit('abrir-busca-paciente')"
          @limpar-selecao="$emit('limpar-paciente')"
        />
      </div>

      <!-- Dentista -->
      <div class="col-md-6">
        <label class="form-label">Ortodontista</label>
        <select class="form-select" 
                :value="form.dentista_id" 
                @change="$emit('update-form', 'dentista_id', $event.target.value)">
          <option value="">Selecione um ortodontista</option>
          <option v-for="dentista in dentistas" :key="dentista?.id || Math.random()" :value="dentista?.id">
            {{ dentista?.nome || 'Ortodontista sem nome' }}
          </option>
        </select>
      </div>

      <!-- Título -->
      <div class="col-md-8">
        <label class="form-label">Título do Orçamento *</label>
        <input type="text" 
               class="form-control" 
               :value="form.titulo" 
               @input="$emit('update-form', 'titulo', $event.target.value)"
               :class="{ 'is-invalid': errors.titulo }"
               placeholder="Ex: Tratamento Ortodôntico Completo">
        <div class="invalid-feedback" v-if="errors.titulo">
          {{ errors.titulo }}
        </div>
      </div>

      <!-- Data de Validade -->
      <div class="col-md-4">
        <label class="form-label">Data de Validade</label>
        <input type="date" 
               class="form-control" 
               :value="form.data_validade"
               @change="$emit('update-form', 'data_validade', $event.target.value)"
               :min="minDate">
      </div>

      <!-- Descrição -->
      <div class="col-12">
        <label class="form-label">Descrição</label>
        <textarea class="form-control" 
                  rows="2" 
                  :value="form.descricao"
                  @input="$emit('update-form', 'descricao', $event.target.value)"
                  placeholder="Descrição geral do orçamento"></textarea>
      </div>
    </div>

    <!-- Itens do Orçamento -->
    <div class="row mt-4">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h6 class="mb-0">Itens do Orçamento</h6>
          <button type="button" class="btn btn-sm btn-outline-primary" @click="$emit('add-item')">
            <i class="fas fa-plus me-1"></i>
            Adicionar Item
          </button>
        </div>

        <div class="card">
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-hover mb-0">
                <thead class="table-light">
                  <tr>
                    <th style="width: 30%">Serviço/Produto</th>
                    <th style="width: 10%">Qtd</th>
                    <th style="width: 15%">Valor Unit.</th>
                    <th style="width: 15%">Desconto</th>
                    <th style="width: 15%">Total</th>
                    <th style="width: 10%">Ações</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in form.itens" :key="index">
                    <!-- Serviço/Produto -->
                    <td>
                      <div class="mb-2">
                        <select class="form-select form-select-sm" 
                                :value="item.servico_produto_id"
                                @change="selectServicoProduto(index, $event.target.value)">
                          <option value="">Selecionar serviço...</option>
                          <option v-for="servico in servicosProdutos"
                                  :key="servico?.id || Math.random()"
                                  :value="servico?.id">
                            {{ servico?.nome || 'Serviço sem nome' }} - {{ formatCurrency(servico?.valor_base || 0) }}
                          </option>
                        </select>
                      </div>
                      <input type="text" 
                             class="form-control form-control-sm" 
                             :value="item.nome"
                             @input="updateItem(index, 'nome', $event.target.value)"
                             placeholder="Nome do item"
                             :class="{ 'is-invalid': errors[`itens.${index}.nome`] }">
                    </td>

                    <!-- Quantidade -->
                    <td>
                      <input type="number" 
                             step="0.01" 
                             min="0.01"
                             class="form-control form-control-sm" 
                             :value="item.quantidade"
                             @input="updateItem(index, 'quantidade', parseFloat($event.target.value) || 1)"
                             :class="{ 'is-invalid': errors[`itens.${index}.quantidade`] }">
                    </td>

                    <!-- Valor Unitário -->
                    <td>
                      <div class="input-group input-group-sm">
                        <span class="input-group-text">R$</span>
                        <input type="number" 
                               step="0.01" 
                               min="0.01"
                               class="form-control" 
                               :value="item.valor_unitario"
                               @input="updateItem(index, 'valor_unitario', parseFloat($event.target.value) || 0)"
                               :class="{ 'is-invalid': errors[`itens.${index}.valor_unitario`] }">
                      </div>
                    </td>

                    <!-- Desconto -->
                    <td>
                      <div class="input-group input-group-sm">
                        <input type="number" 
                               step="0.01" 
                               min="0"
                               max="100"
                               class="form-control" 
                               :value="item.desconto_percentual"
                               @input="updateItemDesconto(index, 'percentual', parseFloat($event.target.value) || 0)"
                               placeholder="%">
                        <span class="input-group-text">%</span>
                      </div>
                    </td>

                    <!-- Total -->
                    <td>
                      <div class="fw-bold text-success">
                        {{ formatCurrency(calculateItemTotal(item)) }}
                      </div>
                    </td>

                    <!-- Ações -->
                    <td>
                      <div class="d-flex gap-1">
                        <button type="button"
                                class="btn btn-sm btn-outline-info"
                                @click="toggleItemDetails(index)"
                                :title="expandedItems.includes(index) ? 'Ocultar detalhes e seleção de dentes' : 'Mostrar detalhes e seleção de dentes'">
                          <i :class="expandedItems.includes(index) ? 'fas fa-chevron-up' : 'fas fa-tooth'"></i>
                        </button>
                        <button type="button"
                                class="btn btn-sm btn-outline-danger"
                                @click="$emit('remove-item', index)"
                                :disabled="form.itens.length <= 1">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>

                  <!-- Linha expandível com detalhes do item -->
                  <tr v-if="expandedItems.includes(index)" class="item-details-row">
                    <td colspan="6" class="p-3 bg-light">
                      <div class="row g-3">
                        <!-- Descrição do Item -->
                        <div class="col-md-6">
                          <label class="form-label">Descrição Detalhada</label>
                          <textarea class="form-control"
                                    rows="3"
                                    :value="item.descricao"
                                    @input="updateItem(index, 'descricao', $event.target.value)"
                                    placeholder="Descrição detalhada do procedimento"></textarea>
                        </div>

                        <!-- Seletor de Dentes -->
                        <div class="col-md-6">
                          <dente-selector
                            v-model="item.dentes"
                            @update:modelValue="updateItem(index, 'dentes', $event)"
                          />
                        </div>

                        <!-- Observações do Item -->
                        <div class="col-12">
                          <label class="form-label">Observações do Item</label>
                          <textarea class="form-control"
                                    rows="2"
                                    :value="item.observacoes"
                                    @input="updateItem(index, 'observacoes', $event.target.value)"
                                    placeholder="Observações específicas deste item"></textarea>
                        </div>
                      </div>
                    </td>
                  </tr>
                </tbody>
                <tfoot class="table-light">
                  <tr>
                    <td colspan="4" class="text-end fw-bold">Total Geral:</td>
                    <td class="fw-bold text-success fs-5">{{ formatCurrency(totalGeral) }}</td>
                    <td></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Observações -->
    <div class="row mt-3">
      <div class="col-12">
        <label class="form-label">Observações</label>
        <textarea class="form-control" 
                  rows="3" 
                  :value="form.observacoes"
                  @input="$emit('update-form', 'observacoes', $event.target.value)"
                  placeholder="Observações adicionais do orçamento"></textarea>
      </div>
    </div>
  </div>
</template>

<script>
import { orcamentoService } from '@/services/orcamentoService';
import PacienteBuscaButton from '@/components/Global/PacienteBuscaButton.vue';
import DenteSelector from './DenteSelector.vue';

export default {
  name: 'OrcamentoForm',
  components: {
    PacienteBuscaButton,
    DenteSelector,
  },
  props: {
    form: {
      type: Object,
      required: true
    },
    errors: {
      type: Object,
      default: () => ({})
    },
    pacientes: {
      type: Array,
      default: () => []
    },
    dentistas: {
      type: Array,
      default: () => []
    },
    servicosProdutos: {
      type: Array,
      default: () => []
    },
    preselectedPaciente: {
      type: [String, Number],
      default: null
    },
    pacienteSelecionado: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      expandedItems: []
    };
  },
  computed: {
    minDate() {
      return new Date().toISOString().split('T')[0];
    },
    totalGeral() {
      return this.form.itens.reduce((sum, item) => sum + this.calculateItemTotal(item), 0);
    }
  },
  methods: {
    formatCurrency: orcamentoService.formatCurrency,

    calculateItemTotal(item) {
      return orcamentoService.calculateItemTotal(
        item.quantidade,
        item.valor_unitario,
        item.desconto_percentual,
        item.desconto_valor
      );
    },

    updateItem(index, field, value) {
      const updatedItens = [...this.form.itens];
      updatedItens[index][field] = value;
      this.$emit('update-form', 'itens', updatedItens);
    },

    toggleItemDetails(index) {
      const expandedIndex = this.expandedItems.indexOf(index);
      if (expandedIndex > -1) {
        this.expandedItems.splice(expandedIndex, 1);
      } else {
        this.expandedItems.push(index);
      }
    },

    updateItemDesconto(index, type, value) {
      const updatedItens = [...this.form.itens];
      
      if (type === 'percentual') {
        updatedItens[index].desconto_percentual = value;
        updatedItens[index].desconto_valor = 0; // Limpar valor fixo
      } else {
        updatedItens[index].desconto_valor = value;
        updatedItens[index].desconto_percentual = 0; // Limpar percentual
      }
      
      this.$emit('update-form', 'itens', updatedItens);
    },

    selectServicoProduto(index, servicoId) {
      const servico = this.servicosProdutos.find(s => s.id == servicoId);
      const updatedItens = [...this.form.itens];
      
      if (servico) {
        updatedItens[index].servico_produto_id = servicoId;
        updatedItens[index].nome = servico.nome;
        updatedItens[index].valor_unitario = servico.valor_base;
        updatedItens[index].descricao = servico.descricao || '';
      } else {
        updatedItens[index].servico_produto_id = null;
      }
      
      this.$emit('update-form', 'itens', updatedItens);
    }
  }
};
</script>

<style scoped>
.table th {
  border-top: none;
  font-weight: 600;
  font-size: 0.875rem;
}

.form-control-sm, .form-select-sm {
  font-size: 0.875rem;
}

.input-group-text {
  background-color: #f8f9fa;
  border-color: #dee2e6;
  font-size: 0.875rem;
}

.is-invalid {
  border-color: #dc3545;
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.025);
}

.item-details-row {
  border-top: 2px solid #007bff;
}

.item-details-row td {
  background-color: #f8f9fa !important;
  border-bottom: 2px solid #dee2e6;
}

.d-flex.gap-1 {
  gap: 0.25rem;
}
</style>
