<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateMentoriasTableAddStatusAndMentor extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('mentorias', function (Blueprint $table) {
            // Alterar o status para ter os novos valores
            $table->string('status')->default('AGUARDANDO')->change();
            
            // Tornar mentor_id nullable (será preenchido quando iniciar a mentoria)
            $table->unsignedBigInteger('mentor_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('mentorias', function (Blueprint $table) {
            $table->string('status')->default('PENDENTE')->change();
            $table->unsignedBigInteger('mentor_id')->nullable(false)->change();
        });
    }
}
