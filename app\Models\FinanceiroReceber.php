<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Clinica;
use App\Models\Paciente;
use App\Models\Dentista;

class FinanceiroReceber extends Model
{
    use HasFactory, Notifiable, LogsActivity;

    protected $table = 'financeiro_receber';

    // Constantes de status
    const STATUS_PENDENTE = 'pendente';
    const STATUS_PAGO = 'pago';
    const STATUS_VENCIDO = 'vencido';
    const STATUS_CANCELADO = 'cancelado';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'clinica_id',
        'paciente_id',
        'dentista_id',
        'contrato_codigo',
        'referencia',
        'descricao',
        'observacoes',
        'valor_nominal',
        'percentual_desconto',
        'valor_desconto',
        'percentual_acrescimo',
        'valor_acrescimo',
        'valor_final',
        'data_emissao',
        'data_vencimento',
        'data_pagamento',
        'meio_pagamento',
        'parcela_numero',
        'parcelas_total',
        'fatura_principal_id',
        'status',
        'lancado_por',
        // Campos legados (manter compatibilidade)
        'caixa_id',
        'conta_id',
        'pagador_nome',
        'pagador_tipo',
        'fornecedor_id',
        'notas',
        'descontos',
        'acrescimos',
        'parcela',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'data_emissao' => 'date',
            'data_vencimento' => 'date',
            'data_pagamento' => 'date',
            'valor_nominal' => 'decimal:2',
            'percentual_desconto' => 'decimal:2',
            'valor_desconto' => 'decimal:2',
            'percentual_acrescimo' => 'decimal:2',
            'valor_acrescimo' => 'decimal:2',
            'valor_final' => 'decimal:2',
        ];
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*']);
    }

    // ==================== RELACIONAMENTOS ====================

    public function clinica(): BelongsTo
    {
        return $this->belongsTo(Clinica::class, 'clinica_id');
    }

    public function paciente(): BelongsTo
    {
        return $this->belongsTo(Paciente::class, 'paciente_id');
    }

    public function dentista(): BelongsTo
    {
        return $this->belongsTo(Dentista::class, 'dentista_id');
    }

    public function faturaPrincipal(): BelongsTo
    {
        return $this->belongsTo(FinanceiroReceber::class, 'fatura_principal_id');
    }

    public function parcelas(): HasMany
    {
        return $this->hasMany(FinanceiroReceber::class, 'fatura_principal_id');
    }

    public function lancadoPor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'lancado_por');
    }

    // ==================== SCOPES ====================

    public function scopeDaClinica(Builder $query, int $clinicaId): Builder
    {
        return $query->where('clinica_id', $clinicaId);
    }

    public function scopeDoPaciente(Builder $query, int $pacienteId): Builder
    {
        return $query->where('paciente_id', $pacienteId);
    }

    public function scopePendentes(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_PENDENTE);
    }

    public function scopePagas(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_PAGO);
    }

    public function scopeVencidas(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_VENCIDO)
                    ->orWhere(function ($q) {
                        $q->where('status', self::STATUS_PENDENTE)
                          ->where('data_vencimento', '<', now()->toDateString());
                    });
    }

    public function scopeCanceladas(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_CANCELADO);
    }

    // ==================== MÉTODOS PRINCIPAIS ====================

    /**
     * Calcular valor final baseado no valor nominal, descontos e acréscimos
     */
    public function calcularValorFinal(): float
    {
        $valorNominal = (float) $this->valor_nominal;
        $valorDesconto = (float) $this->valor_desconto;
        $valorAcrescimo = (float) $this->valor_acrescimo;
        $percentualDesconto = (float) $this->percentual_desconto;
        $percentualAcrescimo = (float) $this->percentual_acrescimo;

        // Calcular desconto por percentual se não houver valor fixo
        if ($valorDesconto == 0 && $percentualDesconto > 0) {
            $valorDesconto = ($valorNominal * $percentualDesconto) / 100;
        }

        // Calcular acréscimo por percentual se não houver valor fixo
        if ($valorAcrescimo == 0 && $percentualAcrescimo > 0) {
            $valorAcrescimo = ($valorNominal * $percentualAcrescimo) / 100;
        }

        return $valorNominal - $valorDesconto + $valorAcrescimo;
    }

    /**
     * Marcar fatura como paga
     */
    public function marcarComoPago(?string $dataPagamento = null, ?string $meioPagamento = null): void
    {
        $this->update([
            'status' => self::STATUS_PAGO,
            'data_pagamento' => $dataPagamento ?: now()->toDateString(),
            'meio_pagamento' => $meioPagamento
        ]);
    }

    /**
     * Cancelar fatura
     */
    public function cancelar(): void
    {
        $this->update(['status' => self::STATUS_CANCELADO]);
    }

    /**
     * Criar fatura com parcelamento inteligente
     */
    public static function criarComParcelamento(array $dados, int $numeroParcelas): array
    {
        $dataVencimento = Carbon::parse($dados['data_vencimento']);
        $valorTotal = (float) $dados['valor_nominal'];

        // Calcular valor final considerando descontos e acréscimos
        $valorDesconto = (float) ($dados['valor_desconto'] ?? 0);
        $valorAcrescimo = (float) ($dados['valor_acrescimo'] ?? 0);
        $percentualDesconto = (float) ($dados['percentual_desconto'] ?? 0);
        $percentualAcrescimo = (float) ($dados['percentual_acrescimo'] ?? 0);

        // Calcular desconto por percentual se não houver valor fixo
        if ($valorDesconto == 0 && $percentualDesconto > 0) {
            $valorDesconto = ($valorTotal * $percentualDesconto) / 100;
        }

        // Calcular acréscimo por percentual se não houver valor fixo
        if ($valorAcrescimo == 0 && $percentualAcrescimo > 0) {
            $valorAcrescimo = ($valorTotal * $percentualAcrescimo) / 100;
        }

        $valorFinalTotal = $valorTotal - $valorDesconto + $valorAcrescimo;
        $valorPorParcela = $valorFinalTotal / $numeroParcelas;

        $faturas = [];

        // Criar fatura principal (primeira parcela)
        $dadosFaturaPrincipal = array_merge($dados, [
            'parcela_numero' => 1,
            'parcelas_total' => $numeroParcelas,
            'valor_nominal' => $valorPorParcela,
            'valor_final' => $valorPorParcela,
            'status' => self::STATUS_PENDENTE,
            'data_vencimento' => $dataVencimento->toDateString(),
        ]);

        $faturaPrincipal = self::create($dadosFaturaPrincipal);
        $faturas[] = $faturaPrincipal;

        // Criar parcelas subsequentes
        for ($i = 2; $i <= $numeroParcelas; $i++) {
            $dataVencimentoParcela = self::calcularProximoVencimento($dataVencimento, $i - 1);

            $dadosParcela = array_merge($dados, [
                'fatura_principal_id' => $faturaPrincipal->id,
                'parcela_numero' => $i,
                'parcelas_total' => $numeroParcelas,
                'valor_nominal' => $valorPorParcela,
                'valor_final' => $valorPorParcela,
                'percentual_desconto' => 0,
                'valor_desconto' => 0,
                'percentual_acrescimo' => 0,
                'valor_acrescimo' => 0,
                'status' => self::STATUS_PENDENTE,
                'data_vencimento' => $dataVencimentoParcela->toDateString(),
            ]);

            $faturas[] = self::create($dadosParcela);
        }

        return $faturas;
    }

    /**
     * Calcular próximo vencimento considerando meses com menos dias
     */
    private static function calcularProximoVencimento(Carbon $dataBase, int $mesesAFrente): Carbon
    {
        $diaOriginal = $dataBase->day;
        $novaData = $dataBase->copy()->addMonths($mesesAFrente);

        // Se o dia original não existe no mês de destino, usar o último dia do mês
        $ultimoDiaDoMes = $novaData->daysInMonth;

        if ($diaOriginal > $ultimoDiaDoMes) {
            $novaData->day = $ultimoDiaDoMes;
        } else {
            $novaData->day = $diaOriginal;
        }

        return $novaData;
    }

    /**
     * Verificar se a fatura está vencida
     */
    public function isVencida(): bool
    {
        return $this->status === self::STATUS_PENDENTE &&
               $this->data_vencimento < now()->toDateString();
    }

    /**
     * Atualizar status baseado na data de vencimento
     */
    public function atualizarStatus(): void
    {
        if ($this->status === self::STATUS_PENDENTE && $this->isVencida()) {
            $this->update(['status' => self::STATUS_VENCIDO]);
        }
    }
}
