<?php

use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Dentistas;
use App\Http\Controllers\ConsultaController;
use App\Http\Controllers\MentoriaController;
use App\Http\Controllers\PacientesController;
use App\Http\Controllers\TratamentoController;
use App\Http\Controllers\AparatologiaController;
use App\Http\Controllers\MetaTerapeuticaController;
use App\Http\Controllers\ImageController;
use App\Http\Controllers\Modelo3DController;
use App\Http\Controllers\ClinicaController;
use App\Http\Controllers\ContatoPacienteController;
use App\Http\Controllers\ContatoDentistaController;
use App\Http\Controllers\NecessidadeEncaminhamentoController;
use App\Http\Controllers\HistoricoPacienteController;
use App\Http\Controllers\ActionHistoryController;
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\Auth\EmailVerificationNotificationController;
use App\Http\Controllers\Auth\NewPasswordController;
use App\Http\Controllers\Auth\PasswordResetLinkController;
use App\Http\Controllers\Auth\VerifyEmailController;
use App\Http\Controllers\FinanceiroReceberController;
use App\Http\Controllers\OrcamentoController;
use App\Http\Controllers\ServicoProdutoController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\Users;
use App\Models\ContatoPaciente;
use App\Http\Middleware\CheckSystemAdmin;

Route::group(['prefix' => 'pacientes'], function ()
{
    Route::get('get-by-token/{token}', [PacientesController::class, 'getByToken']);
    Route::get('ficha-inicial-by-token/{token}', [PacientesController::class, 'getFichaInicialByToken']);

    Route::post('welcome-form', [PacientesController::class, 'submitWelcomeForm']);
});

// -----------------------------------------------------

Route::group(['prefix' => 'img'], function() {
    Route::get('{url}', [ImageController::class, 'serveImage']);
});

Route::group(['middleware' => ['jwt.auth', 'action_history']], function ()
{

    Route::group(['prefix' => 'modelo3d'], function() {
        Route::get('{url}', [Modelo3DController::class, 'serveModelo3D']);
    });

    // -----------------------------------------------------

    Route::group(['prefix' => 'img'], function() {
        Route::post('', [ImageController::class, 'uploadImage']);
    });

    Route::group(['prefix' => 'imagem'], function() {
        Route::delete('por-data', [ImageController::class, 'deleteByDateAndPatient']);
    });

    Route::group(['prefix' => 'modelo3d'], function() {
        Route::delete('por-data', [Modelo3DController::class, 'deleteByDateAndPatient']);
    });

    Route::group(['prefix' => 'modelo3d'], function() {
        Route::post('', [Modelo3DController::class, 'uploadModelo3D']);
    });

    // Rota para obter modelos 3D de um paciente específico
    Route::get('paciente/{paciente_id}/modelos3d', [Modelo3DController::class, 'getModelos3DByPaciente']);

    // Rota para obter imagens de um paciente específico
    Route::get('paciente/{paciente_id}/imagens', [ImageController::class, 'getImagensByPaciente']);

    // -----------------------------------------------------

    Route::group(['prefix' => 'pacientes'], function ()
    {
        Route::post('search', [PacientesController::class, 'search']);

        Route::post('upload-image', [PacientesController::class, 'uploadImage']);

        Route::post('salvar-diagnostico', [PacientesController::class, 'salvarDiagnostico']);

        Route::post('salvar-prognostico', [PacientesController::class, 'salvarPrognostico']);

        Route::get('ficha-inicial/{paciente_id}', [PacientesController::class, 'getFichaInicial']);

        // Rota para obter modelos 3D de um paciente
        Route::get('{paciente_id}/modelos3d', [Modelo3DController::class, 'getModelos3DByPaciente']);

        // Rota para obter lista simplificada de pacientes da clínica atual
        Route::get('lista-simples', [PacientesController::class, 'listaSimples']);
    });

    // -----------------------------------------------------

    Route::group(['prefix' => 'contatos-dentistas'], function ()
    {
        Route::delete('{id}', [ContatoDentistaController::class, 'deleteContato']);
    });

    // -----------------------------------------------------

    Route::group(['prefix' => 'contatos-pacientes'], function ()
    {
        Route::delete('{id}', [ContatoPacienteController::class, 'deleteContato']);
    });

    // -----------------------------------------------------

    Route::group(['prefix' => 'dentistas'], function ()
    {
        Route::post('search', [Dentistas::class, 'search']);
    });

    // -----------------------------------------------------

    Route::group(['prefix' => 'mentorias'], function ()
    {
        Route::get('', [MentoriaController::class, 'getAll']);
        Route::get('{id}', [MentoriaController::class, 'getMentoria']);
        Route::post('solicitar', [MentoriaController::class, 'solicitarMentoria']);
        Route::post('mensagem', [MentoriaController::class, 'enviarMensagem']);
        Route::post('mensagem-com-imagens', [MentoriaController::class, 'enviarMensagemComImagens']);
        Route::put('{id}/marcar-lidas', [MentoriaController::class, 'marcarMensagensComoLidas']);
        Route::put('{id}/iniciar', [MentoriaController::class, 'iniciarMentoria']);
        Route::put('{id}/finalizar', [MentoriaController::class, 'finalizarMentoria']);
        Route::put('{id}/cancelar', [MentoriaController::class, 'cancelarMentoria']);
        Route::put('{id}/reabrir', [MentoriaController::class, 'reabrirMentoria']);
    });

    // -----------------------------------------------------

    Route::group(['prefix' => 'tratamentos'], function ()
    {
        Route::get('analises/{paciente_id}', [TratamentoController::class, 'getAnalises']);

        Route::post('analises', [TratamentoController::class, 'salvarAnalises']);

        Route::post('metas_terapeuticas', [TratamentoController::class, 'salvarMetasTerapeuticas']);

        Route::post('add-meta', [TratamentoController::class, 'addMetaTerapeutica']);

        Route::delete('meta-terapeutica/{id}', [TratamentoController::class, 'deleteMetaTerapeutica']);

        Route::post('iniciar-tratamento/{idPaciente}', [TratamentoController::class, 'iniciarTratamento']);
    });

    // -----------------------------------------------------

    // Route::group(['prefix' => 'admin'], function () {
    //     Route::get('/dashboard', [AdminController::class, 'dashboard']);
    //     Route::get('/users', [AdminController::class, 'users']);
    // });

    // Models com rotas que apenas admins gerenciam:
    Route::apiResource('clinicas', ClinicaController::class)->middleware('system_admin');

    // Rotas para pegar informações apenas de um determinado clinica_slug:
    Route::get('{clinica_slug}/dentistas/{id_matricula}', [Dentistas::class, 'showWithClinicaSlug']);
    Route::get('{clinica_slug}/dentistas', [Dentistas::class, 'getClinicaDentistas']);

    Route::get('{clinica_slug}/pacientes/{id_ficha}', [PacientesController::class, 'showWithClinicaSlug']);
    Route::get('{clinica_slug}/pacientes', [PacientesController::class, 'getClinicaPacientes']);
    // ------------------------------------------------------------------

    // Models com rotas comuns entre admins e usuários
    Route::apiResource('dentistas', Dentistas::class);
    Route::apiResource('pacientes', PacientesController::class);

    // Rotas de consultas
    Route::apiResource('consultas', ConsultaController::class);

    // Rotas personalizadas para consultas
    Route::group(['prefix' => 'consultas'], function () {
        Route::get('paciente/{paciente_id}/periodo/{timestamp_inicio}/{timestamp_fim}', [ConsultaController::class, 'getConsultasByPacienteAndTimeRange']);
        Route::get('periodo/{timestamp_inicio}/{timestamp_fim}', [ConsultaController::class, 'getConsultasByTimeRange']);
        Route::get('paciente/{paciente_id}', [ConsultaController::class, 'getConsultasByPaciente']);
    });

    Route::apiResource('contatos-pacientes', ContatoPacienteController::class);
    Route::apiResource('contatos-dentistas', ContatoDentistaController::class);
    Route::apiResource('metas-terapeuticas', MetaTerapeuticaController::class, [
        'parameters' => [
            'metas-terapeuticas' => 'metaTerapeutica'
        ]
    ]);

    Route::apiResource('necessidades-encaminhamentos', NecessidadeEncaminhamentoController::class);
    Route::apiResource('aparatologia', AparatologiaController::class);
    Route::apiResource('imagem', ImageController::class);
    Route::apiResource('modelo3d', Modelo3DController::class);

    // Rotas de histórico de pacientes
    Route::group(['prefix' => 'historicos-pacientes'], function () {
        Route::get('{paciente_id}', [HistoricoPacienteController::class, 'getHistoricoByPaciente']);
        Route::post('consulta/{consulta_id}/observacoes', [HistoricoPacienteController::class, 'updateConsultaObservacoes']);
    });
    Route::apiResource('historicos-pacientes', HistoricoPacienteController::class, [
        'parameters' => [
            'historicos-pacientes' => 'historicoPaciente'
        ]
    ]);

    // Rotas personalizadas para faturas (devem vir ANTES do apiResource)
    Route::group(['prefix' => 'faturas'], function () {
        Route::get('estatisticas', [FinanceiroReceberController::class, 'estatisticas']);
        Route::get('paciente/{paciente_id}', [FinanceiroReceberController::class, 'faturasPaciente']);
        Route::post('{id}/marcar-como-pago', [FinanceiroReceberController::class, 'marcarComoPago']);
    });

    // Rotas do módulo financeiro
    Route::apiResource('faturas', FinanceiroReceberController::class);

    // Rotas do módulo de orçamentos
    Route::apiResource('orcamentos', OrcamentoController::class);

    // Rotas personalizadas para orçamentos
    Route::group(['prefix' => 'orcamentos'], function () {
        Route::post('{id}/enviar', [OrcamentoController::class, 'enviar']);
        Route::post('{id}/aprovar', [OrcamentoController::class, 'aprovar']);
        Route::post('{id}/converter-para-fatura', [OrcamentoController::class, 'converterParaFatura']);
        Route::post('{id}/duplicar', [OrcamentoController::class, 'duplicar']);
        Route::get('paciente/{paciente_id}', [OrcamentoController::class, 'orcamentosPaciente']);
        Route::get('estatisticas', [OrcamentoController::class, 'estatisticas']);
    });

    // Rotas de serviços e produtos
    Route::apiResource('servicos-produtos', ServicoProdutoController::class);

    // Rotas personalizadas para serviços/produtos
    Route::group(['prefix' => 'servicos-produtos'], function () {
        Route::post('{id}/toggle-status', [ServicoProdutoController::class, 'toggleStatus']);
        Route::get('tipos', [ServicoProdutoController::class, 'tipos']);
        Route::get('buscar-para-orcamento', [ServicoProdutoController::class, 'buscarParaOrcamento']);
    });



    // Rotas de histórico de ações
    Route::group(['prefix' => 'action-history'], function () {
        Route::get('', [ActionHistoryController::class, 'index']);
        Route::get('stats', [ActionHistoryController::class, 'getStats']);
        Route::get('export', [ActionHistoryController::class, 'export']);
        Route::get('patient/{paciente_id}', [ActionHistoryController::class, 'getByPatient']);
        Route::get('dentist/{dentista_id}', [ActionHistoryController::class, 'getByDentist']);
        Route::get('user/{user_id}', [ActionHistoryController::class, 'getByUser']);
        Route::get('{action_history}', [ActionHistoryController::class, 'show']);
    });

    // Rotas personalizadas para notificações (DEVEM vir ANTES do apiResource)
    Route::group(['prefix' => 'notifications'], function () {
        Route::get('unread-count', [NotificationController::class, 'unreadCount']);
        Route::post('mark-all-read', [NotificationController::class, 'markAllAsRead']);
        Route::delete('clear-old-read', [NotificationController::class, 'clearOldRead']);
    });

    // Rotas de notificações
    Route::apiResource('notifications', NotificationController::class);
});

// ---------------- DEFAULT ROUTES: ---------------------

Route::group([
    'middleware' => 'api',
    'prefix' => 'auth'
], function ($router) {

    Route::post('login', 'App\Http\Controllers\AuthController@login');
    Route::post('logout', 'App\Http\Controllers\AuthController@logout');
    Route::post('refresh', 'App\Http\Controllers\AuthController@refresh');
    Route::post('me', 'App\Http\Controllers\AuthController@me');
    Route::patch('profile', [Users::class, 'updateProfile'])->middleware('jwt.auth');

});

// Rota adicional para profile na raiz
Route::patch('profile', [Users::class, 'updateProfile'])->middleware('jwt.auth');

Route::post('forgot-password', [PasswordResetLinkController::class, 'store'])
    ->middleware('guest')
    ->name('password.email');

Route::post('reset-password', [NewPasswordController::class, 'store'])
    ->middleware('guest')
    ->name('password.store');

// ----------------------------------------------------------------------
// ---------------- NATIVE ROUTES REPLACED BY JWT-AUTH: -----------------

// Route::post('login', [AuthenticatedSessionController::class, 'store'])
//                 ->middleware('guest')
//                 ->name('login');

// Route::post('logout', [AuthenticatedSessionController::class, 'destroy'])
//                 ->middleware('auth')
//                 ->name('logout');

// ---------------------------------------------------------------------
// -------------- NATIVE FUNCTIONS THAT WILL NOT BE USED: ---------------

// Route::post('register', [RegisteredUserController::class, 'store'])
//     ->middleware('guest')
//     ->name('register');

// Route::get('verify-email/{id}/{hash}', VerifyEmailController::class)
//     ->middleware(['auth', 'signed', 'throttle:6,1'])
//     ->name('verification.verify');

// Route::post('email/verification-notification', [EmailVerificationNotificationController::class, 'store'])
//     ->middleware(['auth', 'throttle:6,1'])
//     ->name('verification.send');


