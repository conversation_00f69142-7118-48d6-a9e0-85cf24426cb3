<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class NotificationController extends Controller
{
    /**
     * Listar notificações do usuário autenticado
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();

        $query = Notification::forUser($user->id)
            ->orderBy('created_at', 'desc');

        // Filtrar por status de leitura se especificado
        if ($request->has('read')) {
            $read = filter_var($request->read, FILTER_VALIDATE_BOOLEAN);
            if ($read) {
                $query->read();
            } else {
                $query->unread();
            }
        }

        // Paginação
        $perPage = $request->get('per_page', 15);
        $notifications = $query->paginate($perPage);

        return responseSuccess($notifications);
    }

    /**
     * Criar nova notificação
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'type' => ['required', 'string', Rule::in(['info', 'success', 'warning', 'error'])],
            'user_id' => 'required|exists:users,id',
            'data' => 'nullable|array',
            'action_url' => 'nullable|string|max:500'
        ]);

        $notification = Notification::create($request->all());

        return responseSuccess($notification, 201);
    }

    /**
     * Exibir notificação específica
     */
    public function show(string $id): JsonResponse
    {
        $user = Auth::user();

        $notification = Notification::forUser($user->id)->findOrFail($id);

        return responseSuccess($notification);
    }

    /**
     * Marcar notificação como lida/não lida
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $user = Auth::user();

        $request->validate([
            'read' => 'required|boolean'
        ]);

        $notification = Notification::forUser($user->id)->findOrFail($id);

        if ($request->read) {
            $notification->markAsRead();
        } else {
            $notification->markAsUnread();
        }

        return responseSuccess($notification);
    }

    /**
     * Remover notificação
     */
    public function destroy(string $id): JsonResponse
    {
        $user = Auth::user();

        $notification = Notification::forUser($user->id)->findOrFail($id);
        $notification->delete();

        return responseSuccess(['message' => 'Notificação removida com sucesso']);
    }

    /**
     * Contar notificações não lidas
     */
    public function unreadCount(): JsonResponse
    {
        $user = Auth::user();

        $count = Notification::forUser($user->id)->unread()->count();

        return responseSuccess(['count' => $count]);
    }

    /**
     * Marcar todas as notificações como lidas
     */
    public function markAllAsRead(): JsonResponse
    {
        $user = Auth::user();

        $updated = Notification::forUser($user->id)
            ->unread()
            ->update([
                'read' => true,
                'read_at' => now()
            ]);

        return responseSuccess(['message' => "Marcadas {$updated} notificações como lidas"]);
    }

    /**
     * Limpar notificações lidas antigas (mais de 30 dias)
     */
    public function clearOldRead(): JsonResponse
    {
        $user = Auth::user();

        $deleted = Notification::forUser($user->id)
            ->read()
            ->where('read_at', '<', now()->subDays(30))
            ->delete();

        return responseSuccess(['message' => "Removidas {$deleted} notificações antigas"]);
    }
}
