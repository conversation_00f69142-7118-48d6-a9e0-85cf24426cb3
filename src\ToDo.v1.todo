✔ Wiki
☐ Sistema de tickets
✔ Sistema de notificações


✔ Revisar protocolos de tratamento
Financeiro a receber:
    ☐ Implementar odontograma
    ☐ Conferir sistema de orçamentos (clinicorp)
☐ CORRIGIR: verificar a expiração do token no front-end, em vez de aguardar a resposta do servidor
Melhorias de layout:
    ✔ Melhorar layout da tela de análise (a versão full screen)
    Agenda (tela inicial! - tem que ser robusta):
        ☐ Fazer um calendário robusto no canto superior direito (talvez aumentar a largura da sidenav para esta tela)
    Lumi SideNav:
        ✔ Melhorar fonte (deixar mais bold - fica bem melhor para ler)
        ✔ Melhorar efeitos hover dos botões (fazer animação bacana e sutil/elegante?)
    ☐ Melhorar opções do questionário inicial: tirar checkbox "Nenhuma" das perguntas que forem de checkboxes
    ☐ Adicionar texto "Iniciar" ao botão Play das análises
    ☐ Deixar mais elegante o estilo da seta de voltar do paciente para /pacientes    
    ☐ Verificar outro nome para a aba "Tratamento" (talvez nomear como Tratamento a aba da análise - se encontrar um nome melhor para a aba que vai conter as consultas+histórico+imagens)
    ☐ Melhorar layout dos campos de início do tratamento e meses previstos
    Visualização das imagens:
        ☐ Fazer espécie de "lupa" - e talvez aumentar o tamanho da imagem base na tela
        ☐ Tirar "X" de excluir as imagens do efeito hover -  fazer botão pequeno só quando ela tiver aberta em fullscreen
    Badge Paciente/Tratamento/"próxima consulta":
        ☐ Fazer ficar verdinha e com ícone de calendar-check quando já tiver marcada
        ☐ Fazer ficar default e com ícone calendar-day (sem check) quando não tiver marcada
Logs do Laravel:
    ☐ Traduzir
    ☐ Nos updates, salvar apenas os campos que foram alterados (isso gera a possibilidade de implementarmos um sistema de rollback das alterações - que seja "travado" de forma que seja possível restaurar apenas o último passo - verificar se é melhor apenas excluir a alteração anterior, ou registrar uma nova versão - seguir lógica do git??)
-- ---------------------------------------------------------
☐ Fazer sistema robusto de busca para pacientes e para ortodontistas
Fazer sistema de cobrança da mensalidade dos ortodontistas:
    ☐ Sistema interno do app para cobrança da própria mensalidade dos usuários (verificar se por segurança é obrigatório fazer isso em outro painel - um app dedicado a admins)
☐ Tirar botão de excluir dos grupos diários de imagens
☐ Fazer função de reagendar consultas
✔ Fazer menu de notificações
☐ Notificação de mentoria solicitada
☐ Resposta do mentor no menu de notificações
☐ Fazer opção para adicionar um histórico direto na tela do histórico:
    ☐ Verificar se precisa alterar o model
☐ Passar as metas terapêuticas para a parte do tratamento
☐ Tratamento (planejamento): remover os 4 quadrantes de metas terapêuticas e deixar só a opção para adicionar (e também "depende de outra área")
Dentista > Pacientes:
    ☐ Exibir listagem de pacientes (com busca simples):
        ☐ Ao clicar, envia para a tela do Paciente
Dentista > Consultas:
    ☐ Exibir listagem de consultas (com filtro simples)



☐ V3: Fazer dashboards analíticos de BI


☐ Criar testes automatizados para a API
☐ Criar versão dev do site (com tela chique de autenticação - "administração")
☐ Fazer carousel ficar "sticky" (preso ao topo quando rolar a tela)
☐ Adicionar padrão de horários na agenda (20 em 20m ou 30 em 30m, por exemplo - permitir que o usuário defina?)
☐ Consultas: criar uma aba para o histórico de consultas - nela aparecem os estados das metas terapêuticas (e permite adicionar?)
☐ Remover observações do tratamento recomendado
Header do tratamento:
    ☐ Manter o header fixo ao rolar a lista de consultas
Funcionalidades:
    ☐ Fazer filtro na agenda para alternar entre "Exibir canceladas"
    ☐ Dentista > Pacientes: marcar os que estiverem com mentoria solicitada
    ☐ Criar lista de funcionalidades do sistema
-- ----------------------------------------------------------------------------------------------------
Later?:
    ☐ Refinar layout para telas entre md e lg? ou telas lg? (na faixa de 768 a 1024p de largura)
    ☐ Adicionar opção para gerar carta de encaminhamento (PDF)
    ☐ Implementar solicitações de reagendamentos (para os funcionários receberem e reagendarem)
    ☐ Fazer último paciente acessado ficar no state global do app, para retornar à ele quando alternar entre as abas
    ☐ Opção para excluir/arquivar paciente
    ☐ Opção para excluir/arquivar ortodontista