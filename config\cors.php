<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Cross-Origin Resource Sharing (CORS) Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your settings for cross-origin resource sharing
    | or "CORS". This determines what cross-origin operations may execute
    | in web browsers. You are free to adjust these settings as needed.
    |
    | To learn more: https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
    |
    */

    'paths' => ['*'],

    'allowed_methods' => ['*'],

    'allowed_origins' => [
        'http://local.lumiorthosystem',
        'https://local.lumiorthosystem',
        'http://local.lumiorthosystem:8080',
        'https://local.lumiorthosystem:8080',

        'http://local.lumiorthosystem:8081',
        'https://local.lumiorthosystem:8081',

        'http://local.lumiorthosystem:8082',
        'https://local.lumiorthosystem:8082',

        'http://local2.lumiorthosystem',
        'https://local2.lumiorthosystem',
        'http://local2.lumiorthosystem:8081',
        'https://local2.lumiorthosystem:8081',

        'http://local3.lumiorthosystem',
        'https://local3.lumiorthosystem',
        'http://local3.lumiorthosystem:8082',
        'https://local3.lumiorthosystem:8082',

        'http://local4.lumiorthosystem',
        'https://local4.lumiorthosystem',
        'http://local4.lumiorthosystem:8083',
        'https://local4.lumiorthosystem:8083',

        'http://dev.lumiorthosystem.com.br',
        'https://dev.lumiorthosystem.com.br',

        'http://app.lumiorthosystem.com.br',
        'https://app.lumiorthosystem.com.br',
    ],

    'allowed_origins_patterns' => [],

    'allowed_headers' => ['*'],

    'exposed_headers' => [],

    'max_age' => 0,

    'supports_credentials' => true,

];
