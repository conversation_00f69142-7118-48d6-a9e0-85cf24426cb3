<?php

namespace App\Console\Commands;

use App\Models\Notification;
use App\Models\User;
use Illuminate\Console\Command;

class SendGlobalNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:send-global
                            {title : O título da notificação}
                            {message : A mensagem da notificação}
                            {--type=info : Tipo da notificação (info, success, warning, error)}
                            {--url= : URL de ação opcional}
                            {--data= : Dados adicionais em JSON}
                            {--exclude-admins : Excluir administradores do sistema}
                            {--only-admins : Enviar apenas para administradores}
                            {--clinica= : Enviar apenas para usuários de uma clínica específica}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Enviar notificação global para todos os usuários ou grupos específicos';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $title = $this->argument('title');
        $message = $this->argument('message');
        $type = $this->option('type');
        $actionUrl = $this->option('url');
        $data = $this->option('data');
        $excludeAdmins = $this->option('exclude-admins');
        $onlyAdmins = $this->option('only-admins');
        $clinicaId = $this->option('clinica');

        // Validar tipo
        if (!in_array($type, ['info', 'success', 'warning', 'error'])) {
            $this->error('Tipo inválido. Use: info, success, warning ou error');
            return 1;
        }

        // Validar dados JSON se fornecido
        $parsedData = null;
        if ($data) {
            $parsedData = json_decode($data, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->error('Dados JSON inválidos: ' . json_last_error_msg());
                return 1;
            }
        }

        // Construir query de usuários
        $usersQuery = User::query();

        // Filtrar por clínica se especificado
        if ($clinicaId) {
            $usersQuery->where('clinica_id', $clinicaId);
        }

        // Filtrar administradores
        if ($excludeAdmins) {
            $usersQuery->where('system_admin', false);
        } elseif ($onlyAdmins) {
            $usersQuery->where('system_admin', true);
        }

        $users = $usersQuery->get();

        if ($users->isEmpty()) {
            $this->warn('Nenhum usuário encontrado com os critérios especificados.');
            return 0;
        }

        // Confirmar envio
        $userCount = $users->count();
        $this->info("Título: {$title}");
        $this->info("Mensagem: {$message}");
        $this->info("Tipo: {$type}");
        if ($actionUrl) $this->info("URL: {$actionUrl}");
        $this->info("Usuários que receberão: {$userCount}");

        if (!$this->confirm('Deseja enviar esta notificação?')) {
            $this->info('Operação cancelada.');
            return 0;
        }

        // Criar notificações
        $notifications = [];
        $now = now();

        foreach ($users as $user) {
            $notifications[] = [
                'title' => $title,
                'message' => $message,
                'type' => $type,
                'user_id' => $user->id,
                'read' => false,
                'read_at' => null,
                'data' => $parsedData,
                'action_url' => $actionUrl,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        // Inserir em lotes para melhor performance
        $chunks = array_chunk($notifications, 100);
        $totalCreated = 0;

        $this->info('Enviando notificações...');
        $progressBar = $this->output->createProgressBar(count($chunks));

        foreach ($chunks as $chunk) {
            Notification::insert($chunk);
            $totalCreated += count($chunk);
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
        $this->info("✅ {$totalCreated} notificações enviadas com sucesso!");

        // Mostrar estatísticas
        $this->table(
            ['Estatística', 'Valor'],
            [
                ['Usuários alcançados', $userCount],
                ['Notificações criadas', $totalCreated],
                ['Tipo', $type],
                ['Data/Hora', $now->format('d/m/Y H:i:s')],
            ]
        );

        return 0;
    }
}
