<?php

namespace App\Http\Controllers;

use App\Models\FinanceiroReceber;
use App\Models\Paciente;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class FinanceiroReceberController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $query = FinanceiroReceber::with(['paciente', 'dentista'])
            ->daClinica($clinicaId);

        // Filtros
        if ($request->has('paciente_id')) {
            $query->doPaciente($request->paciente_id);
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('descricao')) {
            $query->where('descricao', 'LIKE', '%' . $request->descricao . '%');
        }

        if ($request->has('data_inicio') && $request->has('data_fim')) {
            $query->whereBetween('data_vencimento', [
                $request->data_inicio,
                $request->data_fim
            ]);
        }

        if ($request->has('vencidas') && $request->vencidas) {
            $query->vencidas();
        }

        $faturas = $query->orderBy('data_vencimento', 'desc')
                        ->orderBy('created_at', 'desc')
                        ->paginate(15);

        return responseSuccess(['data' => $faturas]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $validator = Validator::make($request->all(), [
            'paciente_id' => 'required|exists:pacientes,id',
            'descricao' => 'required|string|max:255',
            'valor_nominal' => 'required|numeric|min:0.01',
            'data_vencimento' => 'required|date',
            'parcelas_total' => 'integer|min:1|max:60',
            'percentual_desconto' => 'nullable|numeric|min:0|max:100',
            'valor_desconto' => 'nullable|numeric|min:0',
            'percentual_acrescimo' => 'nullable|numeric|min:0|max:100',
            'valor_acrescimo' => 'nullable|numeric|min:0',
            'observacoes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return responseError([
                'message' => 'Dados inválidos',
                'data' => $validator->errors(),
                'statusCode' => 422
            ]);
        }

        try {
            $dados = [
                'clinica_id' => $clinicaId,
                'paciente_id' => $request->paciente_id,
                'dentista_id' => $request->dentista_id ?: null,
                'descricao' => $request->descricao,
                'valor_nominal' => (float) $request->valor_nominal,
                'data_vencimento' => $request->data_vencimento,
                'parcelas_total' => (int) ($request->get('parcelas_total') ?: 1),
                'percentual_desconto' => (float) ($request->get('percentual_desconto') ?: 0),
                'valor_desconto' => (float) ($request->get('valor_desconto') ?: 0),
                'percentual_acrescimo' => (float) ($request->get('percentual_acrescimo') ?: 0),
                'valor_acrescimo' => (float) ($request->get('valor_acrescimo') ?: 0),
                'observacoes' => $request->observacoes ?: null,
                'data_emissao' => now(),
                'lancado_por' => $user['id'],
                'status' => FinanceiroReceber::STATUS_PENDENTE,
            ];

            $numeroParcelas = $request->get('parcelas_total', 1);
            
            if ($numeroParcelas > 1) {
                $faturas = FinanceiroReceber::criarComParcelamento($dados, $numeroParcelas);
                return responseSuccess([
                    'message' => "Fatura criada com sucesso em {$numeroParcelas} parcelas",
                    'data' => $faturas
                ]);
            } else {
                $fatura = FinanceiroReceber::create($dados);
                $fatura->valor_final = $fatura->calcularValorFinal();
                $fatura->save();
                
                return responseSuccess([
                    'message' => 'Fatura criada com sucesso',
                    'data' => $fatura->load(['paciente', 'dentista'])
                ]);
            }
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao criar fatura: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $fatura = FinanceiroReceber::with(['paciente', 'dentista', 'parcelas'])
            ->daClinica($clinicaId)
            ->findOrFail($id);

        return responseSuccess(['data' => $fatura]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $fatura = FinanceiroReceber::daClinica($clinicaId)->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'descricao' => 'sometimes|string|max:255',
            'valor_nominal' => 'sometimes|numeric|min:0.01',
            'data_vencimento' => 'sometimes|date',
            'percentual_desconto' => 'nullable|numeric|min:0|max:100',
            'valor_desconto' => 'nullable|numeric|min:0',
            'percentual_acrescimo' => 'nullable|numeric|min:0|max:100',
            'valor_acrescimo' => 'nullable|numeric|min:0',
            'observacoes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return responseError([
                'message' => 'Dados inválidos',
                'data' => $validator->errors(),
                'statusCode' => 422
            ]);
        }

        try {
            $fatura->update($request->all());
            $fatura->valor_final = $fatura->calcularValorFinal();
            $fatura->save();

            return responseSuccess([
                'message' => 'Fatura atualizada com sucesso',
                'data' => $fatura->load(['paciente', 'dentista'])
            ]);
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao atualizar fatura: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $fatura = FinanceiroReceber::daClinica($clinicaId)->findOrFail($id);

        try {
            // Se for fatura principal, cancelar todas as parcelas
            if ($fatura->fatura_principal_id === null && $fatura->parcelas_total > 1) {
                $fatura->parcelas()->update(['status' => FinanceiroReceber::STATUS_CANCELADO]);
            }
            
            $fatura->cancelar();

            return responseSuccess(['message' => 'Fatura cancelada com sucesso']);
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao cancelar fatura: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Marcar fatura como paga
     */
    public function marcarComoPago(Request $request, string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $validator = Validator::make($request->all(), [
            'data_pagamento' => 'nullable|date',
            'meio_pagamento' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return responseError([
                'message' => 'Dados inválidos',
                'data' => $validator->errors(),
                'statusCode' => 422
            ]);
        }

        $fatura = FinanceiroReceber::daClinica($clinicaId)->findOrFail($id);

        try {
            $fatura->marcarComoPago(
                $request->get('data_pagamento'),
                $request->get('meio_pagamento')
            );

            return responseSuccess([
                'message' => 'Fatura marcada como paga',
                'data' => $fatura->fresh(['paciente', 'dentista'])
            ]);
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao marcar fatura como paga: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Obter faturas de um paciente específico
     */
    public function faturasPaciente(string $pacienteId)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $faturas = FinanceiroReceber::with(['dentista'])
            ->daClinica($clinicaId)
            ->doPaciente($pacienteId)
            ->orderBy('data_vencimento', 'asc')
            ->orderBy('parcela_numero', 'asc')
            ->get();

        return responseSuccess(['data' => $faturas]);
    }

    /**
     * Obter estatísticas financeiras
     */
    public function estatisticas(Request $request)
    {
        try {
            $user = auth()->payload();
            $clinicaId = $user['clinica']['id'];

            $query = FinanceiroReceber::daClinica($clinicaId);

            if ($request->has('paciente_id')) {
                $query->doPaciente($request->paciente_id);
            }

            $totalPendente = $query->clone()->pendentes()->sum('valor_final') ?: 0;
            $totalVencido = $query->clone()->vencidas()->sum('valor_final') ?: 0;
            $totalPago = $query->clone()->pagas()->sum('valor_final') ?: 0;
            $totalGeral = $query->clone()->sum('valor_final') ?: 0;

            $estatisticas = [
                'total_pendente' => $totalPendente,
                'total_vencido' => $totalVencido,
                'total_pago' => $totalPago,
                'total_geral' => $totalGeral,
                'quantidade_pendente' => $query->clone()->pendentes()->count(),
                'quantidade_vencida' => $query->clone()->vencidas()->count(),
                'quantidade_paga' => $query->clone()->pagas()->count(),
            ];

            return responseSuccess(['data' => $estatisticas]);
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao obter estatísticas: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }
}
