<template>
  <div class="image-gallery-selector">
    <!-- Head<PERSON> <PERSON> -->
    <div class="gallery-header">
      <div class="header-content">
        <div class="header-title">
          <i class="fas fa-images me-2"></i>
          <span>Selecionar Imagens</span>
        </div>
        <div class="header-subtitle">
          Escolha as imagens que deseja referenciar na conversa
        </div>
      </div>
      <div class="header-actions">
        <button 
          class="btn btn-outline-secondary btn-sm me-2"
          @click="$emit('cancel')"
        >
          <i class="fas fa-times me-1"></i>
          Cancelar
        </button>
        <button 
          class="btn btn-primary btn-sm"
          @click="proceedToObservations"
          :disabled="selectedImages.length === 0"
        >
          <i class="fas fa-arrow-right me-1"></i>
          Continuar ({{ selectedImages.length }})
        </button>
      </div>
    </div>

    <!-- <PERSON><PERSON><PERSON>ção -->
    <div v-if="selectedImages.length > 0" class="selection-counter">
      <div class="counter-content">
        <i class="fas fa-check-circle me-2"></i>
        {{ selectedImages.length }} imagem{{ selectedImages.length > 1 ? 'ns' : '' }} selecionada{{ selectedImages.length > 1 ? 's' : '' }}
        <button 
          class="btn btn-link btn-sm p-0 ms-2"
          @click="clearSelection"
        >
          <i class="fas fa-times"></i>
          Limpar seleção
        </button>
      </div>
    </div>

    <!-- Container da Galeria -->
    <div class="gallery-container">
      <!-- Loading state -->
      <div v-if="loading" class="loading-message">
        <div class="message-content">
          <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Carregando...</span>
          </div>
          <p class="main-text mb-0">
            Carregando imagens do paciente...
          </p>
        </div>
      </div>

      <!-- Mensagem quando não há imagens -->
      <div v-else-if="groupedImagesByDate.length === 0" class="no-images-message">
        <div class="message-content">
          <i class="fas fa-images fa-2x mb-3"></i>
          <p class="main-text mb-0">
            Este paciente ainda não possui imagens cadastradas.
          </p>
        </div>
      </div>

      <!-- Grupos de imagens por data -->
      <div v-else v-for="group in groupedImagesByDate" :key="group.date" class="date-group">
        <div class="date-group-header">
          <div class="date-group-header-content">
            <span class="date-text">{{ $filters.dateLong(group.date) }}</span>
          </div>
          <div class="date-group-actions">
            <span class="date-how-much">{{ $filters.howMuchTime(group.date, { type: 'date' }) }}</span>
            <button 
              class="btn btn-outline-primary btn-sm ms-2"
              @click="toggleDateGroup(group)"
              :title="isDateGroupSelected(group) ? 'Desmarcar todas desta data' : 'Selecionar todas desta data'"
            >
              <i :class="isDateGroupSelected(group) ? 'fas fa-check-square' : 'far fa-square'"></i>
            </button>
          </div>
        </div>
        
        <div class="images-container">
          <div 
            v-for="image in group.images" 
            :key="image.id"
            class="image-item"
            :class="{ 'selected': isImageSelected(image) }"
            @click="toggleImageSelection(image)"
          >
            <div class="image-wrapper">
              <img :src="image.url" :alt="getImageDescription(image)" />
              <div class="image-overlay">
                <div class="selection-checkbox">
                  <i :class="isImageSelected(image) ? 'fas fa-check-circle' : 'far fa-circle'"></i>
                </div>
              </div>
            </div>
            <div class="image-info">
              <div class="image-description">
                {{ getImageDescription(image) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getImageDescription } from "@/helpers/utils";

export default {
  name: 'ImageGallerySelector',
  props: {
    imagens: {
      type: Array,
      required: true,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['cancel', 'proceed'],
  data() {
    return {
      selectedImages: []
    };
  },
  computed: {
    safePatientImages() {
      const images = Array.isArray(this.imagens) ? this.imagens : [];
      // Filtra apenas imagens regulares (não diagnósticas)
      return images.filter(img => img.is_diagnostico !== true && img.is_diagnostico !== 1);
    },
    groupedImagesByDate() {
      const groups = {};
      this.safePatientImages.forEach((img) => {
        const date = img.data || "Sem data";
        if (!groups[date]) {
          groups[date] = [];
        }
        groups[date].push(img);
      });
      // Convert to array of { date, images } sorted by date descending
      return Object.keys(groups)
        .map((date) => ({ date, images: groups[date] }))
        .sort((a, b) => (a.date < b.date ? 1 : -1));
    }
  },
  methods: {
    getImageDescription,
    
    isImageSelected(image) {
      return this.selectedImages.some(selected => selected.id === image.id);
    },
    
    toggleImageSelection(image) {
      const index = this.selectedImages.findIndex(selected => selected.id === image.id);
      if (index > -1) {
        this.selectedImages.splice(index, 1);
      } else {
        this.selectedImages.push(image);
      }
    },
    
    isDateGroupSelected(group) {
      return group.images.every(image => this.isImageSelected(image));
    },
    
    toggleDateGroup(group) {
      const allSelected = this.isDateGroupSelected(group);
      
      if (allSelected) {
        // Remove todas as imagens deste grupo
        group.images.forEach(image => {
          const index = this.selectedImages.findIndex(selected => selected.id === image.id);
          if (index > -1) {
            this.selectedImages.splice(index, 1);
          }
        });
      } else {
        // Adiciona todas as imagens deste grupo que não estão selecionadas
        group.images.forEach(image => {
          if (!this.isImageSelected(image)) {
            this.selectedImages.push(image);
          }
        });
      }
    },
    
    clearSelection() {
      this.selectedImages = [];
    },
    
    proceedToObservations() {
      if (this.selectedImages.length > 0) {
        this.$emit('proceed', this.selectedImages);
      }
    }
  }
};
</script>

<style scoped>
.image-gallery-selector {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.gallery-header {
  background: white;
  padding: 1.5rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.header-content {
  flex: 1;
}

.header-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #344767;
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
}

.header-subtitle {
  color: #6c757d;
  font-size: 0.9rem;
}

.header-actions {
  display: flex;
  align-items: center;
}

.selection-counter {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  padding: 0.75rem 1.5rem;
  border-bottom: 1px solid #dee2e6;
  animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.counter-content {
  display: flex;
  align-items: center;
  color: #1976d2;
  font-weight: 500;
}

.gallery-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.no-images-message,
.loading-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #6c757d;
}

.message-content {
  max-width: 400px;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.main-text {
  font-size: 1.1rem;
  line-height: 1.5;
}

.date-group {
  margin-bottom: 1.5rem;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  background: white;
}

.date-group-header {
  background: #e9ecef;
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #dee2e6;
}

.date-text {
  font-weight: 500;
  color: #495057;
}

.date-group-actions {
  display: flex;
  align-items: center;
}

.date-how-much {
  font-size: 0.85rem;
  color: #6c757d;
  background: rgba(13, 110, 253, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.images-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 1rem;
  padding: 1rem;
}

.image-item {
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.image-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.image-item.selected {
  border: 2px solid #0d6efd;
  box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
}

.image-wrapper {
  position: relative;
  aspect-ratio: 4/3;
  overflow: hidden;
}

.image-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.image-item:hover .image-wrapper img {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.3);
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 0.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-item:hover .image-overlay,
.image-item.selected .image-overlay {
  opacity: 1;
}

.selection-checkbox {
  background: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.selection-checkbox i {
  font-size: 0.9rem;
  color: #0d6efd;
}

.image-info {
  padding: 0.75rem;
}

.image-description {
  font-size: 0.85rem;
  color: #495057;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Responsividade */
@media (max-width: 768px) {
  .gallery-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .images-container {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.75rem;
    padding: 0.75rem;
  }
}
</style>
