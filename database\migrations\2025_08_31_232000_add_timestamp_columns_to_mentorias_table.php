<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('mentorias', function (Blueprint $table) {
            $table->timestamp('iniciada_em')->nullable()->after('status');
            $table->timestamp('finalizada_em')->nullable()->after('iniciada_em');
            $table->timestamp('cancelada_em')->nullable()->after('finalizada_em');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('mentorias', function (Blueprint $table) {
            $table->dropColumn(['iniciada_em', 'finalizada_em', 'cancelada_em']);
        });
    }
};
