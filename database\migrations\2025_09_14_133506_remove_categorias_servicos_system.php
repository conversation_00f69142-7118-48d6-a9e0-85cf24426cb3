<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remover foreign key e coluna categoria_id da tabela servicos_produtos
        Schema::table('servicos_produtos', function (Blueprint $table) {
            $table->dropForeign(['categoria_id']);
            $table->dropColumn('categoria_id');
        });

        // Remover tabela categorias_servicos
        Schema::dropIfExists('categorias_servicos');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recriar tabela categorias_servicos
        Schema::create('categorias_servicos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('clinica_id');
            $table->string('nome');
            $table->text('descricao')->nullable();
            $table->string('cor', 7)->default('#007bff');
            $table->boolean('ativo')->default(true);
            $table->timestamps();

            $table->foreign('clinica_id')->references('id')->on('clinicas')->onDelete('cascade');
            $table->index(['clinica_id', 'ativo']);
        });

        // Adicionar coluna categoria_id de volta na tabela servicos_produtos
        Schema::table('servicos_produtos', function (Blueprint $table) {
            $table->unsignedBigInteger('categoria_id')->nullable()->after('clinica_id');
            $table->foreign('categoria_id')->references('id')->on('categorias_servicos')->onDelete('set null');
        });
    }
};
