/**
 * Serviço de cache de imagens
 * Pré-carrega e mantém imagens em cache usando blob URLs
 */

class ImageCacheService {
    constructor() {
        this.cache = new Map();
        this.loadingPromises = new Map();
        this.isInitialized = false;
    }

    /**
     * Inicializa o serviço com delay para não concorrer com o carregamento do app
     */
    async initialize(delayMs = 2000) {
        if (this.isInitialized) {
            return;
        }

        // Aguardar o delay especificado
        await new Promise(resolve => setTimeout(resolve, delayMs));
        
        console.log('Inicializando cache de imagens...');
        
        // Pré-carregar imagens das bandeiras
        await this.preloadFlagImages();
        
        this.isInitialized = true;
        console.log('Cache de imagens inicializado');
    }

    /**
     * Carrega uma imagem e armazena em cache
     */
    async loadImageToCache(key, importFunction) {
        // Se já está em cache, retornar
        if (this.cache.has(key)) {
            return this.cache.get(key);
        }

        // Se já está sendo carregada, aguardar a promise existente
        if (this.loadingPromises.has(key)) {
            return await this.loadingPromises.get(key);
        }

        // Criar nova promise de carregamento
        const loadingPromise = this._loadImage(key, importFunction);
        this.loadingPromises.set(key, loadingPromise);

        try {
            const cachedUrl = await loadingPromise;
            this.cache.set(key, cachedUrl);
            return cachedUrl;
        } catch (error) {
            console.warn(`Erro ao carregar imagem ${key}:`, error);
            return null;
        } finally {
            this.loadingPromises.delete(key);
        }
    }

    /**
     * Carrega uma imagem específica
     */
    async _loadImage(key, importFunction) {
        try {
            // Importar a imagem dinamicamente
            const imageUrl = (await importFunction()).default;
            
            // Fazer fetch da imagem
            const response = await fetch(imageUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const blob = await response.blob();
            
            // Criar URL do blob para cache
            const blobUrl = URL.createObjectURL(blob);
            console.log(`Imagem ${key} carregada em cache`);
            
            return blobUrl;
        } catch (error) {
            console.warn(`Erro ao carregar imagem ${key}:`, error);
            throw error;
        }
    }

    /**
     * Pré-carrega as imagens das bandeiras
     */
    async preloadFlagImages() {
        const flags = [
            {
                key: 'flag-pt',
                importFunction: () => import('@/assets/img/flags/pt.png')
            },
            {
                key: 'flag-en',
                importFunction: () => import('@/assets/img/flags/en.png')
            },
            {
                key: 'flag-es',
                importFunction: () => import('@/assets/img/flags/es.png')
            }
        ];

        // Carregar as imagens uma por vez com pequeno delay entre elas
        for (const flag of flags) {
            try {
                await this.loadImageToCache(flag.key, flag.importFunction);
                
                // Pequeno delay entre carregamentos
                await new Promise(resolve => setTimeout(resolve, 100));
            } catch (error) {
                console.warn(`Erro ao pré-carregar bandeira ${flag.key}:`, error);
            }
        }
    }

    /**
     * Obtém uma imagem do cache
     */
    getImage(key) {
        return this.cache.get(key) || null;
    }

    /**
     * Obtém uma imagem de bandeira específica
     */
    getFlagImage(countryCode) {
        return this.getImage(`flag-${countryCode}`);
    }

    /**
     * Verifica se uma imagem está em cache
     */
    hasImage(key) {
        return this.cache.has(key);
    }

    /**
     * Remove uma imagem do cache
     */
    removeImage(key) {
        const url = this.cache.get(key);
        if (url && url.startsWith('blob:')) {
            URL.revokeObjectURL(url);
        }
        this.cache.delete(key);
    }

    /**
     * Limpa todo o cache
     */
    clearCache() {
        for (const [key, url] of this.cache.entries()) {
            if (url && url.startsWith('blob:')) {
                URL.revokeObjectURL(url);
            }
        }
        this.cache.clear();
        console.log('Cache de imagens limpo');
    }

    /**
     * Obtém estatísticas do cache
     */
    getStats() {
        return {
            totalImages: this.cache.size,
            isInitialized: this.isInitialized,
            loadingCount: this.loadingPromises.size
        };
    }
}

// Criar instância singleton
const imageCacheService = new ImageCacheService();

export default imageCacheService;
