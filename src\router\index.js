import { createRouter, createWeb<PERSON><PERSON><PERSON> } from "vue-router";

import Entrar from "../views/Entrar.vue";
import Agenda from "../views/Agenda.vue";
import Pacientes from "../views/Pacientes.vue";
import Planejamento from "../views/Planejamento.vue";
import Dentistas from "../views/Dentistas.vue";
import Financeiro from "../views/Financeiro.vue";
import Mentorias from "../views/Mentorias.vue";
import WelcomeForm from "@/views/WelcomeForm.vue";
import Configuracoes from "@/views/Configuracoes.vue";

import Paciente from "../views/Paciente.vue";

import Dentista from "../views/Dentista.vue";

import usuariosService from "@/services/usuariosService";

const routes = [
  {
    path: "/",
    name: "/",
    redirect: "/agenda",
    meta: {
      requiresAuth: true
    }
  },
  {
    path: "/entrar",
    name: "<PERSON>trar",
    component: Entrar,
  },
  {
    path: "/agenda",
    name: "Agenda",
    component: Agenda,
    meta: {
      requiresAuth: true
    }
  },
  {
    path: "/pacientes",
    name: "<PERSON>ient<PERSON>",
    component: Pacientes,
    meta: {
      requiresAuth: true
    }
  },
  {
    path: "/planejamento/:id",
    name: "Planejamento",
    component: Planejamento,
    meta: {
      requiresAuth: true
    }
  },
  {
    path: "/ortodontistas",
    name: "Ortodontistas",
    component: Dentistas,
    meta: {
      requiresAuth: true
    }
  },
  {
    path: "/financeiro",
    name: "Financeiro",
    component: Financeiro,
    meta: {
      requiresAuth: true
    }
  },
  {
    path: "/mentorias",
    name: "Mentorias",
    component: Mentorias,
    meta: {
      requiresAuth: true
    }
  },
  {
    path: "/paciente",
    name: "/paciente",
    redirect: "/pacientes",
    meta: {
      requiresAuth: true
    }
  },
  {
    path: "/paciente/:id_ficha",
    name: "PacientePadrao",
    component: Paciente,
    meta: {
      requiresAuth: true
    }
  },
  {
    path: "/:clinica_slug/paciente/:id_ficha",
    name: "PacienteClinica",
    component: Paciente,
    meta: {
      requiresAuth: true
    }
  },
  {
    path: "/ortodontista/:id_matricula",
    name: "DentistaPadrao",
    component: Dentista,
    meta: {
      requiresAuth: true
    }
  },
  {
    path: "/:clinica_slug/ortodontista/:id_matricula",
    name: "DentistaClinica",
    component: Dentista,
    meta: {
      requiresAuth: true
    }
  },
  {
    path: "/configuracoes",
    name: "Configuracoes",
    component: Configuracoes,
    meta: {
      requiresAuth: true
    }
  },
  {
    path: "/bem-vindo",
    name: "WelcomeForm",
    component: WelcomeForm,
    meta: {
      requiresAuth: false
    }
  },
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    redirect: "/agenda",
    meta: {
      requiresAuth: true
    }
  },
];

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  // history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  linkActiveClass: "active",
});

router.beforeEach(async (to, from, next) => {
  // Se a rota requer autenticação
  if (to.meta.requiresAuth) {
    const isAuthenticated = usuariosService.isAuthenticated();

    if (!isAuthenticated) {
      // Não autenticado, redirecionar para login
      next("/entrar");
      return;
    }

    // Verificar se o token é válido (apenas se já está autenticado)
    try {
      const refreshResult = await usuariosService.refreshAuth();
      if (refreshResult === false) {
        // Token inválido ou expirado, redirecionar para login
        next("/entrar");
        return;
      }
    } catch (error) {
      console.error('Erro na verificação de autenticação:', error);
      next("/entrar");
      return;
    }
  }

  // Se está indo para a página de login mas já está autenticado
  if (to.name === 'Entrar' && usuariosService.isAuthenticated()) {
    next("/agenda");
    return;
  }

  next();
});

export default router;
