/* ===== LUMI GLOBAL LAYOUT ENHANCEMENTS ===== */

/* Global app layout structure */
.lumi-app-layout {
    min-height: calc(100vh - var(--tab-navigation-height, 0px));
    display: grid;
    grid-template-rows: 1fr auto;
    background: var(--primary-background, #fbfdfe);
}

/* Adjust layout when tab navigation is present */
.user-access:has(.bg-gradient-primary) .lumi-app-layout {
    min-height: calc(100vh - 80px); /* Account for tab navigation */
}

/* Fallback for browsers that don't support :has() */
@supports not selector(:has(*)) {
    .user-access .lumi-app-layout {
        min-height: calc(100vh - 80px);
    }
}

.lumi-main-container {
    display: flex;
    flex-direction: column;
    background: #ffffff;
    position: relative;
    overflow-x: hidden;
    max-width: 100vw;
    margin: 0 auto;
    width: 100%;
    min-height: 0; /* Allow grid to control height */
}

.lumi-content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    min-height: 0; /* Allow content to shrink if needed */
}

/* Ensure all pages have proper content wrapper */
.main-page-content {
    /* Override any existing styles to ensure proper layout */
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    padding: 1.5rem !important;
    min-height: auto !important; /* Let the parent container handle min-height */
}

/* Special handling for pages that need different padding */
.main-page-content.calendar-container {
    padding: 1rem !important;
}

/* Ensure content fills available space */
.main-page-content > .row {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.main-page-content > .row > .col-12 {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Table containers should expand to fill space */
.main-page-content .table-responsive,
.main-page-content .vue3-easy-data-table__main {
    flex: 1;
}

/* Search input styling for better integration */
.main-page-content .search-input {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.main-page-content .search-input:focus {
    background: rgba(255, 255, 255, 0.95);
    border-color: #6ca5db;
    box-shadow: 0 0 0 3px rgba(108, 165, 219, 0.1);
}

/* Card styling improvements for the new layout */
.main-page-content .card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Modal improvements */
.modal-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Loading states */
.main-page-content .spinner-border {
    color: #5988A8;
}

/* Empty state styling */
.main-page-content .bg-gradient-light {
    background: rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Legacy responsive styles - consolidated above */

/* Ensure sidenav works well with new layout */
.lumi-sidenav {
    background: var(--sidenav-background) !important;
    border-left: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.05);
}

/* Tab navigation integration */
.v-tabs {
    background: linear-gradient(135deg, #1B4464 0%, #56809F 100%) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Footer enhancements */
.lumi-footer {
    grid-row: 2;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05) !important;
    padding: 1rem 0 !important;
    position: relative;
    z-index: 10;
}

.lumi-footer .footer {
    background: transparent !important;
    border-top: none !important;
    padding: 0.5rem 0 !important;
}

.lumi-footer .copyright {
    color: rgba(0, 0, 0, 0.7) !important;
    font-size: 0.875rem;
    margin: 0 !important;
    font-weight: 500;
}

/* Fab search positioning */
.fab-search {
    position: fixed !important;
    bottom: 2rem !important;
    right: 2rem !important;
    z-index: 1040 !important;
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Ensure proper scrolling behavior */
.main-content {
    scroll-behavior: smooth;
}

/* Fix for any potential overflow issues */
.lumi-page-container {
    overflow-x: hidden;
}

/* Special handling for pages with different layout needs */
.page-width-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: linear-gradient(to right, #FFF, #EEE, #FFF);
}

.page-width {
    flex: 1;
    display: flex;
    flex-direction: column;
    -webkit-box-shadow: 1px 1px 25px 0px rgba(138, 138, 138, 0.2);
    -moz-box-shadow: 1px 1px 25px 0px rgba(138, 138, 138, 0.2);
    box-shadow: 1px 1px 25px 0px rgba(138, 138, 138, 0.2);
    background: #FFF;
    max-width: 1300px;
    margin: 0 auto;
    border-left: 2px solid rgba(222, 222, 222, 0.6);
    border-right: 2px solid rgba(222, 222, 222, 0.6);
}

/* Ensure calendar and other special pages work properly */
.main-page-content.calendar-container {
    min-height: auto !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .lumi-app-layout {
        min-height: calc(100vh - 60px); /* Smaller tab navigation on mobile */
    }

    .user-access:has(.bg-gradient-primary) .lumi-app-layout {
        min-height: calc(100vh - 60px);
    }

    @supports not selector(:has(*)) {
        .user-access .lumi-app-layout {
            min-height: calc(100vh - 60px);
        }
    }

    .main-page-content {
        padding: 1rem !important;
    }
}

@media (max-width: 576px) {
    .main-page-content {
        padding: 0.75rem !important;
    }

    .lumi-footer {
        padding: 0.75rem 0 !important;
    }
}
