# Sistema Global de Drafts

## Visão Geral

O Sistema Global de Drafts é uma solução elegante e funcional que permite aos usuários gerenciar múltiplos rascunhos de pacientes simultaneamente, evitando perda de informações durante a navegação.

## Características Principais

### 1. FABs Empilhados
- **Posicionamento**: FABs são empilhados verticalmente no canto inferior direito
- **Ordenação**: Mais recente primeiro (no topo da pilha)
- **Animação**: Entrada suave com efeito slide-in
- **Hover**: Efeitos visuais elegantes com escala e sombra

### 2. Persistência Inteligente
- **localStorage**: Rascunhos persistem entre sessões
- **Sincronização**: Sistema sincroniza com caches individuais
- **Limpeza**: Remove automaticamente drafts órfãos

### 3. Interface Intuitiva
- **Badge**: Mostra número de alterações
- **Tooltip**: Informações detalhadas e ações
- **Responsivo**: Adapta-se a diferentes tamanhos de tela

## Componentes

### GlobalDraftsFab.vue
Componente visual que renderiza os FABs empilhados.

**Props:**
- `drafts`: Array de objetos draft

**Events:**
- `toggle-tooltip`: Alternar tooltip
- `close-tooltip`: Fechar tooltip
- `review-and-save`: Revisar e salvar alterações
- `open-patient`: Abrir paciente para edição

### GlobalDraftsManager.vue
Componente gerenciador que coordena FABs e modal de revisão.

**Funcionalidades:**
- Modal de revisão de alterações
- Integração com API de salvamento
- Gerenciamento de estado dos tooltips

### useGlobalDrafts.js
Composable que gerencia o estado global dos drafts.

**Métodos principais:**
- `addOrUpdateDraft()`: Adicionar/atualizar draft
- `removeDraft()`: Remover draft
- `toggleTooltip()`: Alternar tooltip
- `openPatient()`: Navegar para paciente

## Fluxo de Funcionamento

### 1. Criação de Draft
```javascript
// Quando usuário faz alterações e navega
this.globalDrafts.addOrUpdateDraft({
  patientId: this.paciente.id,
  patientName: this.paciente.nome,
  changesCount: this.changedFields.size,
  lastModified: Date.now(),
  cacheKey: this.patientCacheKey
});
```

### 2. Exibição de FABs
- FABs aparecem automaticamente quando há drafts
- Empilhamento visual com efeitos de profundidade
- Badge mostra número de alterações

### 3. Interação do Usuário
**Opção 1: Verificar e Salvar**
- Abre modal com preview das alterações
- Permite salvamento direto no banco
- Remove draft após salvamento

**Opção 2: Abrir Paciente**
- Navega para a página do paciente
- Restaura estado de edição
- Mantém draft ativo

### 4. Limpeza Automática
- Remove drafts após salvamento
- Limpa caches órfãos na inicialização
- Sincroniza com localStorage

## Integração com Paciente.vue

### Modificações Realizadas
1. **Router Guard Simplificado**: Não bloqueia mais navegação
2. **FAB Local Removido**: Substituído pelo sistema global
3. **Auto-save Global**: Integra com sistema de drafts
4. **Limpeza de Cache**: Remove drafts ao salvar/descartar

### Código de Integração
```javascript
// Adicionar ao data()
globalDrafts: getGlobalDraftsInstance(),

// Modificar saveToLocalCache()
this.globalDrafts.addOrUpdateDraft({
  patientId: this.paciente.id,
  patientName: this.paciente.nome,
  changesCount: this.changedFields.size,
  lastModified: Date.now(),
  cacheKey: this.patientCacheKey
});

// Modificar clearLocalCache()
this.globalDrafts.removeDraft(this.paciente.id);
```

## Vantagens do Sistema

### 1. Experiência do Usuário
- **Não Intrusivo**: Não bloqueia navegação
- **Visualmente Claro**: FABs mostram estado dos drafts
- **Funcional**: Permite gerenciar múltiplos pacientes

### 2. Funcionalidade
- **Multi-paciente**: Suporta drafts de vários pacientes
- **Persistente**: Mantém dados entre sessões
- **Inteligente**: Limpeza automática de dados órfãos

### 3. Manutenibilidade
- **Modular**: Componentes separados e reutilizáveis
- **Testável**: Lógica isolada em composables
- **Extensível**: Fácil adicionar novas funcionalidades

## Estrutura de Dados

### Draft Object
```javascript
{
  patientId: "123",
  patientName: "João Silva",
  changesCount: 3,
  lastModified: 1691234567890,
  showTooltip: false,
  cacheKey: "patient_123",
  routePath: "/paciente/123"
}
```

### localStorage Structure
```javascript
// Índice global
"globalDrafts": {
  "patient_123": { /* draft object */ },
  "patient_456": { /* draft object */ }
}

// Caches individuais (mantidos)
"patient_123": { /* cache data */ }
"patient_456": { /* cache data */ }
```

## Responsividade

### Desktop
- FABs com 280px de largura
- Tooltips com 320px de largura
- Botões lado a lado

### Mobile
- FABs com 260px de largura
- Tooltips com 280px de largura
- Botões empilhados verticalmente

## Animações e Efeitos

### Entrada de FABs
- `slideInRight`: Animação de entrada suave
- `cubic-bezier(0.4, 0, 0.2, 1)`: Curva de animação elegante

### Hover Effects
- Escala: `scale(1.02)`
- Sombra: Intensificação da sombra
- Translação: Movimento para a esquerda

### Empilhamento
- FABs secundários: `scale(0.95)` e `opacity(0.9)`
- Hover em empilhados: Volta ao tamanho normal

## Considerações de Performance

### Otimizações
- **Debounce**: Auto-save com debounce
- **Lazy Loading**: Tooltips só carregam quando necessário
- **Memory Management**: Limpeza automática de dados órfãos

### Monitoramento
- Console logs para debug (removíveis em produção)
- Tratamento de erros robusto
- Fallbacks para casos de erro

## Próximos Passos

### Melhorias Futuras
1. **Notificações Push**: Alertar sobre drafts antigos
2. **Sincronização**: Sync entre abas/dispositivos
3. **Backup**: Backup automático de drafts importantes
4. **Analytics**: Métricas de uso do sistema

### Configurações
1. **Tempo de Expiração**: Configurar TTL dos drafts
2. **Limite de Drafts**: Máximo de drafts simultâneos
3. **Temas**: Personalização visual dos FABs
