import axios from '@/services/axios'
import moment from 'moment'

export async function uploadImage(options) {
    if (!options.paciente_id)
        return false;

    options = {
        dir: 'misc',
        data: moment().format('YYYY-MM-DD HH:mm:ss'),
        descricao: '',
        is_diagnostico: false,
        tag_diagnostico: '',
        ...options,
    }

    try {
        let data = new FormData();
        console.log('options.imagem:', options.imagem)
        data.append('paciente_id', options.paciente_id);
        data.append('imagem', options.imagem);
        data.append('dir', options.dir);
        data.append('data', options.data);
        data.append('descricao', options.descricao);

        // Adiciona os novos campos para imagens de diagnóstico
        data.append('is_diagnostico', options.is_diagnostico);
        data.append('tag_diagnostico', options.tag_diagnostico);

        const response = await axios.post('/imagem', data,
            {
                headers: { 'Content-Type': 'multipart/form-data' }
            })

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response;

    } catch (error) {
        console.error('Erro ao enviar imagem:', error);
    }

    return false;
}

export async function excluirImagem(id) {
    try {
        // Usando o padrão apiResource para excluir uma imagem específica
        const response = await axios.delete(`/imagem/${id}`);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return true;

    } catch (error) {
        console.error('Erro ao excluir imagem:', error);
        return false;
    }
}

export async function excluirImagensPorData(pacienteId, data) {
    try {
        // Endpoint para excluir todas as imagens de uma data específica
        const response = await axios.delete(`/imagem/por-data`, {
            data: {
                paciente_id: pacienteId,
                data: data
            }
        });

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return true;

    } catch (error) {
        console.error('Erro ao excluir imagens por data:', error);
        return false;
    }
}

export async function getImagensPorPaciente(pacienteId) {
    try {
        const response = await axios.get(`/paciente/${pacienteId}/imagens`);

        if (!response || !response.data)
            return [];

        return response.data;

    } catch (error) {
        console.error('Erro ao buscar imagens do paciente:', error);
        return [];
    }
}