# LumiSidenav Component

Este componente permite criar sidebars configuráveis para o sistema Lumi, com suporte a grupos de botões, ícones e ações personalizadas.

## Uso Básico

```vue
<template>
  <lumi-sidenav
    icon="mdi-calendar-month"
    class="fixed-end lumi-sidenav"
    :config="sidenavConfig"
    @action="handleAction">
  </lumi-sidenav>
</template>

<script>
import LumiSidenav from "@/views/components/LumiSidenav/index.vue";

export default {
  components: {
    LumiSidenav
  },
  data() {
    return {
      sidenavConfig: {
        groups: [
          {
            title: "AGENDA",
            buttons: [
              {
                text: "Nova consulta",
                icon: "add",
                iconType: "material",
                action: "newAppointment"
              },
              // Mais botões...
            ]
          },
          // Mais grupos...
        ]
      }
    };
  },
  methods: {
    handleAction(action, button) {
      console.log(`Action: ${action}`, button);

      // Lógica para lidar com as ações
      switch (action) {
        case 'newAppointment':
          // Lógica para criar nova consulta
          break;
        // Mais casos...
      }
    }
  }
};
</script>
```

## Configuração

O componente aceita um objeto de configuração com a seguinte estrutura:

```javascript
{
  groups: [
    {
      title: "TÍTULO DO GRUPO", // Título do grupo (opcional)
      buttons: [
        {
          text: "Texto do botão", // Texto do botão
          icon: "add", // Nome do ícone
          iconType: "material", // Tipo de ícone: "material", "font-awesome", "vuetify"
          action: "acao", // Identificador da ação (opcional)
          actionData: {}, // Dados adicionais para a ação (opcional)
          route: "/caminho", // Caminho do Vue Router (opcional)
          attributes: { // Atributos HTML (opcional)
            "data-bs-toggle": "modal",
            "data-bs-target": "#modalId"
          },
          active: false, // Se o botão está ativo (opcional)
          autoCollapse: true, // Se deve recolher o sidenav após a ação (padrão: true, exceto para ações de confirmação)
          class: "", // Classes CSS adicionais para o elemento li (opcional)
          iconClass: "", // Classes CSS adicionais para o container do ícone (opcional)
          textClass: "" // Classes CSS adicionais para o texto (opcional)
        }
      ]
    }
  ]
}
```

## Auto-Collapse (Recolhimento Automático)

O componente possui funcionalidade de recolhimento automático do sidenav após executar ações. Por padrão:

- **Ações normais**: O sidenav é recolhido automaticamente após a execução
- **Ações de confirmação**: O sidenav NÃO é recolhido automaticamente (ex: logout, delete, confirm)
- **Controle manual**: Use a propriedade `autoCollapse: false` para desabilitar o recolhimento automático

### Exemplos:

```javascript
// Ação normal - recolhe automaticamente
{
  text: "Nova consulta",
  action: "newAppointment",
  // autoCollapse: true (padrão)
}

// Ação de confirmação - NÃO recolhe automaticamente
{
  text: "Sair",
  action: "logout", // Detectado automaticamente como ação de confirmação
}

// Desabilitar recolhimento manualmente
{
  text: "Ação especial",
  action: "specialAction",
  autoCollapse: false // Não recolhe o sidenav
}
```

### Ações de Confirmação Detectadas Automaticamente:
- Ações que contêm: `logout`, `delete`, `remove`, `confirm`

## Tipos de Ícones

O componente suporta três tipos de ícones:

### Material Icons

```javascript
{
  icon: "add", // Nome do ícone Material
  iconType: "material"
}
```

### Font Awesome

```javascript
{
  icon: ["fas", "user"], // Array com o prefixo e o nome do ícone
  iconType: "font-awesome"
}
```

### Vuetify Icons

```javascript
{
  icon: "mdi-calendar", // Nome do ícone Vuetify
  iconType: "vuetify"
}
```

## Exemplos de Uso

Veja as implementações nas views principais:

- `Agenda.vue` - Exemplo de sidenav para a agenda
- `Pacientes.vue` - Exemplo de sidenav para pacientes com modal
- `Dentistas.vue` - Exemplo de sidenav para dentistas com modais
- `Configuracoes.vue` - Exemplo de sidenav para configurações com estados ativos e atualização dinâmica

## Modo Compatível

O componente ainda suporta o modo antigo usando slots:

```vue
<lumi-sidenav icon="mdi-calendar-month" class="fixed-end lumi-sidenav">
  <sidenav-list-agenda></sidenav-list-agenda>
</lumi-sidenav>
```

Isso permite uma migração gradual para o novo sistema configurável.

## Otimizações de Layout

O LumiSidenav foi refinado para oferecer um layout mais inteligente e compacto:

### Ajustes de Largura
- **Largura principal**: Reduzida de 18rem para 16rem para melhor aproveitamento do espaço
- **Mantém robustez**: O design continua elegante e profissional

### Elementos Otimizados
- **Ícone do Header**: Reduzido de 55pt para 50pt com margens ajustadas
- **Logo do Footer**: Reduzido de 2.7rem para 2.4rem
- **Botões**: Altura reduzida de 48px para 46px, padding e margens otimizados
- **Espaçamento**: Margens entre ícones e texto refinadas para melhor compactação

### Otimizações para Telas Baixas
- **Telas baixas (≤690px)**: Logo do footer reduz para 2rem
- **Telas muito baixas (≤600px)**: Ícone do header reduz para 42pt, logo para 1.8rem

### Benefícios
- **Mais espaço para conteúdo**: A redução sutil da largura libera espaço valioso
- **Mantém elegância**: Todos os elementos continuam visualmente equilibrados
- **Melhor responsividade**: Ajustes automáticos para diferentes alturas de tela
- **Performance**: Transições suaves entre os diferentes tamanhos
