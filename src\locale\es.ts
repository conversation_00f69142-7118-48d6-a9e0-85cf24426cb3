export default {
    agenda: 'Agenda',
    badge: 'Insignia',
    open: '<PERSON><PERSON><PERSON>',
    close: '<PERSON><PERSON><PERSON>',
    dismiss: '<PERSON><PERSON><PERSON>',
    confirmEdit: {
        ok: 'OK',
        cancel: 'Cancelar',
    },
    dataIterator: {
        noResultsText: 'No se encontraron registros coincidentes',
        loadingText: 'Cargando elementos...',
    },
    dataTable: {
        itemsPerPageText: 'Filas por página:',
        ariaLabel: {
            sortDescending: 'Ordenado descendente.',
            sortAscending: 'Ordenado ascendente.',
            sortNone: 'Sin ordenar.',
            activateNone: 'Activar para quitar el ordenamiento.',
            activateDescending: 'Activar para ordenar descendente.',
            activateAscending: 'Activar para ordenar ascendente.',
        },
        sortBy: 'Ordenar por',
    },
    dataFooter: {
        itemsPerPageText: 'Elementos por página:',
        itemsPerPageAll: 'Todos',
        nextPage: 'Página siguiente',
        prevPage: 'Página anterior',
        firstPage: 'Primera página',
        lastPage: 'Última página',
        pageText: '{0}-{1} de {2}',
    },
    dateRangeInput: {
        divider: 'a',
    },
    datePicker: {
        itemsSelected: '{0} seleccionados',
        range: {
            title: 'Seleccionar fechas',
            header: 'Ingresar fechas',
        },
        title: 'Seleccionar fecha',
        header: 'Ingresar fecha',
        input: {
            placeholder: 'Ingresar fecha',
        },
    },
    noDataText: 'No hay datos disponibles',
    carousel: {
        prev: 'Visual anterior',
        next: 'Visual siguiente',
        ariaLabel: {
            delimiter: 'Diapositiva {0} de {1} del carrusel',
        },
    },
    calendar: {
        moreEvents: '{0} más',
        today: 'Hoy',
        day: 'Día',
        week: 'Semana',
        month: 'Mes',
        appointment: 'Cita{add}',
        view: 'Ver cita',
        report: 'Reprogramar'
    },
    input: {
        clear: 'Borrar {0}',
        prependAction: '{0} acción antepuesta',
        appendAction: '{0} acción añadida',
        otp: 'Por favor ingrese el carácter OTP {0}',
    },
    fileInput: {
        counter: '{0} archivos',
        counterSize: '{0} archivos ({1} en total)',
    },
    timePicker: {
        am: 'AM',
        pm: 'PM',
        title: 'Seleccionar hora',
    },
    pagination: {
        ariaLabel: {
            root: 'Navegación de paginación',
            next: 'Página siguiente',
            previous: 'Página anterior',
            page: 'Ir a la página {0}',
            currentPage: 'Página actual, página {0}',
            first: 'Primera página',
            last: 'Última página',
        },
    },
    stepper: {
        next: 'Siguiente',
        prev: 'Anterior',
    },
    rating: {
        ariaLabel: {
            item: 'Calificación {0} de {1}',
        },
    },
    loading: 'Cargando...',
    infiniteScroll: {
        loadMore: 'Cargar más',
        empty: 'No hay más',
    },

    // Custom:
    login: {
        submitAction: 'Entrar'
    },

    // Main Navigation
    mainNav: {
        agenda: 'Agenda',
        patients: 'Pacientes',
        orthodontists: 'Ortodoncistas',
        financial: 'Financiero',
        mentoring: 'Mentorías',
        settings: 'Configuraciones'
    },

    // Configuraciones de perfil
    profile: {
        title: 'Configuraciones de perfil',
        language: 'Idioma / Language',
        fullName: 'Nombre completo',
        clinic: 'Clínica',
        email: 'Correo electrónico',
        username: 'Nombre de usuario',
        newPassword: 'Nueva contraseña',
        confirmPassword: 'Confirmar contraseña',
        passwordHint: 'Complete solo si desea cambiar su contraseña',
        confirmPasswordHint: 'Confirme su nueva contraseña',
        saveButton: 'Guardar cambios',
        savingButton: 'Guardando...',
        loadingText: 'Cargando...'
    },
    pendingChanges: {
        title: 'Cambios pendientes',
        patientHasChanges: 'Este paciente tiene {count} {count, plural, one {cambio} other {cambios}} no {count, plural, one {guardado} other {guardados}}.',
        changesFound: 'Cambios encontrados:',
        lastModified: 'Última modificación:',
        understood: 'Entendido',
        categoryChanges: '{count} {count, plural, one {cambio} other {cambios}}',
        saveChanges: 'Guardar cambios',
        changesSaved: '¡Cambios guardados! Borrador guardado automáticamente',
        draftSavedAt: 'Borrador guardado a las {time}'
    }
}
